version: '3.8'

services:
  app:
    build:
      context: .
      dockerfile: Dockerfile
    ports:
      - "3000:3000"
      - "80:80"
    environment:
      - DATABASE_URL=****************************************************************
      - JWT_SECRET=magyar_nemet_nyelvtanulo_jwt_secret_2024
      - STRIPE_SECRET_KEY=${STRIPE_SECRET_KEY}
      - STRIPE_WEBHOOK_SECRET=we_1RFqa0PDb2KbDM6GzgR5O969
      - STRIPE_PRICE_ID=price_1RFbaGPDb2KbDM6G
      - STRIPE_PRODUCT_ID=prod_S9vUbzJakxCBx9
      - PORT=3000
      - NODE_ENV=production
      - FRONTEND_URL=https://digitalisnemet.hu
      - VITE_API_URL=https://app.digitalisnemet.hu/api
    volumes:
      - ./data:/app/data

volumes:
  data: