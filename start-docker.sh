#!/bin/sh

# Környezeti változók kiírása (debug)
echo "Környezeti változók:"
echo "DATABASE_URL: $DATABASE_URL"
echo "JWT_SECRET: $JWT_SECRET"
echo "STRIPE_SECRET_KEY: $STRIPE_SECRET_KEY"
echo "STRIPE_WEBHOOK_SECRET: $STRIPE_WEBHOOK_SECRET"
echo "STRIPE_PRICE_ID: $STRIPE_PRICE_ID"
echo "STRIPE_PRODUCT_ID: $STRIPE_PRODUCT_ID"

# Adatbázis könyvtár létrehozása és jogosultságok beállítása
mkdir -p /app/data
chmod -R 777 /app/data

# Backend indítása
cd /app/backend-sqlite

# Prisma generálás futtatása
echo "Prisma generálás futtatása..."
export PRISMA_CLI_QUERY_ENGINE_TYPE=binary
export PRISMA_CLIENT_ENGINE_TYPE=binary
export DATABASE_URL="file:/app/data/magyar_nemet_nyelvtanulo.db"
npx prisma generate

# Backend szerver indítása
echo "Backend szerver indítása..."
node src/server.js &

# Nginx indítása a frontend kiszolgálásához
echo "Nginx indítása..."
nginx -g "daemon off;" &

# Várakozás a folyamatokra
wait