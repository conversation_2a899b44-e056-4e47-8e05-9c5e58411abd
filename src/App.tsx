
import { Toaster } from "@/components/ui/toaster";
import { Toaster as Sonner } from "@/components/ui/sonner";
import { TooltipProvider } from "@/components/ui/tooltip";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { BrowserRouter, Routes, Route } from "react-router-dom";
import Navigation from "@/components/Navigation";
import FloatingChatButton from "@/components/FloatingChatButton";
import Home from "@/pages/Home";
import Vocabulary from "@/pages/Vocabulary";
import Phrases from "@/pages/Phrases";
import Quiz from "@/pages/Quiz";
import Games from "@/pages/Games";
import Assistant from "@/pages/Assistant";
import NotFound from "@/pages/NotFound";
import Login from "@/pages/Login";
import Register from "@/pages/Register";
import Profile from "@/pages/Profile";
import Subscription from "@/pages/Subscription";
import SubscriptionSuccess from "@/pages/SubscriptionSuccess";
import SubscriptionCancel from "@/pages/SubscriptionCancel";
import Invitations from "@/pages/Invitations";
import Points from "@/pages/Points";
import PointsSuccess from "@/pages/PointsSuccess";
import PointsCancel from "@/pages/PointsCancel";
import Support from "@/pages/Support";
import TicketDetail from "@/pages/TicketDetail";
import ForgotPassword from "@/pages/ForgotPassword";
import ResetPassword from "@/pages/ResetPassword";
import AdminUsers from "@/pages/AdminUsers";
import AdminUserDetail from "@/pages/AdminUserDetail";
import AdminStats from "@/pages/AdminStats";
import ButtonDemo from "@/pages/ButtonDemo";
import FAQ from "@/pages/FAQ";
import Privacy from "@/pages/Privacy";
import Terms from "@/pages/Terms";
import Cookies from "@/pages/Cookies";
import ProtectedRoute from "@/components/ProtectedRoute";
import { AuthProvider } from "@/contexts/AuthContext";
import { SpeechSettingsProvider } from "@/contexts/SpeechSettingsContext";

// Új oldalak importálása
import FreizeitGames from "@/pages/FreizeitGames";
import FreizeitVocabularyPage from "@/pages/FreizeitVocabulary";
import GrammarExercisesA1 from "@/pages/GrammarExercisesA1";
import VocationalRetail from "@/pages/VocationalRetail";
import VocationalRetailPhrases from "@/pages/VocationalRetailPhrases";
import VocationalRetailGames from "@/pages/VocationalRetailGames";
import PronunciationPractice from "@/pages/PronunciationPractice";
import TranslatorTool from "@/pages/TranslatorTool";
import TeacherSubscription from "@/pages/TeacherSubscription";
import TeacherSuccess from "@/pages/TeacherSuccess";
import TeacherDashboard from "@/pages/TeacherDashboard";
import Professional from "@/pages/Professional";

// Szakmai oldalak importálása

const queryClient = new QueryClient();

const App = () => (
  <QueryClientProvider client={queryClient}>
    <TooltipProvider>
      <Toaster />
      <Sonner />
      <BrowserRouter>
        <AuthProvider>
          <SpeechSettingsProvider>
            <Navigation />
            <FloatingChatButton />
            <div className="min-h-screen pt-16 md:pt-20">
              <Routes>
              {/* Nyilvános útvonalak */}
              <Route path="/" element={<Home />} />
              <Route path="/login" element={<Login />} />
              <Route path="/register" element={<Register />} />
              <Route path="/forgot-password" element={<ForgotPassword />} />
              <Route path="/reset-password/:token" element={<ResetPassword />} />
              <Route path="/ui/button-demo" element={<ButtonDemo />} />
              <Route path="/faq" element={<FAQ />} />
              <Route path="/privacy" element={<Privacy />} />
              <Route path="/terms" element={<Terms />} />
              <Route path="/cookies" element={<Cookies />} />

              {/* Védett útvonalak - minden csomag számára elérhetők */}
              <Route path="/vocabulary" element={
                <ProtectedRoute requireSubscription={false}>
                  <Vocabulary />
                </ProtectedRoute>
              } />
              <Route path="/phrases" element={
                <ProtectedRoute requireSubscription={false}>
                  <Phrases />
                </ProtectedRoute>
              } />
              <Route path="/quiz" element={
                <ProtectedRoute requireSubscription={false}>
                  <Quiz />
                </ProtectedRoute>
              } />
              <Route path="/games" element={
                <ProtectedRoute requireSubscription={false}>
                  <Games />
                </ProtectedRoute>
              } />
              <Route path="/assistant" element={
                <ProtectedRoute requireSubscription={false}>
                  <Assistant />
                </ProtectedRoute>
              } />
              <Route path="/translator" element={
                <ProtectedRoute requireSubscription={false}>
                  <TranslatorTool />
                </ProtectedRoute>
              } />
              <Route path="/professional" element={
                <ProtectedRoute requireSubscription={false}>
                  <Professional />
                </ProtectedRoute>
              } />

              {/* Új útvonalak a Freizeit témához */}
              <Route path="/games/freizeit" element={
                <ProtectedRoute requireSubscription={false}>
                  <FreizeitGames />
                </ProtectedRoute>
              } />
              <Route path="/vocabulary/freizeit" element={
                <ProtectedRoute requireSubscription={false}>
                  <FreizeitVocabularyPage />
                </ProtectedRoute>
              } />

              {/* Nyelvtani gyakorlatok */}
              <Route path="/grammar/a1" element={
                <ProtectedRoute requireSubscription={false}>
                  <GrammarExercisesA1 />
                </ProtectedRoute>
              } />

              {/* Kiejtésgyakorló */}
              <Route path="/pronunciation" element={
                <ProtectedRoute requireSubscription={false}>
                  <PronunciationPractice />
                </ProtectedRoute>
              } />

              {/* Szakmai témakörök útvonalai */}
              <Route path="/vocational/retail" element={
                <ProtectedRoute requireSubscription={false}>
                  <VocationalRetail />
                </ProtectedRoute>
              } />
              <Route path="/vocational/retail/phrases" element={
                <ProtectedRoute requireSubscription={false}>
                  <VocationalRetailPhrases />
                </ProtectedRoute>
              } />
              <Route path="/vocational/retail/games" element={
                <ProtectedRoute requireSubscription={false}>
                  <VocationalRetailGames />
                </ProtectedRoute>
              } />

              {/* Felhasználói fiókok kezelése */}
              <Route path="/profile" element={
                <ProtectedRoute requireSubscription={false}>
                  <Profile />
                </ProtectedRoute>
              } />
              <Route path="/subscription" element={
                <ProtectedRoute requireSubscription={false}>
                  <Subscription />
                </ProtectedRoute>
              } />
              <Route path="/subscription/success" element={<SubscriptionSuccess />} />
              <Route path="/subscription/cancel" element={<SubscriptionCancel />} />
              <Route path="/invitations" element={
                <ProtectedRoute requireSubscription={false}>
                  <Invitations />
                </ProtectedRoute>
              } />
              <Route path="/points" element={
                <ProtectedRoute requireSubscription={false}>
                  <Points />
                </ProtectedRoute>
              } />
              <Route path="/points/success" element={<PointsSuccess />} />
              <Route path="/points/cancel" element={<PointsCancel />} />

              {/* Tanári útvonalak */}
              <Route path="/teacher/subscribe" element={
                <ProtectedRoute requireSubscription={false}>
                  <TeacherSubscription />
                </ProtectedRoute>
              } />
              <Route path="/teacher/success" element={
                <ProtectedRoute requireSubscription={false}>
                  <TeacherSuccess />
                </ProtectedRoute>
              } />
              <Route path="/teacher/dashboard" element={
                <ProtectedRoute requireSubscription={false}>
                  <TeacherDashboard />
                </ProtectedRoute>
              } />

              {/* Support útvonalak */}
              <Route path="/support" element={
                <ProtectedRoute requireSubscription={false}>
                  <Support />
                </ProtectedRoute>
              } />
              <Route path="/support/:id" element={
                <ProtectedRoute requireSubscription={false}>
                  <TicketDetail />
                </ProtectedRoute>
              } />

              {/* Admin útvonalak */}
              <Route path="/admin/users" element={
                <ProtectedRoute requireSubscription={false}>
                  <AdminUsers />
                </ProtectedRoute>
              } />
              <Route path="/admin/users/:id" element={
                <ProtectedRoute requireSubscription={false}>
                  <AdminUserDetail />
                </ProtectedRoute>
              } />
              <Route path="/admin/stats" element={
                <ProtectedRoute requireSubscription={false}>
                  <AdminStats />
                </ProtectedRoute>
              } />

              {/* 404 oldal */}
              <Route path="*" element={<NotFound />} />
            </Routes>
            </div>
          </SpeechSettingsProvider>
        </AuthProvider>
      </BrowserRouter>
    </TooltipProvider>
  </QueryClientProvider>
);

export default App;