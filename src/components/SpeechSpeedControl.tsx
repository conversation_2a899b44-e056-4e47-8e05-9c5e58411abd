import React from "react";
import { Slider } from "@/components/ui/slider";
import { Label } from "@/components/ui/label";
import { Button } from "@/components/ui/button";
import { RotateCcw } from "lucide-react";

interface SpeechSpeedControlProps {
  speed: number;
  onChange: (value: number) => void;
}

/**
 * Beszédsebesség szabály<PERSON>ó komponen<PERSON>
 * Lehetővé teszi a felhasz<PERSON>ó s<PERSON> a beszédsebesség beállítását
 */
const SpeechSpeedControl: React.FC<SpeechSpeedControlProps> = ({ speed, onChange }) => {
  // Sebesség visszaállítása az alapértelmezett értékre (1.0)
  const resetSpeed = () => {
    onChange(1.0);
  };

  // Sebesség formázása a megjelenítéshez
  const formattedSpeed = speed.toFixed(2);

  return (
    <div className="flex flex-col space-y-2">
      <div className="flex items-center justify-between">
        <Label htmlFor="speech-speed" className="text-sm">
          Beszédsebesség: {formattedSpeed}x
        </Label>
        <Button
          variant="ghost"
          size="sm"
          className="h-7 px-2"
          onClick={resetSpeed}
          title="Alapértelmezett sebesség (1.0x)"
        >
          <RotateCcw className="h-3.5 w-3.5" />
        </Button>
      </div>
      <Slider
        id="speech-speed"
        min={0.25}
        max={2.0}
        step={0.05}
        value={[speed]}
        onValueChange={(values) => onChange(values[0])}
        className="w-full"
      />
      <div className="flex justify-between text-xs text-gray-500">
        <span>Lassú</span>
        <span>Normál</span>
        <span>Gyors</span>
      </div>
    </div>
  );
};

export default SpeechSpeedControl;
