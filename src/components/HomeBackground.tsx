import React from "react";

const HomeBackground: React.FC = () => {
  return (
    <div className="fixed inset-0 -z-10 overflow-hidden pointer-events-none">
      {/* Fő háttér gradiens */}
      <div className="absolute inset-0 bg-gradient-to-br from-white via-language-background to-language-light opacity-80"></div>
      
      {/* Absztrakt formák és minták */}
      <svg
        className="absolute inset-0 w-full h-full opacity-[0.07]"
        xmlns="http://www.w3.org/2000/svg"
        viewBox="0 0 1920 1080"
        preserveAspectRatio="xMidYMid slice"
      >
        {/* Német zászló színeit idéző elemek */}
        <path d="M0,300 Q480,150 960,300 T1920,300 V0 H0 Z" fill="#FFD700" opacity="0.3" /> {/* Arany */}
        <path d="M0,600 Q480,450 960,600 T1920,600 V300 H0 Z" fill="#DD0000" opacity="0.2" /> {/* Piros */}
        <path d="M0,900 Q480,750 960,900 T1920,900 V600 H0 Z" fill="#000000" opacity="0.1" /> {/* Fekete */}
        
        {/* Magyar zászló színeit idéző elemek */}
        <circle cx="200" cy="200" r="100" fill="#CE2939" opacity="0.15" /> {/* Piros */}
        <circle cx="1720" cy="880" r="150" fill="#008751" opacity="0.15" /> {/* Zöld */}
        
        {/* Nyelvtanulási elemek - betűk és szavak */}
        <g opacity="0.2" fill="#3b82f6">
          <text x="150" y="400" fontSize="40" fontFamily="Arial" fontWeight="bold">A B C</text>
          <text x="1600" y="300" fontSize="40" fontFamily="Arial" fontWeight="bold">Ä Ö Ü</text>
          <text x="800" y="200" fontSize="30" fontFamily="Arial" fontWeight="bold">Deutsch</text>
          <text x="1200" y="500" fontSize="30" fontFamily="Arial" fontWeight="bold">Magyar</text>
        </g>
        
        {/* Dekoratív vonalak és formák */}
        <path d="M0,100 C300,200 600,50 900,100 S1200,200 1500,150 S1800,50 1920,100" stroke="#10b981" strokeWidth="2" fill="none" opacity="0.2" />
        <path d="M0,800 C300,700 600,850 900,800 S1200,700 1500,750 S1800,850 1920,800" stroke="#8b5cf6" strokeWidth="2" fill="none" opacity="0.2" />
        
        {/* Absztrakt geometriai formák */}
        <rect x="100" y="600" width="200" height="200" rx="30" fill="#3b82f6" opacity="0.1" />
        <rect x="1600" y="200" width="200" height="200" rx="30" fill="#10b981" opacity="0.1" />
        <rect x="800" y="700" width="300" height="300" rx="150" fill="#8b5cf6" opacity="0.1" />
        
        {/* Hullámok és görbék */}
        <path d="M-100,1080 C400,900 800,1000 1200,900 S1600,800 2000,900" stroke="#ec4899" strokeWidth="3" fill="none" opacity="0.15" />
        <path d="M-100,980 C400,800 800,900 1200,800 S1600,700 2000,800" stroke="#f59e0b" strokeWidth="3" fill="none" opacity="0.15" />
      </svg>
      
      {/* Lebegő pontok és részecskék */}
      <div className="absolute inset-0">
        <div className="absolute top-1/4 left-1/4 w-4 h-4 rounded-full bg-language-primary opacity-20 animate-pulse"></div>
        <div className="absolute top-3/4 left-1/3 w-6 h-6 rounded-full bg-language-secondary opacity-20 animate-pulse" style={{ animationDelay: "1s" }}></div>
        <div className="absolute top-1/2 left-2/3 w-5 h-5 rounded-full bg-language-accent opacity-20 animate-pulse" style={{ animationDelay: "2s" }}></div>
        <div className="absolute top-1/3 left-3/4 w-3 h-3 rounded-full bg-language-quaternary opacity-20 animate-pulse" style={{ animationDelay: "1.5s" }}></div>
      </div>
      
      {/* Fényes foltok */}
      <div className="absolute top-0 left-0 w-full h-full">
        <div className="absolute top-0 left-1/4 w-[500px] h-[500px] bg-language-primary opacity-[0.03] rounded-full blur-[100px]"></div>
        <div className="absolute bottom-0 right-1/4 w-[600px] h-[600px] bg-language-accent opacity-[0.03] rounded-full blur-[120px]"></div>
      </div>
    </div>
  );
};

export default HomeBackground;
