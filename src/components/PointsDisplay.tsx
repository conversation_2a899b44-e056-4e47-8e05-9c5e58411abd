import React, { useEffect } from "react";
import { useAuth } from "@/contexts/AuthContext";
import { Button } from "@/components/ui/button";
import { Coins } from "lucide-react";
import { Link } from "react-router-dom";
import { cn } from "@/lib/utils";

interface PointsDisplayProps {
  className?: string;
  variant?: "default" | "compact";
}

const PointsDisplay = ({ className, variant = "default" }: PointsDisplayProps) => {
  const { isAuthenticated, userPoints, checkUserPoints } = useAuth();

  // Pontok frissítése a komponens betöltésekor, de csak egyszer
  useEffect(() => {
    // Csak egyszer frissítjük a pontokat, amikor a komponens betöltődik
    // és a felhasználó be van jelentkezve
    let isMounted = true;

    if (isAuthenticated && isMounted) {
      // Csak akkor frissítjük, ha a komponens még mindig a DOM-ban van
      const loadPoints = async () => {
        await checkUserPoints();
      };

      // Csak egyszer hívjuk meg
      loadPoints();
    }

    // Cleanup függvény, amely jelzi, ha a komponens eltávolításra kerül
    return () => {
      isMounted = false;
    };
  }, [isAuthenticated]); // checkUserPoints eltávolítva a függőségekből

  if (!isAuthenticated) {
    return null;
  }

  if (variant === "compact") {
    return (
      <Link to="/points">
        <Button
          variant="outline"
          size="sm"
          className={cn(
            "flex items-center gap-1.5 rounded-full border-language-border bg-white/80 hover:bg-white",
            className
          )}
        >
          <Coins className="h-3.5 w-3.5 text-language-primary" />
          <span className="font-medium">{userPoints}</span>
        </Button>
      </Link>
    );
  }

  return (
    <Link to="/points">
      <Button
        variant="outline"
        className={cn(
          "flex items-center gap-2 rounded-full border-language-border bg-white/80 hover:bg-white",
          className
        )}
      >
        <div className="flex items-center gap-1.5">
          <Coins className="h-4 w-4 text-language-primary" />
          <span className="font-medium">{userPoints}</span>
        </div>
        <span className="text-sm text-muted-foreground">pont</span>
      </Button>
    </Link>
  );
};

export default PointsDisplay;
