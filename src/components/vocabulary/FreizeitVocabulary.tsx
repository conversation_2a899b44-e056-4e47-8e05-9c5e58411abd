import React, { useState } from 'react';
import { <PERSON>, CardContent, CardDescription, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>eader, CardTitle } from "../ui/card";
import { Ta<PERSON>, Ta<PERSON>Content, <PERSON><PERSON>List, TabsTrigger } from "../ui/tabs";
import { <PERSON><PERSON> } from "../ui/button";
import { Badge } from "../ui/badge";
import { Switch } from "../ui/switch";
import { Label } from "../ui/label";
import { Separator } from "../ui/separator";
import { freizeitVocabulary, VocabularyItem } from "../../data/vocabulary/freizeit";

const FreizeitVocabulary: React.FC = () => {
  const [activeTab, setActiveTab] = useState<string>(freizeitVocabulary[0].id);
  const [showGerman, setShowGerman] = useState<boolean>(true);
  const [showHungarian, setShowHungarian] = useState<boolean>(true);
  const [showExample<PERSON>, setShowExamples] = useState<boolean>(true);
  const [showNotes, setShowNotes] = useState<boolean>(true);
  const [flashcardMode, setFlashcardMode] = useState<boolean>(false);
  const [currentFlashcard, setCurrentFlashcard] = useState<VocabularyItem | null>(null);
  const [showFlashcardAnswer, setShowFlashcardAnswer] = useState<boolean>(false);
  const [flashcardDirection, setFlashcardDirection] = useState<'de-hu' | 'hu-de'>('de-hu');
  
  // Flashcard kezelés
  const startFlashcardMode = () => {
    const activeCategory = freizeitVocabulary.find(cat => cat.id === activeTab);
    if (!activeCategory || activeCategory.items.length === 0) return;
    
    // Véletlenszerűen választunk egy elemet
    const randomIndex = Math.floor(Math.random() * activeCategory.items.length);
    setCurrentFlashcard(activeCategory.items[randomIndex]);
    setShowFlashcardAnswer(false);
    setFlashcardMode(true);
  };
  
  const nextFlashcard = () => {
    const activeCategory = freizeitVocabulary.find(cat => cat.id === activeTab);
    if (!activeCategory || activeCategory.items.length === 0) return;
    
    // Véletlenszerűen választunk egy elemet, de ne legyen ugyanaz
    let randomIndex;
    do {
      randomIndex = Math.floor(Math.random() * activeCategory.items.length);
    } while (
      activeCategory.items.length > 1 && 
      currentFlashcard && 
      activeCategory.items[randomIndex].german === currentFlashcard.german
    );
    
    setCurrentFlashcard(activeCategory.items[randomIndex]);
    setShowFlashcardAnswer(false);
  };
  
  const exitFlashcardMode = () => {
    setFlashcardMode(false);
    setCurrentFlashcard(null);
  };
  
  return (
    <Card className="w-full max-w-4xl mx-auto">
      <CardHeader>
        <CardTitle>Szabadidő szókincsbővítő</CardTitle>
        <CardDescription>
          Tanulj új szavakat és kifejezéseket a szabadidő, hobbik és napi rutinok témaköréhez!
        </CardDescription>
      </CardHeader>
      
      <CardContent>
        {flashcardMode && currentFlashcard ? (
          // Flashcard mód
          <div className="space-y-6">
            <div className="flex justify-between items-center">
              <Button variant="outline" onClick={exitFlashcardMode}>
                Vissza a szószedethez
              </Button>
              
              <div className="flex items-center gap-2">
                <Label>Irány:</Label>
                <Button 
                  variant={flashcardDirection === 'de-hu' ? 'default' : 'outline'}
                  onClick={() => setFlashcardDirection('de-hu')}
                  className="w-28"
                >
                  DE → HU
                </Button>
                <Button 
                  variant={flashcardDirection === 'hu-de' ? 'default' : 'outline'}
                  onClick={() => setFlashcardDirection('hu-de')}
                  className="w-28"
                >
                  HU → DE
                </Button>
              </div>
            </div>
            
            <Card className="flex flex-col items-center justify-center min-h-64 p-6">
              <div className="text-xl font-medium mb-6">
                {flashcardDirection === 'de-hu' ? currentFlashcard.german : currentFlashcard.hungarian}
              </div>
              
              {showFlashcardAnswer ? (
                <div className="space-y-4 w-full">
                  <div className="text-center">
                    <Badge className="text-lg px-3 py-1">
                      {flashcardDirection === 'de-hu' ? currentFlashcard.hungarian : currentFlashcard.german}
                    </Badge>
                  </div>
                  
                  {currentFlashcard.example && (
                    <div className="text-sm italic text-center mt-4">
                      {currentFlashcard.example}
                    </div>
                  )}
                  
                  {currentFlashcard.notes && (
                    <div className="text-xs text-gray-500 text-center mt-2">
                      {currentFlashcard.notes}
                    </div>
                  )}
                </div>
              ) : (
                <Button onClick={() => setShowFlashcardAnswer(true)}>
                  Mutasd a választ
                </Button>
              )}
            </Card>
            
            {showFlashcardAnswer && (
              <div className="flex justify-center">
                <Button onClick={nextFlashcard}>
                  Következő szó
                </Button>
              </div>
            )}
          </div>
        ) : (
          // Normál szószedet mód
          <div className="space-y-6">
            <div className="flex justify-between items-center">
              <Button onClick={startFlashcardMode}>
                Váltás kártyák módra
              </Button>
              
              <div className="flex items-center space-x-4">
                <div className="flex items-center space-x-2">
                  <Switch 
                    id="show-german" 
                    checked={showGerman} 
                    onCheckedChange={setShowGerman}
                  />
                  <Label htmlFor="show-german">Német</Label>
                </div>
                
                <div className="flex items-center space-x-2">
                  <Switch 
                    id="show-hungarian" 
                    checked={showHungarian} 
                    onCheckedChange={setShowHungarian}
                  />
                  <Label htmlFor="show-hungarian">Magyar</Label>
                </div>
                
                <div className="flex items-center space-x-2">
                  <Switch 
                    id="show-examples" 
                    checked={showExamples} 
                    onCheckedChange={setShowExamples}
                  />
                  <Label htmlFor="show-examples">Példamondatok</Label>
                </div>
                
                <div className="flex items-center space-x-2">
                  <Switch 
                    id="show-notes" 
                    checked={showNotes} 
                    onCheckedChange={setShowNotes}
                  />
                  <Label htmlFor="show-notes">Megjegyzések</Label>
                </div>
              </div>
            </div>
            
            <Tabs defaultValue={activeTab} value={activeTab} onValueChange={setActiveTab}>
              <TabsList className="grid grid-cols-3 md:grid-cols-5">
                {freizeitVocabulary.map(category => (
                  <TabsTrigger key={category.id} value={category.id}>
                    {category.title}
                  </TabsTrigger>
                ))}
              </TabsList>
              
              {freizeitVocabulary.map(category => (
                <TabsContent key={category.id} value={category.id} className="space-y-4">
                  <div className="grid grid-cols-1 gap-2">
                    {category.items.map((item, index) => (
                      <div 
                        key={`${category.id}-${index}`} 
                        className="p-3 border rounded-lg hover:bg-gray-50 transition-colors"
                      >
                        <div className="flex flex-wrap justify-between gap-y-2">
                          {showGerman && (
                            <div className="font-medium">
                              {item.german}
                            </div>
                          )}
                          
                          {showHungarian && (
                            <div className="text-gray-700">
                              {item.hungarian}
                            </div>
                          )}
                        </div>
                        
                        {showExamples && item.example && (
                          <div className="mt-1 text-sm italic text-gray-600">
                            {item.example}
                          </div>
                        )}
                        
                        {showNotes && item.notes && (
                          <div className="mt-1 text-xs text-gray-500">
                            {item.notes}
                          </div>
                        )}
                      </div>
                    ))}
                  </div>
                </TabsContent>
              ))}
            </Tabs>
          </div>
        )}
      </CardContent>
      
      <CardFooter>
        <p className="text-sm text-gray-500">
          A szókincsbővítő segít a "Freizeit" témakör szavainak és kifejezéseinek elsajátításában, használd rendszeresen a fejlődésért!
        </p>
      </CardFooter>
    </Card>
  );
};

export default FreizeitVocabulary;