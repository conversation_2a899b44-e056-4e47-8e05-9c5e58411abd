
import React, { useState, useEffect } from "react";
import { Link, useLocation } from "react-router-dom";
import { cn } from "@/lib/utils";
import { useAuth } from "@/contexts/AuthContext";
import {
  Book,
  MessageSquare,
  GraduationCap,
  Home,
  Gamepad2,
  ChevronDown,
  ChevronUp,
  ChevronRight,
  BookOpen,
  Brain,
  Menu,
  X,
  Bot,
  Globe,
  Sparkles,
  Lightbulb,
  Palette,
  User,
  LogIn,
  LogOut,
  UserCircle,
  CreditCard,
  UserPlus,
  Users,
  Coins,
  HelpCircle,
  Briefcase,
  ShoppingBag,
  Mic,
  Stethoscope,
  Utensils,
  Factory,
  HardHat,
  Truck,
  Heart,
  Languages
} from "lucide-react";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
  DropdownMenuSeparator,
} from "@/components/ui/dropdown-menu";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { motion, AnimatePresence } from "framer-motion";
import PointsDisplay from "./PointsDisplay";

const Navigation = () => {
  const location = useLocation();
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);
  const { user, isAuthenticated, hasSubscription, isTeacher, logout } = useAuth();

  // Scroll kezelés
  const [scrolled, setScrolled] = useState(false);

  useEffect(() => {
    const handleScroll = () => {
      setScrolled(window.scrollY > 10);
    };

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  // Kategorizált navigációs elemek és színek
  const navCategories = [
    {
      id: "home",
      color: "language-primary",
      items: [
        { path: "/", label: "Főoldal", icon: <Home className="w-5 h-5" />, description: "Kezdőlap" },
      ]
    },
    {
      id: "learning",
      label: "Tanulás",
      color: "language-secondary",
      icon: <Globe className="w-5 h-5" />,
      items: [
        { path: "/vocabulary", label: "Szókincs", icon: <Book className="w-5 h-5" />, description: "Német szavak és kifejezések" },
        { path: "/phrases", label: "Mondatok", icon: <MessageSquare className="w-5 h-5" />, description: "Hasznos mondatok és párbeszédek" },
      ]
    },
    {
      id: "practice",
      label: "Gyakorlás",
      color: "language-accent",
      icon: <Sparkles className="w-5 h-5" />,
      items: [
        { path: "/quiz", label: "Kvíz", icon: <GraduationCap className="w-5 h-5" />, description: "Teszteld a tudásod" },
        { path: "/games", label: "Játékok", icon: <Gamepad2 className="w-5 h-5" />, description: "Tanulj játékosan" },
        {
          path: "/pronunciation",
          label: "Kiejtésgyakorló",
          icon: <Mic className="w-5 h-5" />,
          description: "Gyakorold a kiejtést",
          badge: { text: "Új", variant: "teal" }
        },
      ]
    },
    {
      id: "vocational",
      label: "Szakmai",
      color: "language-quaternary",
      icon: <Briefcase className="w-5 h-5" />,
      items: [

        { path: "/vocational/retail", label: "Bolti eladó", icon: <ShoppingBag className="w-5 h-5" />, description: "Bolti eladó szakmai szókincs" },
        { path: "/vocational/healthcare", label: "Egészségügy", icon: <Stethoscope className="w-5 h-5" />, description: "Egészségügyi szakmai szókincs" },
        { path: "/vocational/hospitality", label: "Vendéglátás", icon: <Utensils className="w-5 h-5" />, description: "Vendéglátóipari szakmai szókincs" },
        { path: "/vocational/manufacturing", label: "Gyártás", icon: <Factory className="w-5 h-5" />, description: "Gyári munkás szakmai szókincs" },
        { path: "/vocational/construction", label: "Építőipar", icon: <HardHat className="w-5 h-5" />, description: "Építőipari szakmai szókincs" },
        { path: "/vocational/logistics", label: "Logisztika", icon: <Truck className="w-5 h-5" />, description: "Logisztikai szakmai szókincs" },
        { path: "/vocational/eldercare", label: "Idősgondozás", icon: <Heart className="w-5 h-5" />, description: "Idősgondozói szakmai szókincs" },
      ]
    },
    {
      id: "tools",
      label: "Eszközök",
      color: "language-tertiary",
      icon: <Lightbulb className="w-5 h-5" />,
      items: [
        { path: "/assistant", label: "Asszisztens", icon: <Bot className="w-5 h-5" />, description: "AI nyelvtanulási segéd" },
        { path: "/translator", label: "Fordító", icon: <Languages className="w-5 h-5" />, description: "Szöveg, hang és kép fordítása", badge: { text: "Új", variant: "teal" } },
        { path: "/faq", label: "GYIK", icon: <HelpCircle className="w-5 h-5" />, description: "Gyakran Ismételt Kérdések" },
      ]
    }
  ];

  // Navigációs elem típus definíciója
  type NavItem = {
    path: string;
    label: string;
    icon: React.ReactNode;
    description: string;
    badge?: {
      text: string;
      variant: 'primary' | 'secondary' | 'accent' | 'tertiary' | 'quaternary' | 'teal' | 'success' | 'warning';
    };
  };

  // Kategória típus definíciója
  type NavCategory = {
    id: string;
    label?: string;
    color: string;
    icon?: React.ReactNode;
    items: NavItem[];
  };

  // Aktív kategória ellenőrzése
  const isActiveCategory = (category: NavCategory) => {
    return category.items.some((item) => location.pathname === item.path);
  };

  // Mobil menü toggle
  const toggleMobileMenu = () => {
    setMobileMenuOpen(!mobileMenuOpen);
  };

  return (
    <motion.nav
      className={cn(
        "w-full fixed top-0 left-0 z-50 transition-all duration-300",
        scrolled
          ? "bg-white/90 backdrop-blur-md shadow-md py-2 px-4 border-b border-language-border"
          : "bg-gradient-to-r from-language-light to-white py-3 px-4"
      )}
      initial={{ y: -100 }}
      animate={{ y: 0 }}
      transition={{ type: "spring", stiffness: 300, damping: 30 }}
    >
      <div className="container mx-auto">
        {/* Desktop navigáció */}
        <div className="hidden md:flex justify-between items-center">
          <Link
            to="/"
            className="text-xl font-bold flex items-center gap-2"
          >
            <div className="relative">
              <motion.div
                className="absolute -inset-1 rounded-full bg-gradient-to-r from-language-primary to-language-accent opacity-70 blur-sm"
                animate={{
                  scale: [1, 1.05, 1],
                  opacity: [0.7, 0.8, 0.7]
                }}
                transition={{
                  duration: 3,
                  repeat: Infinity,
                  repeatType: "reverse"
                }}
              />
              <div className="relative bg-white rounded-full p-1">
                <BookOpen className="w-6 h-6 text-language-primary" />
              </div>
            </div>
            <div className="flex flex-col">
              <span className="bg-gradient-to-r from-language-primary to-language-accent bg-clip-text text-transparent leading-tight">Magyar-Német</span>
              <span className="text-xs text-gray-500 font-normal">Nyelvtanuló</span>
            </div>
          </Link>

          <div className="flex items-center gap-1">
            {navCategories.map((category) => {
              // Egyszerű link a főoldalhoz
              if (category.id === "home") {
                const item = category.items[0];
                return (
                  <Link
                    key={item.path}
                    to={item.path}
                    className={cn(
                      "group flex items-center gap-2 px-3 py-2 rounded-full transition-all duration-200",
                      location.pathname === item.path
                        ? `text-language-primary font-medium bg-language-primary/10 shadow-sm`
                        : `text-gray-600 hover:text-language-primary hover:bg-language-hover`
                    )}
                  >
                    <div className={cn(
                      "p-1.5 rounded-md transition-all duration-200",
                      location.pathname === item.path
                        ? "bg-language-primary/30 text-language-primary"
                        : "text-gray-600 group-hover:text-language-primary"
                    )}>
                      {item.icon}
                    </div>
                    <span>{item.label}</span>
                  </Link>
                );
              }

              // Dropdown menü a kategóriákhoz
              // Special grid layout for Szakmai category
              if (category.id === "vocational") {
                return (
                  <DropdownMenu key={category.id}>
                    <DropdownMenuTrigger asChild>
                      <Button
                        variant="ghost"
                        className={cn(
                          "group flex items-center gap-2 px-3 py-2 rounded-full transition-all duration-200",
                          isActiveCategory(category) && "text-language-quaternary font-medium bg-language-quaternary/10",
                          !isActiveCategory(category) && "text-gray-600 hover:bg-language-hover hover:text-language-quaternary"
                        )}
                      >
                        <div className={cn(
                          "transition-colors duration-200",
                          isActiveCategory(category) && "text-language-quaternary",
                          !isActiveCategory(category) && "text-gray-600 group-hover:text-language-quaternary"
                        )}>
                          {category.icon}
                        </div>
                        <span>{category.label}</span>
                        <ChevronDown className={cn(
                          "w-4 h-4 transition-colors duration-200",
                          isActiveCategory(category) && "text-language-quaternary",
                          !isActiveCategory(category) && "text-gray-400 group-hover:text-language-quaternary"
                        )} />
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent
                      align="end"
                      className="p-4 border border-language-border shadow-xl rounded-xl bg-white/95 backdrop-blur-sm w-[500px] overflow-hidden"
                      sideOffset={8}
                    >
                      {/* Háttér dekoráció elemek */}
                      <div className="absolute -top-24 -right-24 w-64 h-64 bg-language-quaternary/5 rounded-full blur-3xl"></div>
                      <div className="absolute -bottom-32 -left-32 w-80 h-80 bg-language-quaternary/5 rounded-full blur-3xl"></div>

                      <div className="relative">
                        <div className="mb-3 flex items-center justify-between">
                          <div>
                            <div className="flex items-center gap-2 text-language-quaternary mb-1">
                              <div className="p-2 rounded-md bg-language-quaternary/10 text-language-quaternary">
                                {category.icon}
                              </div>
                              <span className="text-lg font-medium">Szakmai területek</span>
                            </div>
                            <div className="text-sm text-gray-500">Válassz egy szakterületet a nyelvtanuláshoz</div>
                          </div>


                        </div>

                        <DropdownMenuSeparator className="mb-3" />

                        {/* Kategória csoportosítás */}
                        <div className="grid grid-cols-1 gap-4">
                          {/* Kereskedelmi kategória */}
                          <div>
                            <div className="mb-1 ml-1">
                              <span className="text-xs font-medium text-gray-500 uppercase tracking-wider">Kereskedelem & Szolgáltatás</span>
                            </div>
                            <div className="grid grid-cols-2 gap-3">
                              {category.items
                                .filter(item => ['retail', 'hospitality'].some(key => item.path.includes(key)))
                                .map((item) => (
                                  <DropdownMenuItem key={item.path} asChild className="p-0 focus:bg-transparent">
                                    <Link
                                      to={item.path}
                                      className={cn(
                                        "group flex items-center gap-2 w-full px-3 py-2.5 rounded-xl border transition-all duration-200",
                                        location.pathname === item.path
                                          ? "bg-language-quaternary/10 text-language-quaternary border-language-quaternary/30"
                                          : "text-gray-700 hover:bg-language-quaternary/5 hover:text-language-quaternary border-gray-100 hover:border-language-quaternary/20"
                                      )}
                                    >
                                      <div className={cn(
                                        "p-2 rounded-lg transition-all duration-200 text-white",
                                        location.pathname === item.path
                                          ? "bg-language-quaternary shadow-md shadow-language-quaternary/20"
                                          : "bg-language-quaternary/80 group-hover:bg-language-quaternary shadow-sm group-hover:shadow-md group-hover:shadow-language-quaternary/20"
                                      )}>
                                        {item.icon}
                                      </div>
                                      <div className="flex flex-col">
                                        <div className="flex items-center gap-1">
                                          <span className="font-medium text-sm">{item.label}</span>
                                          {item.badge && (
                                            <Badge className="text-[10px] py-0 px-1 bg-teal-100 text-teal-700">
                                              {item.badge.text}
                                            </Badge>
                                          )}
                                        </div>
                                        <span className="text-[10px] text-gray-500">{item.description}</span>
                                      </div>
                                    </Link>
                                  </DropdownMenuItem>
                                ))
                              }
                            </div>
                          </div>

                          {/* Ipar & Logisztika kategória */}
                          <div>
                            <div className="mb-1 ml-1">
                              <span className="text-xs font-medium text-gray-500 uppercase tracking-wider">Ipar & Logisztika</span>
                            </div>
                            <div className="grid grid-cols-2 gap-3">
                              {category.items
                                .filter(item => ['manufacturing', 'construction', 'logistics'].some(key => item.path.includes(key)))
                                .map((item) => (
                                  <DropdownMenuItem key={item.path} asChild className="p-0 focus:bg-transparent">
                                    <Link
                                      to={item.path}
                                      className={cn(
                                        "group flex items-center gap-2 w-full px-3 py-2.5 rounded-xl border transition-all duration-200",
                                        location.pathname === item.path
                                          ? "bg-language-quaternary/10 text-language-quaternary border-language-quaternary/30"
                                          : "text-gray-700 hover:bg-language-quaternary/5 hover:text-language-quaternary border-gray-100 hover:border-language-quaternary/20"
                                      )}
                                    >
                                      <div className={cn(
                                        "p-2 rounded-lg transition-all duration-200 text-white",
                                        location.pathname === item.path
                                          ? "bg-language-quaternary shadow-md shadow-language-quaternary/20"
                                          : "bg-language-quaternary/80 group-hover:bg-language-quaternary shadow-sm group-hover:shadow-md group-hover:shadow-language-quaternary/20"
                                      )}>
                                        {item.icon}
                                      </div>
                                      <div className="flex flex-col">
                                        <div className="flex items-center gap-1">
                                          <span className="font-medium text-sm">{item.label}</span>
                                          {item.badge && (
                                            <Badge className="text-[10px] py-0 px-1 bg-teal-100 text-teal-700">
                                              {item.badge.text}
                                            </Badge>
                                          )}
                                        </div>
                                        <span className="text-[10px] text-gray-500">{item.description}</span>
                                      </div>
                                    </Link>
                                  </DropdownMenuItem>
                                ))
                              }
                            </div>
                          </div>

                          {/* Egészségügy & Gondozás kategória */}
                          <div>
                            <div className="mb-1 ml-1">
                              <span className="text-xs font-medium text-gray-500 uppercase tracking-wider">Egészségügy & Gondozás</span>
                            </div>
                            <div className="grid grid-cols-2 gap-3">
                              {category.items
                                .filter(item => ['healthcare', 'eldercare'].some(key => item.path.includes(key)))
                                .map((item) => (
                                  <DropdownMenuItem key={item.path} asChild className="p-0 focus:bg-transparent">
                                    <Link
                                      to={item.path}
                                      className={cn(
                                        "group flex items-center gap-2 w-full px-3 py-2.5 rounded-xl border transition-all duration-200",
                                        location.pathname === item.path
                                          ? "bg-language-quaternary/10 text-language-quaternary border-language-quaternary/30"
                                          : "text-gray-700 hover:bg-language-quaternary/5 hover:text-language-quaternary border-gray-100 hover:border-language-quaternary/20"
                                      )}
                                    >
                                      <div className={cn(
                                        "p-2 rounded-lg transition-all duration-200 text-white",
                                        location.pathname === item.path
                                          ? "bg-language-quaternary shadow-md shadow-language-quaternary/20"
                                          : "bg-language-quaternary/80 group-hover:bg-language-quaternary shadow-sm group-hover:shadow-md group-hover:shadow-language-quaternary/20"
                                      )}>
                                        {item.icon}
                                      </div>
                                      <div className="flex flex-col">
                                        <div className="flex items-center gap-1">
                                          <span className="font-medium text-sm">{item.label}</span>
                                          {item.badge && (
                                            <Badge className="text-[10px] py-0 px-1 bg-teal-100 text-teal-700">
                                              {item.badge.text}
                                            </Badge>
                                          )}
                                        </div>
                                        <span className="text-[10px] text-gray-500">{item.description}</span>
                                      </div>
                                    </Link>
                                  </DropdownMenuItem>
                                ))
                              }
                            </div>
                          </div>


                        </div>

                        <div className="mt-3 pt-3 border-t border-gray-100">
                          <div className="flex justify-between items-center">
                            <span className="text-xs text-gray-500">Nem találod a számodra megfelelő szakmai területet?</span>
                            <Link
                              to="/support"
                              className="text-xs text-language-quaternary hover:underline flex items-center gap-1"
                            >
                              <span>Kapcsolat</span>
                              <ChevronRight className="h-3 w-3" />
                            </Link>
                          </div>
                        </div>
                      </div>
                    </DropdownMenuContent>
                  </DropdownMenu>
                );
              }

              // Default dropdown for other categories
              return (
                <DropdownMenu key={category.id}>
                  <DropdownMenuTrigger asChild>
                    <Button
                      variant="ghost"
                      className={cn(
                        "group flex items-center gap-2 px-3 py-2 rounded-full transition-all duration-200",
                        isActiveCategory(category) && category.color === "language-primary" && "text-language-primary font-medium bg-language-primary/10",
                        isActiveCategory(category) && category.color === "language-secondary" && "text-language-secondary font-medium bg-language-secondary/10",
                        isActiveCategory(category) && category.color === "language-accent" && "text-language-accent font-medium bg-language-accent/10",
                        isActiveCategory(category) && category.color === "language-tertiary" && "text-language-tertiary font-medium bg-language-tertiary/10",
                        isActiveCategory(category) && category.color === "language-quaternary" && "text-language-quaternary font-medium bg-language-quaternary/10",
                        !isActiveCategory(category) && "text-gray-600 hover:bg-language-hover",
                        !isActiveCategory(category) && category.color === "language-primary" && "hover:text-language-primary",
                        !isActiveCategory(category) && category.color === "language-secondary" && "hover:text-language-secondary",
                        !isActiveCategory(category) && category.color === "language-accent" && "hover:text-language-accent",
                        !isActiveCategory(category) && category.color === "language-tertiary" && "hover:text-language-tertiary",
                        !isActiveCategory(category) && category.color === "language-quaternary" && "hover:text-language-quaternary"
                      )}
                    >
                      <div className={cn(
                        "transition-colors duration-200",
                        isActiveCategory(category) && category.color === "language-primary" && "text-language-primary",
                        isActiveCategory(category) && category.color === "language-secondary" && "text-language-secondary",
                        isActiveCategory(category) && category.color === "language-accent" && "text-language-accent",
                        isActiveCategory(category) && category.color === "language-tertiary" && "text-language-tertiary",
                        isActiveCategory(category) && category.color === "language-quaternary" && "text-language-quaternary",
                        !isActiveCategory(category) && "text-gray-600",
                        !isActiveCategory(category) && category.color === "language-primary" && "group-hover:text-language-primary",
                        !isActiveCategory(category) && category.color === "language-secondary" && "group-hover:text-language-secondary",
                        !isActiveCategory(category) && category.color === "language-accent" && "group-hover:text-language-accent",
                        !isActiveCategory(category) && category.color === "language-tertiary" && "group-hover:text-language-tertiary",
                        !isActiveCategory(category) && category.color === "language-quaternary" && "group-hover:text-language-quaternary"
                      )}>
                        {category.icon}
                      </div>
                      <span>{category.label}</span>
                      <ChevronDown className={cn(
                        "w-4 h-4 transition-colors duration-200",
                        isActiveCategory(category) && category.color === "language-primary" && "text-language-primary",
                        isActiveCategory(category) && category.color === "language-secondary" && "text-language-secondary",
                        isActiveCategory(category) && category.color === "language-accent" && "text-language-accent",
                        isActiveCategory(category) && category.color === "language-tertiary" && "text-language-tertiary",
                        isActiveCategory(category) && category.color === "language-quaternary" && "text-language-quaternary",
                        !isActiveCategory(category) && "text-gray-400",
                        !isActiveCategory(category) && category.color === "language-primary" && "group-hover:text-language-primary",
                        !isActiveCategory(category) && category.color === "language-secondary" && "group-hover:text-language-secondary",
                        !isActiveCategory(category) && category.color === "language-accent" && "group-hover:text-language-accent",
                        !isActiveCategory(category) && category.color === "language-tertiary" && "group-hover:text-language-tertiary",
                        !isActiveCategory(category) && category.color === "language-quaternary" && "group-hover:text-language-quaternary"
                      )} />
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent
                    align="end"
                    className="p-2 border border-language-border shadow-lg rounded-xl bg-white/95 backdrop-blur-sm w-56"
                    sideOffset={8}
                  >
                    <div className="mb-2 px-3 py-1">
                      <div className={cn(
                        "text-sm font-medium flex items-center gap-2",
                        category.color === "language-primary" && "text-language-primary",
                        category.color === "language-secondary" && "text-language-secondary",
                        category.color === "language-accent" && "text-language-accent",
                        category.color === "language-tertiary" && "text-language-tertiary",
                        category.color === "language-quaternary" && "text-language-quaternary"
                      )}>
                        <div className={cn(
                          "p-1 rounded-md",
                          category.color === "language-primary" && "bg-language-primary/10 text-language-primary",
                          category.color === "language-secondary" && "bg-language-secondary/10 text-language-secondary",
                          category.color === "language-accent" && "bg-language-accent/10 text-language-accent",
                          category.color === "language-tertiary" && "bg-language-tertiary/10 text-language-tertiary",
                          category.color === "language-quaternary" && "bg-language-quaternary/10 text-language-quaternary"
                        )}>
                          {category.icon}
                        </div>
                        <span>{category.label}</span>
                      </div>
                      <div className="text-xs text-gray-500 mt-1">Válassz egy opciót</div>
                    </div>
                    <DropdownMenuSeparator />
                    {category.items.map((item) => (
                      <DropdownMenuItem key={item.path} asChild className="p-0 focus:bg-transparent">
                        <Link
                          to={item.path}
                          className={cn(
                            "group flex items-center gap-3 w-full px-3 py-2 rounded-lg transition-all duration-200 my-1",
                            location.pathname === item.path && category.color === "language-primary" && "bg-language-primary/10 text-language-primary",
                            location.pathname === item.path && category.color === "language-secondary" && "bg-language-secondary/10 text-language-secondary",
                            location.pathname === item.path && category.color === "language-accent" && "bg-language-accent/10 text-language-accent",
                            location.pathname === item.path && category.color === "language-tertiary" && "bg-language-tertiary/10 text-language-tertiary",
                            location.pathname === item.path && category.color === "language-quaternary" && "bg-language-quaternary/10 text-language-quaternary",
                            location.pathname !== item.path && "text-gray-700",
                            location.pathname !== item.path && category.color === "language-primary" && "hover:bg-language-primary/10 hover:text-language-primary",
                            location.pathname !== item.path && category.color === "language-secondary" && "hover:bg-language-secondary/10 hover:text-language-secondary",
                            location.pathname !== item.path && category.color === "language-accent" && "hover:bg-language-accent/10 hover:text-language-accent",
                            location.pathname !== item.path && category.color === "language-tertiary" && "hover:bg-language-tertiary/10 hover:text-language-tertiary",
                            location.pathname !== item.path && category.color === "language-quaternary" && "hover:bg-language-quaternary/10 hover:text-language-quaternary"
                          )}
                        >
                          <div className={cn(
                            "p-1.5 rounded-md transition-all duration-200",
                            location.pathname === item.path && category.color === "language-primary" && "bg-language-primary/30 text-language-primary",
                            location.pathname === item.path && category.color === "language-secondary" && "bg-language-secondary/30 text-language-secondary",
                            location.pathname === item.path && category.color === "language-accent" && "bg-language-accent/30 text-language-accent",
                            location.pathname === item.path && category.color === "language-tertiary" && "bg-language-tertiary/30 text-language-tertiary",
                            location.pathname === item.path && category.color === "language-quaternary" && "bg-language-quaternary/30 text-language-quaternary",
                            location.pathname !== item.path && category.color === "language-primary" && "bg-language-primary/10 text-language-primary group-hover:bg-language-primary/20",
                            location.pathname !== item.path && category.color === "language-secondary" && "bg-language-secondary/10 text-language-secondary group-hover:bg-language-secondary/20",
                            location.pathname !== item.path && category.color === "language-accent" && "bg-language-accent/10 text-language-accent group-hover:bg-language-accent/20",
                            location.pathname !== item.path && category.color === "language-tertiary" && "bg-language-tertiary/10 text-language-tertiary group-hover:bg-language-tertiary/20",
                            location.pathname !== item.path && category.color === "language-quaternary" && "bg-language-quaternary/10 text-language-quaternary group-hover:bg-language-quaternary/20"
                          )}>
                            {item.icon}
                          </div>
                          <div className="flex flex-col">
                            <div className="flex items-center gap-2">
                              <span className="font-medium">{item.label}</span>
                              {item.badge && (
                                <Badge
                                  className={cn(
                                    "text-xs py-0 px-1.5",
                                    item.badge.variant === "primary" && "bg-language-primary/20 text-language-primary",
                                    item.badge.variant === "secondary" && "bg-language-secondary/20 text-language-secondary",
                                    item.badge.variant === "accent" && "bg-language-accent/20 text-language-accent",
                                    item.badge.variant === "tertiary" && "bg-language-tertiary/20 text-language-tertiary",
                                    item.badge.variant === "quaternary" && "bg-language-quaternary/20 text-language-quaternary",
                                    item.badge.variant === "teal" && "bg-teal-100 text-teal-700",
                                    item.badge.variant === "success" && "bg-green-100 text-green-700",
                                    item.badge.variant === "warning" && "bg-yellow-100 text-yellow-700"
                                  )}
                                >
                                  {item.badge.text}
                                </Badge>
                              )}
                            </div>
                            <span className="text-xs text-gray-500">{item.description}</span>
                          </div>
                        </Link>
                      </DropdownMenuItem>
                    ))}
                  </DropdownMenuContent>
                </DropdownMenu>
              );
            })}

            {/* Pontok megjelenítése */}
            {isAuthenticated && <PointsDisplay className="mr-2" />}

            {/* Felhasználói menü */}
            {isAuthenticated ? (
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button
                    variant="ghost"
                    className="group flex items-center gap-2 px-3 py-2 rounded-full transition-all duration-200 text-gray-600 hover:text-language-quaternary hover:bg-language-hover"
                  >
                    <div className="p-1.5 rounded-md text-gray-600 group-hover:text-language-quaternary">
                      <UserCircle className="w-5 h-5" />
                    </div>
                    <span className="max-w-[100px] truncate">{user?.name || 'Felhasználó'}</span>
                    <ChevronDown className="w-4 h-4 text-gray-400 group-hover:text-language-quaternary" />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end" className="w-56">
                  <div className="px-3 py-2 border-b">
                    <div className="font-medium">{user?.name}</div>
                    <div className="text-xs text-gray-500">{user?.email}</div>
                    {hasSubscription ? (
                      <Badge className="mt-1 bg-green-100 text-green-800 hover:bg-green-100">
                        Aktív előfizetés
                      </Badge>
                    ) : (
                      <Badge className="mt-1 bg-orange-100 text-orange-800 hover:bg-orange-100">
                        Nincs előfizetés
                      </Badge>
                    )}
                  </div>
                  <DropdownMenuItem asChild className="p-0 focus:bg-transparent">
                    <Link
                      to="/profile"
                      className="flex items-center gap-2 w-full px-3 py-2 hover:bg-gray-100 transition-colors"
                    >
                      <User className="w-4 h-4" />
                      <span>Profilom</span>
                    </Link>
                  </DropdownMenuItem>
                  <DropdownMenuItem asChild className="p-0 focus:bg-transparent">
                    <Link
                      to="/subscription"
                      className="flex items-center gap-2 w-full px-3 py-2 hover:bg-gray-100 transition-colors"
                    >
                      <CreditCard className="w-4 h-4" />
                      <span>Előfizetés kezelése</span>
                    </Link>
                  </DropdownMenuItem>
                  <DropdownMenuItem asChild className="p-0 focus:bg-transparent">
                    <Link
                      to="/invitations"
                      className="flex items-center gap-2 w-full px-3 py-2 hover:bg-gray-100 transition-colors"
                    >
                      <UserPlus className="w-4 h-4" />
                      <span>Barátok meghívása</span>
                    </Link>
                  </DropdownMenuItem>
                  <DropdownMenuItem asChild className="p-0 focus:bg-transparent">
                    <Link
                      to="/points"
                      className="flex items-center gap-2 w-full px-3 py-2 hover:bg-gray-100 transition-colors"
                    >
                      <Coins className="w-4 h-4" />
                      <span>Pontjaim</span>
                    </Link>
                  </DropdownMenuItem>
                  {isTeacher ? (
                    <DropdownMenuItem asChild className="p-0 focus:bg-transparent">
                      <Link
                        to="/teacher/dashboard"
                        className="flex items-center gap-2 w-full px-3 py-2 hover:bg-gray-100 transition-colors"
                      >
                        <Users className="w-4 h-4" />
                        <span>Tanári vezérlőpult</span>
                      </Link>
                    </DropdownMenuItem>
                  ) : (
                    <DropdownMenuItem asChild className="p-0 focus:bg-transparent">
                      <Link
                        to="/teacher/subscribe"
                        className="flex items-center gap-2 w-full px-3 py-2 hover:bg-gray-100 transition-colors"
                      >
                        <Users className="w-4 h-4" />
                        <span>Tanári csomag</span>
                      </Link>
                    </DropdownMenuItem>
                  )}
                  <DropdownMenuItem asChild className="p-0 focus:bg-transparent">
                    <Link
                      to="/support"
                      className="flex items-center gap-2 w-full px-3 py-2 hover:bg-gray-100 transition-colors"
                    >
                      <HelpCircle className="w-4 h-4" />
                      <span>Ügyfélszolgálat</span>
                    </Link>
                  </DropdownMenuItem>
                  <DropdownMenuSeparator />
                  <DropdownMenuItem
                    onClick={() => logout()}
                    className="flex items-center gap-2 px-3 py-2 hover:bg-gray-100 transition-colors cursor-pointer"
                  >
                    <LogOut className="w-4 h-4" />
                    <span>Kijelentkezés</span>
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
            ) : (
              <div className="flex items-center gap-2">
                <Link to="/login">
                  <Button variant="ghost" className="flex items-center gap-1 rounded-full">
                    <LogIn className="w-4 h-4 mr-1" />
                    Bejelentkezés
                  </Button>
                </Link>
                <Link to="/register">
                  <Button className="bg-language-quaternary hover:bg-language-quaternary/90 text-white rounded-full">
                    Regisztráció
                  </Button>
                </Link>
              </div>
            )}
          </div>
        </div>

        {/* Mobil navigáció */}
        <div className="flex md:hidden justify-between items-center">
          <Link to="/" className="text-lg font-bold flex items-center gap-1.5">
            <div className="relative">
              <div className="absolute -inset-1 rounded-full bg-gradient-to-r from-language-primary to-language-accent opacity-70 blur-sm" />
              <div className="relative bg-white rounded-full p-1">
                <BookOpen className="w-5 h-5 text-language-primary" />
              </div>
            </div>
            <span className="bg-gradient-to-r from-language-primary to-language-accent bg-clip-text text-transparent">Magyar-Német</span>
          </Link>

          <Button
            variant="ghost"
            size="sm"
            onClick={toggleMobileMenu}
            className={cn(
              "rounded-full p-2 transition-all",
              mobileMenuOpen
                ? "bg-language-focus text-language-primary"
                : "text-gray-600 hover:bg-language-focus hover:text-language-primary"
            )}
          >
            <AnimatePresence mode="wait" initial={false}>
              <motion.div
                key={mobileMenuOpen ? "close" : "open"}
                initial={{ opacity: 0, rotate: -90, scale: 0.5 }}
                animate={{ opacity: 1, rotate: 0, scale: 1 }}
                exit={{ opacity: 0, rotate: 90, scale: 0.5 }}
                transition={{ duration: 0.2 }}
              >
                {mobileMenuOpen ? <X className="w-5 h-5" /> : <Menu className="w-5 h-5" />}
              </motion.div>
            </AnimatePresence>
          </Button>
        </div>

        {/* Mobil menü */}
        <AnimatePresence>
          {mobileMenuOpen && (
            <motion.div
              className="md:hidden pt-4 pb-2 mt-3 bg-white rounded-xl shadow-lg border border-language-border overflow-hidden"
              initial={{ height: 0, opacity: 0 }}
              animate={{ height: "auto", opacity: 1 }}
              exit={{ height: 0, opacity: 0 }}
              transition={{ duration: 0.3, ease: "easeInOut" }}
            >
              {/* Felhasználói menü */}
              {isAuthenticated ? (
                <div className="mb-4 px-4">
                  <div className="flex items-center gap-3 mb-2">
                    <div className="p-2 bg-language-quaternary/10 rounded-full">
                      <UserCircle className="w-6 h-6 text-language-quaternary" />
                    </div>
                    <div>
                      <div className="font-medium">{user?.name}</div>
                      <div className="text-xs text-gray-500">{user?.email}</div>
                    </div>
                  </div>
                  <div className="mb-3">
                    <PointsDisplay variant="compact" className="w-full justify-center" />
                  </div>
                  <div className="grid grid-cols-2 gap-2 mt-3">
                    <Link
                      to="/profile"
                      className="flex items-center justify-center gap-2 px-3 py-2 bg-gray-100 rounded-lg hover:bg-gray-200 transition-colors"
                      onClick={() => setMobileMenuOpen(false)}
                    >
                      <User className="w-4 h-4" />
                      <span className="text-sm">Profilom</span>
                    </Link>
                    <Link
                      to="/subscription"
                      className="flex items-center justify-center gap-2 px-3 py-2 bg-gray-100 rounded-lg hover:bg-gray-200 transition-colors"
                      onClick={() => setMobileMenuOpen(false)}
                    >
                      <CreditCard className="w-4 h-4" />
                      <span className="text-sm">Előfizetés</span>
                    </Link>
                    <Link
                      to="/invitations"
                      className="flex items-center justify-center gap-2 px-3 py-2 bg-gray-100 rounded-lg hover:bg-gray-200 transition-colors"
                      onClick={() => setMobileMenuOpen(false)}
                    >
                      <UserPlus className="w-4 h-4" />
                      <span className="text-sm">Barátok meghívása</span>
                    </Link>
                    <Link
                      to="/points"
                      className="flex items-center justify-center gap-2 px-3 py-2 bg-gray-100 rounded-lg hover:bg-gray-200 transition-colors"
                      onClick={() => setMobileMenuOpen(false)}
                    >
                      <Coins className="w-4 h-4" />
                      <span className="text-sm">Pontjaim</span>
                    </Link>
                    <Link
                      to="/support"
                      className="flex items-center justify-center gap-2 px-3 py-2 bg-gray-100 rounded-lg hover:bg-gray-200 transition-colors"
                      onClick={() => setMobileMenuOpen(false)}
                    >
                      <HelpCircle className="w-4 h-4" />
                      <span className="text-sm">Ügyfélszolgálat</span>
                    </Link>
                  </div>
                  <Button
                    variant="outline"
                    className="w-full mt-3 flex items-center justify-center gap-2"
                    onClick={() => {
                      logout();
                      setMobileMenuOpen(false);
                    }}
                  >
                    <LogOut className="w-4 h-4" />
                    <span>Kijelentkezés</span>
                  </Button>
                </div>
              ) : (
                <div className="mb-4 px-4 grid grid-cols-2 gap-2">
                  <Link
                    to="/login"
                    className="flex items-center justify-center gap-2 px-3 py-2 bg-gray-100 rounded-lg hover:bg-gray-200 transition-colors"
                    onClick={() => setMobileMenuOpen(false)}
                  >
                    <LogIn className="w-4 h-4" />
                    <span className="text-sm">Bejelentkezés</span>
                  </Link>
                  <Link
                    to="/register"
                    className="flex items-center justify-center gap-2 px-3 py-2 bg-language-quaternary/10 text-language-quaternary rounded-lg hover:bg-language-quaternary/20 transition-colors"
                    onClick={() => setMobileMenuOpen(false)}
                  >
                    <User className="w-4 h-4" />
                    <span className="text-sm">Regisztráció</span>
                  </Link>
                </div>
              )}

              {/* Navigációs kategóriák */}
              {navCategories.map((category) => (
                <div key={category.id} className="mb-3">
                  {category.id !== "home" && (
                    <div className={cn(
                      "flex items-center px-3 py-1.5 text-sm font-medium ml-2 mt-2 border-l-2",
                      category.color === "language-primary" && "text-language-primary border-language-primary",
                      category.color === "language-secondary" && "text-language-secondary border-language-secondary",
                      category.color === "language-accent" && "text-language-accent border-language-accent",
                      category.color === "language-tertiary" && "text-language-tertiary border-language-tertiary",
                      category.color === "language-quaternary" && "text-language-quaternary border-language-quaternary"
                    )}>
                      <div className={cn(
                        "p-1 rounded-md",
                        category.color === "language-primary" && "bg-language-primary/10 text-language-primary",
                        category.color === "language-secondary" && "bg-language-secondary/10 text-language-secondary",
                        category.color === "language-accent" && "bg-language-accent/10 text-language-accent",
                        category.color === "language-tertiary" && "bg-language-tertiary/10 text-language-tertiary",
                        category.color === "language-quaternary" && "bg-language-quaternary/10 text-language-quaternary"
                      )}>
                        {category.icon}
                      </div>
                      <span className="ml-2">{category.label}</span>
                    </div>
                  )}

                  {/* Enhanced grid layout for Szakmai category on mobile */}
                  {category.id === "vocational" ? (
                    <div className="mt-3 px-2">
                      <div className="mb-2 mx-2">
                        <div className="text-sm text-gray-500">Szakmai szókincs és nyelvtani gyakorlatok különböző területekhez</div>
                      </div>

                      <div className="grid grid-cols-2 gap-2">
                        {category.items.map((item) => (
                          <Link
                            key={item.path}
                            to={item.path}
                            className={cn(
                              "group flex items-center gap-2 px-3 py-2.5 rounded-lg border transition-all duration-200",
                              location.pathname === item.path
                                ? "bg-language-quaternary/10 text-language-quaternary border-language-quaternary/30"
                                : "text-gray-700 hover:bg-language-quaternary/5 hover:text-language-quaternary border-gray-100 hover:border-language-quaternary/20"
                            )}
                            onClick={() => setMobileMenuOpen(false)}
                          >
                            <div className={cn(
                              "p-2 rounded-md transition-all duration-200 text-white",
                              location.pathname === item.path
                                ? "bg-language-quaternary shadow-md shadow-language-quaternary/20"
                                : "bg-language-quaternary/80 group-hover:bg-language-quaternary shadow-sm"
                            )}>
                              {item.icon}
                            </div>
                            <div className="flex flex-col">
                              <div className="flex items-center gap-1 flex-wrap">
                                <span className="font-medium text-sm">{item.label}</span>
                                {item.badge && (
                                  <Badge className="text-xs py-0 px-1 bg-teal-100 text-teal-700">
                                    {item.badge.text}
                                  </Badge>
                                )}
                              </div>
                              <span className="text-xs text-gray-500 truncate max-w-[120px]">{item.description}</span>
                            </div>
                          </Link>
                        ))}
                      </div>

                      <div className="mt-3 pt-3 border-t border-gray-100 mx-2">
                        <Link
                          to="/professional"
                          className="text-xs text-language-quaternary hover:underline flex items-center gap-1 justify-center"
                          onClick={() => setMobileMenuOpen(false)}
                        >
                          <span>Összes szakmai terület</span>
                          <ChevronRight className="h-3 w-3" />
                        </Link>
                      </div>
                    </div>
                  ) : (
                    <div className="mt-1 space-y-1">
                      {category.items.map((item) => (
                        <Link
                          key={item.path}
                          to={item.path}
                          className={cn(
                            "group flex items-center gap-3 px-4 py-2 rounded-lg transition-all duration-200 mx-2 my-1",
                            location.pathname === item.path && category.color === "language-primary" && "bg-language-primary/10 text-language-primary",
                            location.pathname === item.path && category.color === "language-secondary" && "bg-language-secondary/10 text-language-secondary",
                            location.pathname === item.path && category.color === "language-accent" && "bg-language-accent/10 text-language-accent",
                            location.pathname === item.path && category.color === "language-tertiary" && "bg-language-tertiary/10 text-language-tertiary",
                            location.pathname === item.path && category.color === "language-quaternary" && "bg-language-quaternary/10 text-language-quaternary",
                            location.pathname !== item.path && "text-gray-700",
                            location.pathname !== item.path && category.color === "language-primary" && "hover:bg-language-primary/10 hover:text-language-primary",
                            location.pathname !== item.path && category.color === "language-secondary" && "hover:bg-language-secondary/10 hover:text-language-secondary",
                            location.pathname !== item.path && category.color === "language-accent" && "hover:bg-language-accent/10 hover:text-language-accent",
                            location.pathname !== item.path && category.color === "language-tertiary" && "hover:bg-language-tertiary/10 hover:text-language-tertiary",
                            location.pathname !== item.path && category.color === "language-quaternary" && "hover:bg-language-quaternary/10 hover:text-language-quaternary"
                          )}
                          onClick={() => setMobileMenuOpen(false)}
                        >
                          <div className={cn(
                            "p-1.5 rounded-md transition-all duration-200",
                            location.pathname === item.path && category.color === "language-primary" && "bg-language-primary/30 text-language-primary",
                            location.pathname === item.path && category.color === "language-secondary" && "bg-language-secondary/30 text-language-secondary",
                            location.pathname === item.path && category.color === "language-accent" && "bg-language-accent/30 text-language-accent",
                            location.pathname === item.path && category.color === "language-tertiary" && "bg-language-tertiary/30 text-language-tertiary",
                            location.pathname === item.path && category.color === "language-quaternary" && "bg-language-quaternary/30 text-language-quaternary",
                            location.pathname !== item.path && category.color === "language-primary" && "bg-language-primary/10 text-language-primary group-hover:bg-language-primary/20",
                            location.pathname !== item.path && category.color === "language-secondary" && "bg-language-secondary/10 text-language-secondary group-hover:bg-language-secondary/20",
                            location.pathname !== item.path && category.color === "language-accent" && "bg-language-accent/10 text-language-accent group-hover:bg-language-accent/20",
                            location.pathname !== item.path && category.color === "language-tertiary" && "bg-language-tertiary/10 text-language-tertiary group-hover:bg-language-tertiary/20",
                            location.pathname !== item.path && category.color === "language-quaternary" && "bg-language-quaternary/10 text-language-quaternary group-hover:bg-language-quaternary/20"
                          )}>
                            {item.icon}
                          </div>
                          <div className="flex flex-col">
                            <div className="flex items-center gap-2">
                              <span className="font-medium">{item.label}</span>
                              {item.badge && (
                                <Badge
                                  className={cn(
                                    "text-xs py-0 px-1.5",
                                    item.badge.variant === "primary" && "bg-language-primary/20 text-language-primary",
                                    item.badge.variant === "secondary" && "bg-language-secondary/20 text-language-secondary",
                                    item.badge.variant === "accent" && "bg-language-accent/20 text-language-accent",
                                    item.badge.variant === "tertiary" && "bg-language-tertiary/20 text-language-tertiary",
                                    item.badge.variant === "quaternary" && "bg-language-quaternary/20 text-language-quaternary",
                                    item.badge.variant === "teal" && "bg-teal-100 text-teal-700",
                                    item.badge.variant === "success" && "bg-green-100 text-green-700",
                                    item.badge.variant === "warning" && "bg-yellow-100 text-yellow-700"
                                  )}
                                >
                                  {item.badge.text}
                                </Badge>
                              )}
                            </div>
                            <span className="text-xs text-gray-500">{item.description}</span>
                          </div>
                        </Link>
                      ))}
                    </div>
                  )}
                </div>
              ))}
            </motion.div>
          )}
        </AnimatePresence>
      </div>
    </motion.nav>
  );
};

export default Navigation;
