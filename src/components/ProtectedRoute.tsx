import React from 'react';
import { Navigate, useLocation } from 'react-router-dom';
import { useAuth } from '@/contexts/AuthContext';

interface ProtectedRouteProps {
  children: React.ReactNode;
  requireSubscription?: boolean;
}

const ProtectedRoute: React.FC<ProtectedRouteProps> = ({ 
  children, 
  requireSubscription = true 
}) => {
  const { isAuthenticated, hasSubscription, isLoading } = useAuth();
  const location = useLocation();

  // Ha még töltődik, akkor ne csináljunk semmit
  if (isLoading) {
    return <div className="flex items-center justify-center min-h-screen">
      <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-language-primary"></div>
    </div>;
  }

  // Ha nincs bejelentkezve, átirányítjuk a bejelentkezési oldalra
  if (!isAuthenticated) {
    return <Navigate to="/login" state={{ from: location }} replace />;
  }

  // Ha előfizetés szükséges, de nincs, átirányítjuk a subscription oldalra
  if (requireSubscription && !hasSubscription) {
    return <Navigate to="/subscription" state={{ from: location }} replace />;
  }

  // Ha minden rendben, megjelenítjük a gyermek komponenseket
  return <>{children}</>;
};

export default ProtectedRoute;
