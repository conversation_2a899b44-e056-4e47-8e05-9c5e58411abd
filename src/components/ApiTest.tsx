import React, { useState, useEffect } from 'react';
import api from '../services/apiService';

const ApiTest: React.FC = () => {
  const [apiStatus, setApiStatus] = useState<string>('Tesztelés...');
  const [authTest, setAuthTest] = useState<string>('Tesztelés...');

  useEffect(() => {
    testApiConnection();
  }, []);

  const testApiConnection = async () => {
    try {
      // Alap API teszt
      const response = await api.get('/');
      setApiStatus(`✅ API működik: ${response.data.message}`);
      
      // Auth endpoint teszt
      try {
        await api.get('/auth/me');
        setAuthTest('✅ Auth endpoint elérhető');
      } catch (authError: any) {
        if (authError.response?.status === 401) {
          setAuthTest('✅ Auth endpoint elérhető (401 Unauthorized - normális)');
        } else {
          setAuthTest(`❌ Auth endpoint hiba: ${authError.message}`);
        }
      }
    } catch (error: any) {
      setApiStatus(`❌ API hiba: ${error.message}`);
      setAuthTest('❌ Auth teszt kihagyva API hiba miatt');
    }
  };

  return (
    <div style={{ 
      padding: '20px', 
      border: '1px solid #ccc', 
      borderRadius: '8px', 
      margin: '20px',
      backgroundColor: '#f9f9f9'
    }}>
      <h3>🔧 API Kapcsolat Teszt</h3>
      <div style={{ marginBottom: '10px' }}>
        <strong>Alap API:</strong> {apiStatus}
      </div>
      <div style={{ marginBottom: '10px' }}>
        <strong>Auth API:</strong> {authTest}
      </div>
      <button 
        onClick={testApiConnection}
        style={{
          padding: '8px 16px',
          backgroundColor: '#007bff',
          color: 'white',
          border: 'none',
          borderRadius: '4px',
          cursor: 'pointer'
        }}
      >
        🔄 Újratesztelés
      </button>
    </div>
  );
};

export default ApiTest;
