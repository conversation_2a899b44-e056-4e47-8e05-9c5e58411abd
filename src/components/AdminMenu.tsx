import React from 'react';
import { Link, useLocation } from 'react-router-dom';
import { useAuth } from '@/contexts/AuthContext';
import { Button } from '@/components/ui/button';
import { Users, BarChart2, User } from 'lucide-react';

const AdminMenu: React.FC = () => {
  const { user } = useAuth();
  const location = useLocation();

  // <PERSON>, hogy a felhasználó admin-e
  const isAdmin = user?.id === '11383db6-ab6e-4810-81a9-dc5ac1426d3a';

  if (!isAdmin) {
    return null;
  }

  return (
    <div className="mb-6 border rounded-lg p-4 bg-gray-50">
      <h3 className="text-lg font-semibold mb-3">Admin funk<PERSON></h3>
      <div className="flex flex-col space-y-2">
        <Button
          variant={location.pathname === '/admin/users' ? 'default' : 'outline'}
          size="sm"
          className="justify-start"
          asChild
        >
          <Link to="/admin/users">
            <Users className="h-4 w-4 mr-2" />
            Felhasználók
          </Link>
        </Button>

        <Button
          variant={location.pathname === '/admin/stats' ? 'default' : 'outline'}
          size="sm"
          className="justify-start"
          asChild
        >
          <Link to="/admin/stats">
            <BarChart2 className="h-4 w-4 mr-2" />
            Statisztikák
          </Link>
        </Button>

        {location.pathname.startsWith('/admin/users/') && (
          <Button
            variant="outline"
            size="sm"
            className="justify-start"
            asChild
          >
            <Link to="/admin/users">
              <User className="h-4 w-4 mr-2" />
              Vissza a listához
            </Link>
          </Button>
        )}
      </div>
    </div>
  );
};

export default AdminMenu;
