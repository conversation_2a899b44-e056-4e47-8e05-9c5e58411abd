import React, { useState, useRef, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardT<PERSON><PERSON>, CardFooter } from "@/components/ui/card";
import { Textarea } from "@/components/ui/textarea";
import { Bot, X, SendHorizontal, Loader2, MessageSquare, Sparkles } from "lucide-react";
import { AssistantMessage, sendMessageToAssistant } from "@/services/assistantService";
import { cn } from "@/lib/utils";
import MarkdownRenderer from "@/components/MarkdownRenderer";

const FloatingChatButton = () => {
  const [isOpen, setIsOpen] = useState(false);
  const [messages, setMessages] = useState<AssistantMessage[]>([]);
  const [inputMessage, setInputMessage] = useState("");
  const [isLoading, setIsLoading] = useState(false);
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const chatContainerRef = useRef<HTMLDivElement>(null);

  // Automatikus görgetés a chat alján
  useEffect(() => {
    if (messagesEndRef.current) {
      messagesEndRef.current.scrollIntoView({ behavior: "smooth" });
    }
  }, [messages]);

  // Kezdeti üdvözlő üzenet
  useEffect(() => {
    if (isOpen && messages.length === 0) {
      const welcomeMessage: AssistantMessage = {
        role: "assistant",
        content: "Szia! Én vagyok a német nyelvi asszisztensed. Miben segíthetek neked ma?",
      };
      setMessages([welcomeMessage]);
    }
  }, [isOpen, messages.length]);

  // Kattintás figyelése a chaten kívül
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        isOpen &&
        chatContainerRef.current &&
        !chatContainerRef.current.contains(event.target as Node)
      ) {
        setIsOpen(false);
      }
    };

    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, [isOpen]);

  // Üzenet küldése
  const handleSendMessage = async () => {
    if (!inputMessage.trim()) return;

    // Felhasználói üzenet hozzáadása
    const userMessage: AssistantMessage = {
      role: "user",
      content: inputMessage,
    };
    setMessages((prev) => [...prev, userMessage]);
    setInputMessage("");
    setIsLoading(true);

    try {
      // Asszisztens válaszának lekérése
      const response = await sendMessageToAssistant([...messages, userMessage]);

      // Asszisztens válaszának hozzáadása
      const assistantMessage: AssistantMessage = {
        role: "assistant",
        content: response,
      };
      setMessages((prev) => [...prev, assistantMessage]);
    } catch (error) {
      console.error("Hiba az asszisztens válaszának lekérésekor:", error);
      // Hibaüzenet hozzáadása
      const errorMessage: AssistantMessage = {
        role: "assistant",
        content: "Sajnos hiba történt a válasz generálásakor. Kérlek, próbáld újra később.",
      };
      setMessages((prev) => [...prev, errorMessage]);
    } finally {
      setIsLoading(false);
    }
  };

  // Chat bezárása
  const handleClose = () => {
    setIsOpen(false);
  };

  return (
    <>
      {/* Lebegő chat gomb */}
      <Button
        className="fixed bottom-20 md:bottom-6 right-6 rounded-full w-12 h-12 shadow-lg z-50 flex items-center justify-center bg-gradient-to-r from-language-primary to-language-accent hover:shadow-xl transition-all duration-300 border-0 group"
        onClick={() => setIsOpen(true)}
      >
        <div className="absolute inset-0 rounded-full bg-white opacity-0 group-hover:opacity-10 transition-opacity duration-300"></div>
        <MessageSquare className="h-5 w-5 text-white group-hover:scale-110 transition-transform duration-300" />
      </Button>

      {/* Chat ablak */}
      {isOpen && (
        <div
          ref={chatContainerRef}
          className="fixed bottom-24 md:bottom-20 right-6 z-50 w-[85vw] max-w-sm animate-in slide-in-from-bottom-5 duration-200"
        >
          <Card className="shadow-xl border border-slate-200 rounded-2xl overflow-hidden">
            <CardHeader className="py-2.5 px-4 flex flex-row items-center justify-between bg-gradient-to-r from-language-primary/10 to-language-accent/10">
              <CardTitle className="text-sm flex items-center gap-2">
                <div className="flex items-center justify-center bg-gradient-to-r from-language-primary to-language-accent rounded-full p-1.5">
                  <Bot className="h-3.5 w-3.5 text-white" />
                </div>
                <span className="bg-gradient-to-r from-language-primary to-language-accent bg-clip-text text-transparent font-medium">Nyelvi Asszisztens</span>
                <Sparkles className="h-3.5 w-3.5 text-language-accent/70 ml-1" />
              </CardTitle>
              <Button
                variant="ghost"
                size="sm"
                className="h-7 w-7 p-0 rounded-full hover:bg-slate-200/70"
                onClick={handleClose}
              >
                <X className="h-3.5 w-3.5" />
              </Button>
            </CardHeader>
            <CardContent className="p-0">
              <div className="h-[300px] overflow-y-auto p-3 space-y-3 bg-slate-50/50">
                {messages.map((msg, index) => (
                  <div
                    key={index}
                    className={cn(
                      "flex",
                      msg.role === "user" ? "justify-end" : "justify-start"
                    )}
                  >
                    <div
                      className={cn(
                        "max-w-[80%] rounded-xl px-3 py-2 shadow-sm",
                        msg.role === "user"
                          ? "bg-gradient-to-r from-language-primary to-language-accent text-white"
                          : "bg-white border border-slate-200"
                      )}
                    >
                      {msg.role === "assistant" ? (
                        <MarkdownRenderer content={msg.content} className="text-sm" />
                      ) : (
                        <div className="whitespace-pre-wrap text-sm">{msg.content}</div>
                      )}
                    </div>
                  </div>
                ))}
                <div ref={messagesEndRef} />
              </div>
            </CardContent>
            <CardFooter className="p-2 border-t bg-white">
              <div className="flex gap-2 w-full items-end">
                <Textarea
                  placeholder="Írj egy üzenetet..."
                  value={inputMessage}
                  onChange={(e) => setInputMessage(e.target.value)}
                  className="flex-1 min-h-[36px] max-h-[100px] resize-none text-sm rounded-xl border-slate-200 focus-visible:ring-language-primary"
                  onKeyDown={(e) => {
                    if (e.key === "Enter" && !e.shiftKey) {
                      e.preventDefault();
                      handleSendMessage();
                    }
                  }}
                  disabled={isLoading}
                />
                <Button
                  className="h-9 w-9 p-0 rounded-full bg-gradient-to-r from-language-primary to-language-accent hover:shadow-md transition-all duration-200"
                  onClick={handleSendMessage}
                  disabled={isLoading || !inputMessage.trim()}
                >
                  {isLoading ? (
                    <Loader2 className="h-3.5 w-3.5 animate-spin text-white" />
                  ) : (
                    <SendHorizontal className="h-3.5 w-3.5 text-white" />
                  )}
                </Button>
              </div>
            </CardFooter>
          </Card>
        </div>
      )}
    </>
  );
};

export default FloatingChatButton;
