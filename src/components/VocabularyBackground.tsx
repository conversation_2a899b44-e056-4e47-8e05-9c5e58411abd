import React from "react";

const VocabularyBackground: React.FC = () => {
  return (
    <div className="fixed inset-0 -z-10 overflow-hidden pointer-events-none">
      {/* Fő háttér gradiens */}
      <div className="absolute inset-0 bg-gradient-to-br from-white via-language-background to-language-light opacity-80"></div>
      
      {/* Absztrakt formák és minták */}
      <svg
        className="absolute inset-0 w-full h-full opacity-[0.07]"
        xmlns="http://www.w3.org/2000/svg"
        viewBox="0 0 1920 1080"
        preserveAspectRatio="xMidYMid slice"
      >
        {/* Szótanulás témájú elemek */}
        <g opacity="0.2" fill="#3b82f6">
          <text x="150" y="200" fontSize="40" fontFamily="Arial" fontWeight="bold">A B C</text>
          <text x="1600" y="300" fontSize="40" fontFamily="Arial" fontWeight="bold">Ä Ö Ü</text>
          <text x="800" y="200" fontSize="30" fontFamily="Arial" fontWeight="bold">Wörterbuch</text>
          <text x="1200" y="500" fontSize="30" fontFamily="Arial" fontWeight="bold">Szótár</text>
          <text x="300" y="700" fontSize="35" fontFamily="Arial" fontWeight="bold">Lernen</text>
          <text x="1500" y="800" fontSize="35" fontFamily="Arial" fontWeight="bold">Tanulás</text>
        </g>
        
        {/* Dekoratív vonalak és formák */}
        <path d="M0,100 C300,200 600,50 900,100 S1200,200 1500,150 S1800,50 1920,100" stroke="#10b981" strokeWidth="2" fill="none" opacity="0.2" />
        <path d="M0,800 C300,700 600,850 900,800 S1200,700 1500,750 S1800,850 1920,800" stroke="#8b5cf6" strokeWidth="2" fill="none" opacity="0.2" />
        
        {/* Szótanulás témájú formák */}
        <rect x="100" y="600" width="200" height="200" rx="30" fill="#3b82f6" opacity="0.1" />
        <rect x="1600" y="200" width="200" height="200" rx="30" fill="#10b981" opacity="0.1" />
        <rect x="800" y="700" width="300" height="300" rx="150" fill="#8b5cf6" opacity="0.1" />
        
        {/* Flashcard-szerű elemek */}
        <rect x="400" y="300" width="180" height="120" rx="10" fill="#3b82f6" opacity="0.15" />
        <rect x="1200" y="250" width="180" height="120" rx="10" fill="#10b981" opacity="0.15" />
        <rect x="700" y="500" width="180" height="120" rx="10" fill="#8b5cf6" opacity="0.15" />
        <rect x="1000" y="700" width="180" height="120" rx="10" fill="#f59e0b" opacity="0.15" />
      </svg>
      
      {/* Lebegő pontok és részecskék */}
      <div className="absolute inset-0">
        <div className="absolute top-1/4 left-1/4 w-4 h-4 rounded-full bg-language-primary opacity-20 animate-pulse"></div>
        <div className="absolute top-3/4 left-1/3 w-6 h-6 rounded-full bg-language-secondary opacity-20 animate-pulse" style={{ animationDelay: "1s" }}></div>
        <div className="absolute top-1/2 left-2/3 w-5 h-5 rounded-full bg-language-accent opacity-20 animate-pulse" style={{ animationDelay: "2s" }}></div>
        <div className="absolute top-1/3 left-3/4 w-3 h-3 rounded-full bg-language-quaternary opacity-20 animate-pulse" style={{ animationDelay: "1.5s" }}></div>
      </div>
      
      {/* Fényes foltok */}
      <div className="absolute top-0 left-0 w-full h-full">
        <div className="absolute top-0 left-1/4 w-[500px] h-[500px] bg-language-primary opacity-[0.03] rounded-full blur-[100px]"></div>
        <div className="absolute bottom-0 right-1/4 w-[600px] h-[600px] bg-language-accent opacity-[0.03] rounded-full blur-[120px]"></div>
      </div>
    </div>
  );
};

export default VocabularyBackground;
