import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON> } from "../ui/button";
import { Card, CardContent, CardDescription, <PERSON>Footer, CardHeader, CardTitle } from "../ui/card";
import { Badge } from "../ui/badge";
import { Separator } from "../ui/separator";

interface DominoTile {
  id: string;
  left: {
    type: 'time' | 'activity';
    value: string;
    displayText: string;
  };
  right: {
    type: 'time' | 'activity';
    value: string;
    displayText: string;
  };
  matched: boolean;
  selected: boolean;
}

const TimeDomino: React.FC = () => {
  const [dominoTiles, setDominoTiles] = useState<DominoTile[]>([]);
  const [selectedTile, setSelectedTile] = useState<DominoTile | null>(null);
  const [matchedPairs, setMatchedPairs] = useState<number>(0);
  const [gameOver, setGameOver] = useState<boolean>(false);
  const [difficulty, setDifficulty] = useState<'easy' | 'medium' | 'hard'>('easy');
  const [showSuccess, setShowSuccess] = useState<boolean>(false);
  const [moves, setMoves] = useState<number>(0);

  const timeActivities = [
    // Reggeli tevékenységek
    { 
      time: '06:00', 
      timeDisplay: '6 Uhr', 
      activity: 'aufstehen', 
      activityDisplay: 'Ich stehe auf.'
    },
    { 
      time: '06:30', 
      timeDisplay: 'halb sieben', 
      activity: 'duschen', 
      activityDisplay: 'Ich dusche.'
    },
    { 
      time: '07:00', 
      timeDisplay: '7 Uhr', 
      activity: 'frühstücken', 
      activityDisplay: 'Ich frühstücke.'
    },
    { 
      time: '07:30', 
      timeDisplay: 'halb acht', 
      activity: 'anziehen', 
      activityDisplay: 'Ich ziehe mich an.'
    },
    { 
      time: '08:00', 
      timeDisplay: '8 Uhr', 
      activity: 'zur_arbeit_gehen', 
      activityDisplay: 'Ich gehe zur Arbeit.'
    },
    
    // Déli tevékenységek
    { 
      time: '12:00', 
      timeDisplay: '12 Uhr', 
      activity: 'mittagessen', 
      activityDisplay: 'Ich esse zu Mittag.'
    },
    { 
      time: '13:00', 
      timeDisplay: '13 Uhr', 
      activity: 'pause_machen', 
      activityDisplay: 'Ich mache eine Pause.'
    },
    
    // Délutáni tevékenységek
    { 
      time: '15:30', 
      timeDisplay: 'halb vier', 
      activity: 'kaffee_trinken', 
      activityDisplay: 'Ich trinke einen Kaffee.'
    },
    { 
      time: '17:00', 
      timeDisplay: '17 Uhr', 
      activity: 'nach_hause_gehen', 
      activityDisplay: 'Ich gehe nach Hause.'
    },
    
    // Esti tevékenységek
    { 
      time: '18:00', 
      timeDisplay: '18 Uhr', 
      activity: 'abendessen', 
      activityDisplay: 'Ich esse zu Abend.'
    },
    { 
      time: '19:30', 
      timeDisplay: 'halb acht', 
      activity: 'fernsehen', 
      activityDisplay: 'Ich sehe fern.'
    },
    { 
      time: '20:15', 
      timeDisplay: 'Viertel nach acht', 
      activity: 'buch_lesen', 
      activityDisplay: 'Ich lese ein Buch.'
    },
    { 
      time: '22:00', 
      timeDisplay: '22 Uhr', 
      activity: 'schlafen_gehen', 
      activityDisplay: 'Ich gehe schlafen.'
    }
  ];

  // Új játék kezdése
  const startNewGame = () => {
    let gameTiles: DominoTile[] = [];
    let pairCount: number;
    
    // A nehézségi szint alapján határozzuk meg a párok számát
    switch (difficulty) {
      case 'easy':
        pairCount = 6;
        break;
      case 'medium':
        pairCount = 8;
        break;
      case 'hard':
        pairCount = 10;
        break;
      default:
        pairCount = 6;
    }
    
    // Létrehozzuk a véletlenszerű párokat
    const shuffledActivities = [...timeActivities].sort(() => 0.5 - Math.random());
    const selectedActivities = shuffledActivities.slice(0, pairCount);
    
    selectedActivities.forEach((item, index) => {
      // Bal oldali dominó: idő és a következő tevékenység
      gameTiles.push({
        id: `tile-${index}-left`,
        left: {
          type: 'time',
          value: item.time,
          displayText: item.timeDisplay
        },
        right: {
          type: 'activity',
          value: selectedActivities[(index + 1) % pairCount].activity,
          displayText: selectedActivities[(index + 1) % pairCount].activityDisplay
        },
        matched: false,
        selected: false
      });
      
      // Jobb oldali dominó: tevékenység és a következő idő
      gameTiles.push({
        id: `tile-${index}-right`,
        left: {
          type: 'activity',
          value: item.activity,
          displayText: item.activityDisplay
        },
        right: {
          type: 'time',
          value: selectedActivities[(index + 1) % pairCount].time,
          displayText: selectedActivities[(index + 1) % pairCount].timeDisplay
        },
        matched: false,
        selected: false
      });
    });
    
    // Keverjük össze a dominókat
    gameTiles = gameTiles.sort(() => 0.5 - Math.random());
    
    setDominoTiles(gameTiles);
    setSelectedTile(null);
    setMatchedPairs(0);
    setGameOver(false);
    setMoves(0);
  };

  // Komponens betöltésekor
  useEffect(() => {
    startNewGame();
  }, [difficulty]);

  // Ellenőrizzük, hogy két dominó illeszkedik-e
  const checkMatch = (tile1: DominoTile, tile2: DominoTile): boolean => {
    // Az első dominó jobb oldala egyezik a második dominó bal oldalával
    if (tile1.right.type === tile2.left.type && tile1.right.value === tile2.left.value) {
      return true;
    }
    // A második dominó jobb oldala egyezik az első dominó bal oldalával
    if (tile2.right.type === tile1.left.type && tile2.right.value === tile1.left.value) {
      return true;
    }
    return false;
  };

  // Dominó kiválasztása
  const selectTile = (tile: DominoTile) => {
    if (tile.matched || showSuccess) return;
    
    if (!selectedTile) {
      // Első dominó kiválasztása
      setSelectedTile(tile);
      setDominoTiles(dominoTiles.map(t => 
        t.id === tile.id 
          ? { ...t, selected: true } 
          : t
      ));
    } else if (selectedTile.id === tile.id) {
      // Ugyanaz a dominó, kijelölés megszüntetése
      setSelectedTile(null);
      setDominoTiles(dominoTiles.map(t => 
        t.id === tile.id 
          ? { ...t, selected: false } 
          : t
      ));
    } else {
      // Második dominó kiválasztása, ellenőrizzük a párosítást
      setMoves(moves + 1);
      
      // Ellenőrizzük, hogy a két dominó illeszkedik-e
      const match = checkMatch(selectedTile, tile);
      
      if (match) {
        // Sikeres párosítás
        const updatedTiles = dominoTiles.map(t => 
          t.id === tile.id || t.id === selectedTile.id 
            ? { ...t, matched: true, selected: false } 
            : t
        );
        
        setDominoTiles(updatedTiles);
        setSelectedTile(null);
        setMatchedPairs(matchedPairs + 1);
        
        // Sikeres párosítás visszajelzés
        setShowSuccess(true);
        setTimeout(() => setShowSuccess(false), 1000);
        
        // Ellenőrizzük, hogy vége van-e a játéknak
        const remainingUnmatched = updatedTiles.filter(t => !t.matched).length;
        if (remainingUnmatched === 0) {
          setGameOver(true);
        }
      } else {
        // Sikertelen párosítás, visszaállítjuk a kijelölést
        setDominoTiles(dominoTiles.map(t => 
          t.id === tile.id 
            ? { ...t, selected: true } 
            : t
        ));
        
        setTimeout(() => {
          setDominoTiles(dominoTiles.map(t => 
            (t.id === tile.id || t.id === selectedTile.id) 
              ? { ...t, selected: false } 
              : t
          ));
          setSelectedTile(null);
        }, 1000);
      }
    }
  };

  return (
    <Card className="w-full max-w-4xl mx-auto">
      <CardHeader>
        <CardTitle>Idő-dominó</CardTitle>
        <CardDescription>
          Párosítsd össze az időpontokat a megfelelő tevékenységekkel, hogy gyakorold a német időkifejezéseket!
        </CardDescription>
      </CardHeader>
      
      <CardContent className="space-y-6">
        {/* Nehézségi szint váltó */}
        <div className="flex justify-center gap-2">
          <Button 
            variant={difficulty === 'easy' ? 'default' : 'outline'} 
            onClick={() => setDifficulty('easy')}
            className="w-28"
          >
            Könnyű
          </Button>
          <Button 
            variant={difficulty === 'medium' ? 'default' : 'outline'} 
            onClick={() => setDifficulty('medium')}
            className="w-28"
          >
            Közepes
          </Button>
          <Button 
            variant={difficulty === 'hard' ? 'default' : 'outline'} 
            onClick={() => setDifficulty('hard')}
            className="w-28"
          >
            Nehéz
          </Button>
        </div>
        
        {/* Játékállapot */}
        <div className="flex justify-between items-center bg-gray-50 p-3 rounded-lg">
          <div>
            <span className="font-medium">Lépések:</span> {moves}
          </div>
          <div>
            <span className="font-medium">Párok:</span> {matchedPairs} / {dominoTiles.length / 2}
          </div>
          <Button onClick={startNewGame}>
            Új játék
          </Button>
        </div>
        
        {/* Dominó játéktér */}
        <div className={`grid grid-cols-2 md:grid-cols-4 gap-4 relative ${showSuccess ? 'pointer-events-none' : ''}`}>
          {dominoTiles.map(tile => (
            <div
              key={tile.id}
              onClick={() => selectTile(tile)}
              className={`
                cursor-pointer border-2 rounded-lg overflow-hidden transition-all
                ${tile.matched ? 'opacity-50' : 'opacity-100'}
                ${tile.selected ? 'border-blue-500 shadow-lg scale-105' : 'border-gray-200'}
              `}
            >
              <div className="grid grid-cols-2 h-full">
                {/* Bal oldal */}
                <div className={`p-3 flex items-center justify-center ${tile.left.type === 'time' ? 'bg-amber-100' : 'bg-emerald-100'}`}>
                  <div className="text-center">
                    <span className="text-sm font-medium">
                      {tile.left.type === 'time' ? 'Zeit' : 'Aktivität'}
                    </span>
                    <p className="mt-1">{tile.left.displayText}</p>
                  </div>
                </div>
                
                {/* Jobb oldal */}
                <div className={`p-3 flex items-center justify-center ${tile.right.type === 'time' ? 'bg-amber-100' : 'bg-emerald-100'}`}>
                  <div className="text-center">
                    <span className="text-sm font-medium">
                      {tile.right.type === 'time' ? 'Zeit' : 'Aktivität'}
                    </span>
                    <p className="mt-1">{tile.right.displayText}</p>
                  </div>
                </div>
              </div>
            </div>
          ))}
          
          {/* Siker visszajelzés */}
          {showSuccess && (
            <div className="absolute inset-0 flex items-center justify-center bg-green-500 bg-opacity-20 rounded-lg">
              <div className="bg-white p-4 rounded-lg shadow-lg">
                <h3 className="text-xl font-bold text-green-600">Nagyszerű!</h3>
                <p>Sikeres párosítás!</p>
              </div>
            </div>
          )}
        </div>
        
        {/* Játék vége képernyő */}
        {gameOver && (
          <div className="fixed inset-0 flex items-center justify-center bg-black bg-opacity-50 z-50">
            <div className="bg-white p-6 rounded-lg shadow-lg max-w-md w-full">
              <h2 className="text-2xl font-bold mb-4">Gratulálunk!</h2>
              <p className="mb-4">Sikeresen befejezted a játékot!</p>
              <p className="mb-4">Összes lépés: {moves}</p>
              <div className="flex justify-end">
                <Button onClick={startNewGame}>
                  Új játék
                </Button>
              </div>
            </div>
          </div>
        )}
      </CardContent>
      
      <CardFooter>
        <p className="text-sm text-gray-500">
          Tipp: A dominó csempéknek illeszkedniük kell - az időpontot a megfelelő tevékenységgel kell párosítani.
        </p>
      </CardFooter>
    </Card>
  );
};

export default TimeDomino;