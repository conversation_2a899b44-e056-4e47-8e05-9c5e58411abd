import React, { useState, useEffect, useRef } from 'react';
import { personalPronomenAkkusativDativItems } from '../../data/games/personalPronomenAkkusativDativ';
import { cn } from '../../lib/utils';
import { Button } from '../ui/button';
import { Input } from '../ui/input'; 
import { useToast } from '../ui/use-toast';
import { Badge } from '../ui/badge';
import { textToSpeech } from '../../services/openaiService';
import { Volume2, HelpCircle, Loader2, Info } from 'lucide-react';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "../ui/tooltip";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
  DialogClose,
} from "../ui/dialog";

const PersonalPronomenMixedGame: React.FC = () => {
  const [exercises, setExercises] = useState(personalPronomenAkkusativDativItems);
  const [userAnswers, setUserAnswers] = useState<Record<number, string>>({});
  const [checkedAnswers, setCheckedAnswers] = useState<Record<number, boolean>>({});
  const [showResults, setShowResults] = useState(false);
  const [correctCount, setCorrectCount] = useState(0);
  const [isPlayingAudio, setIsPlayingAudio] = useState<Record<number, boolean>>({});
  const [showHint, setShowHint] = useState<Record<number, boolean>>({});
  const { toast } = useToast();
  const audioRef = useRef<HTMLAudioElement | null>(null);

  // Akkuzatív és datív névmások összehasonlító táblázata
  const pronounComparison = [
    { person: "ich (én)", akkusativ: "mich (engem)", dativ: "mir (nekem)" },
    { person: "du (te)", akkusativ: "dich (téged)", dativ: "dir (neked)" },
    { person: "er (ő - férfi)", akkusativ: "ihn (őt)", dativ: "ihm (neki)" },
    { person: "sie (ő - nő)", akkusativ: "sie (őt)", dativ: "ihr (neki)" },
    { person: "es (az/ő - semleges)", akkusativ: "es (azt/őt)", dativ: "ihm (annak/neki)" },
    { person: "wir (mi)", akkusativ: "uns (minket)", dativ: "uns (nekünk)" },
    { person: "ihr (ti)", akkusativ: "euch (titeket)", dativ: "euch (nektek)" },
    { person: "sie (ők)", akkusativ: "sie (őket)", dativ: "ihnen (nekik)" },
    { person: "Sie (Ön/Önök)", akkusativ: "Sie (Önt/Önöket)", dativ: "Ihnen (Önnek/Önöknek)" }
  ];

  useEffect(() => {
    // Randomize the order of exercises
    setExercises([...personalPronomenAkkusativDativItems].sort(() => Math.random() - 0.5));
    
    // Komponens tisztítása unmount esetén
    return () => {
      if (audioRef.current) {
        audioRef.current.pause();
        if (audioRef.current.src) {
          URL.revokeObjectURL(audioRef.current.src);
        }
      }
    };
  }, []);

  const handleAnswerChange = (index: number, value: string) => {
    setUserAnswers({ ...userAnswers, [index]: value });
  };

  const checkAnswers = () => {
    const newCheckedAnswers: Record<number, boolean> = {};
    let correct = 0;

    exercises.forEach((item, index) => {
      const isCorrect = userAnswers[index]?.toLowerCase() === item.pronoun.toLowerCase();
      newCheckedAnswers[index] = isCorrect;
      if (isCorrect) correct++;
    });

    setCheckedAnswers(newCheckedAnswers);
    setShowResults(true);
    setCorrectCount(correct);

    toast({
      title: `Eredmény: ${correct}/${exercises.length}`,
      description: `${correct} helyes válasz a ${exercises.length}-ból`,
    });
  };

  const resetGame = () => {
    setUserAnswers({});
    setCheckedAnswers({});
    setShowResults(false);
    setShowHint({});
    setExercises([...personalPronomenAkkusativDativItems].sort(() => Math.random() - 0.5));
  };

  const formatSentenceWithBlanks = (sentence: string, position: number, userAnswer?: string) => {
    const words = sentence.split(' ');
    const blankIndex = position;

    return (
      <>
        {words.slice(0, blankIndex).join(' ')}{' '}
        <span className="font-bold underline">
          {userAnswer || '_______'}
        </span>{' '}
        {words.slice(blankIndex + 1).join(' ')}
      </>
    );
  };

  // Mondat felolvasása
  const handleSpeech = async (sentence: string, index: number) => {
    try {
      setIsPlayingAudio({ ...isPlayingAudio, [index]: true });
      
      // Ha van korábbi audio, azt leállítjuk és felszabadítjuk
      if (audioRef.current) {
        audioRef.current.pause();
        URL.revokeObjectURL(audioRef.current.src);
      }

      // A mondat szavai
      const words = sentence.split(' ');
      // Kicseréljük a helyőrzőt a helyes névmással a felolvasáshoz
      const item = exercises[index];
      words[item.position] = item.pronoun;
      // Teljes mondat helyes névmással
      const textToRead = words.join(' ');
      
      // Lekérjük az audio URL-t az OpenAI API-tól
      const audioUrl = await textToSpeech(textToRead, 'de-DE');

      // Létrehozunk egy új audio elemet
      const audio = new Audio(audioUrl);
      audioRef.current = audio;

      // Lejátszás befejezésekor frissítjük az állapotot és felszabadítjuk az erőforrást
      audio.onended = () => {
        setIsPlayingAudio({ ...isPlayingAudio, [index]: false });
      };

      // Lejátszás
      await audio.play();
    } catch (error) {
      console.error('Hiba a szövegfelolvasás során:', error);
      setIsPlayingAudio({ ...isPlayingAudio, [index]: false });
      toast({
        title: "Hiba a felolvasás során",
        description: "Nem sikerült felolvasni a szöveget. Ellenőrizd, hogy van-e elég pontod.",
        variant: "destructive",
      });
    }
  };

  // Segítség mutatása/elrejtése
  const toggleHint = (index: number) => {
    setShowHint({ ...showHint, [index]: !showHint[index] });
  };

  // Segítség generálása
  const getHint = (item: any) => {
    const hints = [];
    
    // Segítség a hint alapján, ha van
    if (item.hint) {
      const hintLower = item.hint.toLowerCase();
      
      if (item.case === 'akkusativ') {
        if (hintLower === 'ich') hints.push('Az "ich" névmás akkuzatívban "mich".');
        else if (hintLower === 'du') hints.push('A "du" névmás akkuzatívban "dich".');
        else if (hintLower === 'er' || hintLower.includes('der')) hints.push('A hímnemű névmás akkuzatívban "ihn".');
        else if (hintLower === 'sie' || hintLower.includes('die')) hints.push('A nőnemű névmás akkuzatívban is "sie".');
        else if (hintLower === 'es' || hintLower.includes('das')) hints.push('A semlegesnemű névmás akkuzatívban is "es".');
        else if (hintLower === 'wir') hints.push('A "wir" névmás akkuzatívban "uns".');
        else if (hintLower === 'ihr') hints.push('Az "ihr" névmás akkuzatívban "euch".');
        else if (hintLower.includes('sie') && (hintLower.includes('több') || hintLower.includes('ők'))) 
          hints.push('A többesszámú "sie" névmás akkuzatívban is "sie".');
        else if (hintLower === 'sie' || hintLower === 'önök') hints.push('A magázó "Sie" névmás akkuzatívban is "Sie".');
      } else {
        if (hintLower === 'ich') hints.push('Az "ich" névmás datívban "mir".');
        else if (hintLower === 'du') hints.push('A "du" névmás datívban "dir".');
        else if (hintLower === 'er' || hintLower.includes('der')) hints.push('A hímnemű névmás datívban "ihm".');
        else if (hintLower === 'sie' || hintLower.includes('die')) hints.push('A nőnemű névmás datívban "ihr".');
        else if (hintLower === 'es' || hintLower.includes('das')) hints.push('A semlegesnemű névmás datívban "ihm".');
        else if (hintLower === 'wir') hints.push('A "wir" névmás datívban "uns".');
        else if (hintLower === 'ihr') hints.push('Az "ihr" névmás datívban "euch".');
        else if (hintLower.includes('sie') && (hintLower.includes('több') || hintLower.includes('ők'))) 
          hints.push('A többesszámú "sie" névmás datívban "ihnen".');
        else if (hintLower === 'sie' || hintLower === 'önök') hints.push('A magázó "Sie" névmás datívban "Ihnen".');
      }
    }
    
    // Általános segítség
    if (item.case === 'akkusativ') {
      hints.push('Az akkuzatív eset a cselekvés közvetlen tárgyát jelöli (kit? mit?).');
    } else {
      hints.push('A datív eset a részeshatározót jelöli (kinek? minek?).');
    }
    
    return hints;
  };

  return (
    <div className="w-full max-w-3xl mx-auto p-4 bg-white rounded-lg shadow-md">
      <div className="flex items-center justify-between mb-6">
        <h2 className="text-2xl font-bold text-center">
          Személyes névmások akkuzatívban és datívban
        </h2>
        
        <Dialog>
          <DialogTrigger asChild>
            <Button variant="ghost" size="icon" className="rounded-full">
              <Info className="h-5 w-5" />
            </Button>
          </DialogTrigger>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>Személyes névmások esetei</DialogTitle>
              <DialogDescription>
                A német nyelvben a személyes névmások alakja megváltozik attól függően, hogy milyen esetben használjuk őket.
              </DialogDescription>
            </DialogHeader>
            <div className="space-y-4 mt-4">
              {/* Névmások táblázata */}
              <div className="border rounded-md overflow-hidden">
                <table className="w-full">
                  <thead>
                    <tr className="bg-gray-100">
                      <th className="px-4 py-2 text-left">Alanyeset</th>
                      <th className="px-4 py-2 text-left">Akkuzatív</th>
                      <th className="px-4 py-2 text-left">Datív</th>
                    </tr>
                  </thead>
                  <tbody>
                    {pronounComparison.map((row, i) => (
                      <tr key={i} className={i % 2 === 0 ? 'bg-white' : 'bg-gray-50'}>
                        <td className="px-4 py-2">{row.person}</td>
                        <td className="px-4 py-2">{row.akkusativ}</td>
                        <td className="px-4 py-2">{row.dativ}</td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
              
              {/* Magyarázat */}
              <div>
                <h3 className="font-medium mb-2">Mikor használjuk?</h3>
                <div className="space-y-2">
                  <div className="p-2 bg-blue-50 rounded">
                    <p className="font-medium text-blue-700">Akkuzatív (4. eset):</p>
                    <p className="text-sm">Tárgyeset, amely a cselekvés közvetlen tárgyát jelöli (kit? mit?).</p>
                    <p className="text-sm">Például: Ich sehe <strong>dich</strong>. (Látlak téged.)</p>
                  </div>
                  
                  <div className="p-2 bg-purple-50 rounded">
                    <p className="font-medium text-purple-700">Datív (3. eset):</p>
                    <p className="text-sm">Részeshatározó, amely azt jelöli, kinek/minek a részére történik a cselekvés (kinek? minek?).</p>
                    <p className="text-sm">Például: Ich gebe <strong>dir</strong> das Buch. (Odaadom neked a könyvet.)</p>
                  </div>
                </div>
              </div>
            </div>
            <DialogClose asChild>
              <Button className="mt-4 w-full">Bezárás</Button>
            </DialogClose>
          </DialogContent>
        </Dialog>
      </div>
      
      <p className="mb-4 text-center">
        Helyettesítsd a hiányzó személyes névmásokat a megfelelő esetben (akkuzatív vagy datív)!
      </p>
      
      <div className="space-y-6">
        {exercises.map((item, index) => (
          <div 
            key={index} 
            className={cn(
              "p-4 rounded-md border",
              showResults && (
                checkedAnswers[index] 
                  ? "border-green-500 bg-green-50" 
                  : "border-red-500 bg-red-50"
              )
            )}
          >
            <div className="flex justify-between items-center mb-3">
              <div className="text-lg">
                {formatSentenceWithBlanks(item.sentence, item.position, userAnswers[index])}
                {item.hint && (
                  <div className="text-sm text-gray-500 mt-1">
                    ({item.hint})
                  </div>
                )}
              </div>
              
              <div className="flex items-center">
                <Badge variant={item.case === 'akkusativ' ? 'outline' : 'secondary'} className="mr-2">
                  {item.case === 'akkusativ' ? 'Akkuzatív' : 'Datív'}
                </Badge>
                
                <div className="flex space-x-2">
                  <TooltipProvider>
                    <Tooltip>
                      <TooltipTrigger asChild>
                        <Button 
                          variant="ghost" 
                          size="icon" 
                          className="h-8 w-8" 
                          onClick={() => handleSpeech(item.sentence, index)}
                          disabled={isPlayingAudio[index]}
                        >
                          {isPlayingAudio[index] ? (
                            <Loader2 className="h-4 w-4 animate-spin" />
                          ) : (
                            <Volume2 className="h-4 w-4" />
                          )}
                        </Button>
                      </TooltipTrigger>
                      <TooltipContent>
                        <p>Mondat meghallgatása (1 pont)</p>
                      </TooltipContent>
                    </Tooltip>
                  </TooltipProvider>
                  
                  <TooltipProvider>
                    <Tooltip>
                      <TooltipTrigger asChild>
                        <Button 
                          variant="ghost" 
                          size="icon" 
                          className="h-8 w-8" 
                          onClick={() => toggleHint(index)}
                        >
                          <HelpCircle className="h-4 w-4" />
                        </Button>
                      </TooltipTrigger>
                      <TooltipContent>
                        <p>Segítség kérése</p>
                      </TooltipContent>
                    </Tooltip>
                  </TooltipProvider>
                </div>
              </div>
            </div>
            
            <div className="flex space-x-2 items-center">
              <Input
                type="text"
                value={userAnswers[index] || ''}
                onChange={(e) => handleAnswerChange(index, e.target.value)}
                placeholder="Írd be a névmást..."
                disabled={showResults}
                className="max-w-xs"
              />
              
              {showResults && (
                <div className={cn(
                  "ml-4 font-medium",
                  checkedAnswers[index] ? "text-green-600" : "text-red-600"
                )}>
                  {checkedAnswers[index] ? (
                    <span>✓ Helyes</span>
                  ) : (
                    <span>✗ Helyes: {item.pronoun}</span>
                  )}
                </div>
              )}
            </div>
            
            {showHint[index] && (
              <div className="mt-2 p-2 bg-blue-50 rounded text-sm">
                <p className="font-medium text-blue-700">Tipp:</p>
                <ul className="list-disc pl-5 text-blue-600">
                  {getHint(item).map((hint, i) => (
                    <li key={i}>{hint}</li>
                  ))}
                </ul>
              </div>
            )}
          </div>
        ))}
      </div>

      <div className="mt-6 flex justify-center space-x-4">
        {!showResults ? (
          <Button onClick={checkAnswers} disabled={Object.keys(userAnswers).length !== exercises.length}>
            Ellenőrzés
          </Button>
        ) : (
          <>
            <div className="text-lg font-medium">
              Eredmény: {correctCount}/{exercises.length} ({Math.round((correctCount / exercises.length) * 100)}%)
            </div>
            <Button onClick={resetGame}>Újrakezdés</Button>
          </>
        )}
      </div>
    </div>
  );
};

export default PersonalPronomenMixedGame;