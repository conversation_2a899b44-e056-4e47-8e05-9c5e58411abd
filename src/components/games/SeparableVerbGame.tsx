import React, { useState, useEffect } from 'react';
import { But<PERSON> } from "../ui/button";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "../ui/card";
import { Badge } from "../ui/badge";
import { Separator } from "../ui/separator";
import { separableVerbs, personPronouns, SeparableVerb } from "../../data/games/separableVerbs";

type GameMode = 'match' | 'complete' | 'order' | 'conjugate';

interface GameState {
  mode: GameMode;
  currentQuestion: any;
  options: string[];
  answer: string | string[];
  userAnswer: string | string[];
  feedback: string;
  isCorrect: boolean | null;
  score: number;
  totalQuestions: number;
  gameOver: boolean;
}

const initialGameState: GameState = {
  mode: 'match',
  currentQuestion: null,
  options: [],
  answer: '',
  userAnswer: '',
  feedback: '',
  isCorrect: null,
  score: 0,
  totalQuestions: 0,
  gameOver: false
};

const SeparableVerbGame: React.FC = () => {
  const [gameState, setGameState] = useState<GameState>(initialGameState);
  const [availableVerbs, setAvailableVerbs] = useState<SeparableVerb[]>([...separableVerbs]);
  const [selectedWords, setSelectedWords] = useState<string[]>([]);

  // Párosítás mód: ige + igekötő párosítása
  const generateMatchQuestion = () => {
    // Ha már nincs több elérhető ige, akkor vége a játéknak
    if (availableVerbs.length === 0) {
      setGameState(prev => ({ ...prev, gameOver: true }));
      return;
    }

    // Véletlenszerűen választunk egy igét
    const randomIndex = Math.floor(Math.random() * availableVerbs.length);
    const selectedVerb = availableVerbs[randomIndex];
    
    // Eltávolítjuk a kiválasztott igét a listából
    const updatedVerbs = [...availableVerbs];
    updatedVerbs.splice(randomIndex, 1);
    setAvailableVerbs(updatedVerbs);

    // Generáljuk a választási lehetőségeket: a helyes igekötő + 3 másik véletlenszerű igekötő
    const allPrefixes = separableVerbs.map(v => v.prefix);
    const uniquePrefixes = [...new Set(allPrefixes)];
    
    // Eltávolítjuk a helyes igekötőt a listából
    const otherPrefixes = uniquePrefixes.filter(p => p !== selectedVerb.prefix);
    
    // Véletlenszerűen választunk 3 másik igekötőt
    const randomPrefixes = [];
    for (let i = 0; i < Math.min(3, otherPrefixes.length); i++) {
      const idx = Math.floor(Math.random() * otherPrefixes.length);
      randomPrefixes.push(otherPrefixes[idx]);
      otherPrefixes.splice(idx, 1);
    }
    
    // Összekeverjük a választási lehetőségeket
    const options = [selectedVerb.prefix, ...randomPrefixes];
    for (let i = options.length - 1; i > 0; i--) {
      const j = Math.floor(Math.random() * (i + 1));
      [options[i], options[j]] = [options[j], options[i]];
    }

    setGameState(prev => ({
      ...prev,
      mode: 'match',
      currentQuestion: selectedVerb,
      options,
      answer: selectedVerb.prefix,
      userAnswer: '',
      feedback: '',
      isCorrect: null,
      totalQuestions: prev.totalQuestions + 1
    }));
  };

  // Kiegészítés mód: mondat kiegészítése elváló igekötős igével
  const generateCompleteQuestion = () => {
    // Ha már nincs több elérhető ige, akkor vége a játéknak
    if (availableVerbs.length === 0) {
      setGameState(prev => ({ ...prev, gameOver: true }));
      return;
    }

    // Véletlenszerűen választunk egy igét
    const randomIndex = Math.floor(Math.random() * availableVerbs.length);
    const selectedVerb = availableVerbs[randomIndex];
    
    // Eltávolítjuk a kiválasztott igét a listából
    const updatedVerbs = [...availableVerbs];
    updatedVerbs.splice(randomIndex, 1);
    setAvailableVerbs(updatedVerbs);

    // Generálunk egy mondatot, ahol az igekötőt kell behelyettesíteni
    const sentence = selectedVerb.example.replace(`${selectedVerb.prefix}`, '___');

    // Generáljuk a választási lehetőségeket
    const allPrefixes = separableVerbs.map(v => v.prefix);
    const uniquePrefixes = [...new Set(allPrefixes)];
    
    // Eltávolítjuk a helyes igekötőt a listából
    const otherPrefixes = uniquePrefixes.filter(p => p !== selectedVerb.prefix);
    
    // Véletlenszerűen választunk 3 másik igekötőt
    const randomPrefixes = [];
    for (let i = 0; i < Math.min(3, otherPrefixes.length); i++) {
      const idx = Math.floor(Math.random() * otherPrefixes.length);
      randomPrefixes.push(otherPrefixes[idx]);
      otherPrefixes.splice(idx, 1);
    }
    
    // Összekeverjük a választási lehetőségeket
    const options = [selectedVerb.prefix, ...randomPrefixes];
    for (let i = options.length - 1; i > 0; i--) {
      const j = Math.floor(Math.random() * (i + 1));
      [options[i], options[j]] = [options[j], options[i]];
    }

    setGameState(prev => ({
      ...prev,
      mode: 'complete',
      currentQuestion: { ...selectedVerb, sentence },
      options,
      answer: selectedVerb.prefix,
      userAnswer: '',
      feedback: '',
      isCorrect: null,
      totalQuestions: prev.totalQuestions + 1
    }));
  };

  // Sorrendbe rendezés mód: az ige, igekötő és vonzat helyes sorrendbe rendezése a mondatban
  const generateOrderQuestion = () => {
    // Ha már nincs több elérhető ige, akkor vége a játéknak
    if (availableVerbs.length === 0) {
      setGameState(prev => ({ ...prev, gameOver: true }));
      return;
    }

    // Véletlenszerűen választunk egy igét
    const randomIndex = Math.floor(Math.random() * availableVerbs.length);
    const selectedVerb = availableVerbs[randomIndex];
    
    // Eltávolítjuk a kiválasztott igét a listából
    const updatedVerbs = [...availableVerbs];
    updatedVerbs.splice(randomIndex, 1);
    setAvailableVerbs(updatedVerbs);

    // Felbontjuk a példamondatot szavakra és összekeverjük
    const words = selectedVerb.example.split(' ');
    const shuffledWords = [...words];
    for (let i = shuffledWords.length - 1; i > 0; i--) {
      const j = Math.floor(Math.random() * (i + 1));
      [shuffledWords[i], shuffledWords[j]] = [shuffledWords[j], shuffledWords[i]];
    }

    setSelectedWords([]);
    
    setGameState(prev => ({
      ...prev,
      mode: 'order',
      currentQuestion: selectedVerb,
      options: shuffledWords,
      answer: words,
      userAnswer: [],
      feedback: '',
      isCorrect: null,
      totalQuestions: prev.totalQuestions + 1
    }));
  };

  // Ragozás mód: az elváló igekötős ige helyes ragozása adott személyben
  const generateConjugateQuestion = () => {
    // Ha már nincs több elérhető ige, akkor vége a játéknak
    if (availableVerbs.length === 0) {
      setGameState(prev => ({ ...prev, gameOver: true }));
      return;
    }

    // Véletlenszerűen választunk egy igét
    const randomIndex = Math.floor(Math.random() * availableVerbs.length);
    const selectedVerb = availableVerbs[randomIndex];
    
    // Eltávolítjuk a kiválasztott igét a listából
    const updatedVerbs = [...availableVerbs];
    updatedVerbs.splice(randomIndex, 1);
    setAvailableVerbs(updatedVerbs);

    // Véletlenszerűen választunk egy személyt
    const randomPerson = personPronouns[Math.floor(Math.random() * personPronouns.length)];

    // A helyes válasz igeragozástól függ (egyszerűsített verzió)
    let correctForm;
    if (randomPerson === 'ich') correctForm = `${randomPerson} ${selectedVerb.verb}e`;
    else if (randomPerson === 'du') correctForm = `${randomPerson} ${selectedVerb.verb}st`;
    else if (randomPerson === 'er/sie/es') correctForm = `${randomPerson} ${selectedVerb.verb}t`;
    else if (randomPerson === 'wir') correctForm = `${randomPerson} ${selectedVerb.verb}en`;
    else if (randomPerson === 'ihr') correctForm = `${randomPerson} ${selectedVerb.verb}t`;
    else correctForm = `${randomPerson} ${selectedVerb.verb}en`;

    // Generáljuk a választási lehetőségeket
    const options = [
      correctForm,
      `${randomPerson} ${selectedVerb.verb}`,
      `${randomPerson} ${selectedVerb.verb}en`,
      `${randomPerson} ${selectedVerb.verb}st`
    ];

    // Összekeverjük a választási lehetőségeket
    for (let i = options.length - 1; i > 0; i--) {
      const j = Math.floor(Math.random() * (i + 1));
      [options[i], options[j]] = [options[j], options[i]];
    }

    setGameState(prev => ({
      ...prev,
      mode: 'conjugate',
      currentQuestion: { ...selectedVerb, person: randomPerson },
      options,
      answer: correctForm,
      userAnswer: '',
      feedback: '',
      isCorrect: null,
      totalQuestions: prev.totalQuestions + 1
    }));
  };

  // Választ ellenőriz
  const checkAnswer = (selectedAnswer: string | string[]) => {
    const { mode, answer } = gameState;
    let isCorrect = false;

    if (mode === 'match' || mode === 'complete' || mode === 'conjugate') {
      isCorrect = selectedAnswer === answer;
    } else if (mode === 'order') {
      // Sorrendbe rendezésnél az összes szónak a helyes sorrendben kell lennie
      isCorrect = JSON.stringify(selectedAnswer) === JSON.stringify(answer);
    }

    setGameState(prev => ({
      ...prev,
      userAnswer: selectedAnswer,
      isCorrect,
      feedback: isCorrect ? 'Richtig!' : 'Falsch!',
      score: isCorrect ? prev.score + 1 : prev.score
    }));
  };
  
  // Szó kiválasztása a szórendi játékban
  const selectWord = (word: string) => {
    if (gameState.userAnswer !== '') return; // Ha már van válasz, nem lehet tovább választani
    
    // Ha a szó már ki van választva, akkor töröljük
    const wordIndex = selectedWords.indexOf(word);
    if (wordIndex !== -1) {
      const newSelectedWords = [...selectedWords];
      newSelectedWords.splice(wordIndex, 1);
      setSelectedWords(newSelectedWords);
    } else {
      // Ha nincs kiválasztva, akkor hozzáadjuk
      setSelectedWords([...selectedWords, word]);
    }
  };

  // Ellenőrzi a szórendi játék válaszát
  const checkOrderAnswer = () => {
    checkAnswer(selectedWords);
  };

  // Következő feladat generálása
  const nextQuestion = () => {
    // Véletlenszerűen választunk egy játékmódot
    const modes: GameMode[] = ['match', 'complete', 'order', 'conjugate'];
    const randomMode = modes[Math.floor(Math.random() * modes.length)];

    // Generáljuk a következő feladatot a választott mód alapján
    switch (randomMode) {
      case 'match':
        generateMatchQuestion();
        break;
      case 'complete':
        generateCompleteQuestion();
        break;
      case 'order':
        generateOrderQuestion();
        break;
      case 'conjugate':
        generateConjugateQuestion();
        break;
      default:
        generateMatchQuestion();
    }
  };

  // Játék újraindítása
  const restartGame = () => {
    setAvailableVerbs([...separableVerbs]);
    setGameState(initialGameState);
    setSelectedWords([]);
    nextQuestion();
  };

  // Kezdeti feladat generálása
  useEffect(() => {
    nextQuestion();
  }, []);

  // Játék megjelenítése
  return (
    <Card className="w-full max-w-2xl mx-auto">
      <CardHeader>
        <CardTitle>Elváló igekötős igék gyakorlása</CardTitle>
        <CardDescription>
          Tanuld meg a német elváló igekötős igéket játékos módon!
        </CardDescription>
      </CardHeader>
      
      <CardContent className="space-y-4">
        {gameState.gameOver ? (
          // Játék vége képernyő
          <div className="text-center space-y-4">
            <h3 className="text-xl font-bold">Játék vége!</h3>
            <p>Pontszámod: {gameState.score} / {gameState.totalQuestions}</p>
            <Button onClick={restartGame}>Újrakezdés</Button>
          </div>
        ) : (
          // Aktuális feladat megjelenítése
          <>
            {/* Feladat típus szerint különböző megjelenítés */}
            {gameState.mode === 'match' && gameState.currentQuestion && (
              <div className="space-y-4">
                <div className="text-center text-lg font-medium">
                  Melyik igekötő tartozik ehhez az igéhez?
                </div>
                
                <div className="flex items-center justify-center gap-2">
                  <Badge variant="outline" className="px-4 py-2 text-lg">{gameState.currentQuestion.verb}</Badge>
                  <span className="font-bold">+</span>
                  <div className="w-24 h-8 border-2 border-dashed border-gray-300 rounded-md flex items-center justify-center">
                    {gameState.userAnswer && <Badge>{gameState.userAnswer as string}</Badge>}
                  </div>
                  <span className="text-gray-400">=</span>
                  <Badge className="px-4 py-2 text-lg">{gameState.currentQuestion.prefix + gameState.currentQuestion.verb}</Badge>
                </div>
                
                <div className="bg-gray-50 p-3 rounded-lg">
                  <p className="font-medium">Jelentés: {gameState.currentQuestion.meaning}</p>
                  <p className="text-sm text-gray-600">Példa: {gameState.currentQuestion.example}</p>
                </div>
                
                <div className="grid grid-cols-2 gap-2">
                  {gameState.options.map((option, index) => (
                    <Button
                      key={index}
                      variant={gameState.userAnswer === option 
                        ? (gameState.isCorrect ? "default" : "destructive") 
                        : "outline"}
                      disabled={gameState.userAnswer !== ''}
                      onClick={() => checkAnswer(option)}
                      className="h-12 text-lg"
                    >
                      {option}
                    </Button>
                  ))}
                </div>
              </div>
            )}
            
            {gameState.mode === 'complete' && gameState.currentQuestion && (
              <div className="space-y-4">
                <div className="text-center text-lg font-medium">
                  Egészítsd ki a mondatot a megfelelő igekötővel!
                </div>
                
                <div className="text-center text-lg p-4 bg-gray-50 rounded-lg">
                  {gameState.currentQuestion.sentence.split('___').map((part, i, arr) => (
                    <React.Fragment key={i}>
                      {part}
                      {i < arr.length - 1 && (
                        gameState.userAnswer 
                          ? <span className={`font-bold ${gameState.isCorrect ? 'text-green-600' : 'text-red-600'}`}>
                              {gameState.userAnswer as string}
                            </span>
                          : <span className="border-b-2 border-dashed border-gray-400 inline-block w-12"></span>
                      )}
                    </React.Fragment>
                  ))}
                </div>
                
                <div className="bg-gray-50 p-3 rounded-lg">
                  <p className="font-medium">Ige: {gameState.currentQuestion.prefix + gameState.currentQuestion.verb}</p>
                  <p className="text-sm text-gray-600">Jelentés: {gameState.currentQuestion.meaning}</p>
                </div>
                
                <div className="grid grid-cols-2 gap-2">
                  {gameState.options.map((option, index) => (
                    <Button
                      key={index}
                      variant={gameState.userAnswer === option 
                        ? (gameState.isCorrect ? "default" : "destructive") 
                        : "outline"}
                      disabled={gameState.userAnswer !== ''}
                      onClick={() => checkAnswer(option)}
                      className="h-12 text-lg"
                    >
                      {option}
                    </Button>
                  ))}
                </div>
              </div>
            )}
            
            {gameState.mode === 'order' && gameState.currentQuestion && (
              <div className="space-y-4">
                <div className="text-center text-lg font-medium">
                  Rendezd a szavakat helyes sorrendbe!
                </div>
                
                <div className="bg-gray-50 p-3 rounded-lg">
                  <p className="font-medium">Ige: {gameState.currentQuestion.prefix + gameState.currentQuestion.verb}</p>
                  <p className="text-sm text-gray-600">Jelentés: {gameState.currentQuestion.meaning}</p>
                </div>
                
                <div className="min-h-16 p-3 border-2 border-dashed border-gray-300 rounded-lg flex flex-wrap gap-2">
                  {selectedWords.map((word, index) => (
                    <Badge 
                      key={index} 
                      variant="secondary"
                      className="px-3 py-1.5 text-base cursor-pointer"
                      onClick={() => selectWord(word)}
                    >
                      {word}
                    </Badge>
                  ))}
                </div>
                
                <div className="flex flex-wrap gap-2">
                  {gameState.options.map((word, index) => (
                    <Badge 
                      key={index} 
                      variant={selectedWords.includes(word) ? "outline" : "default"}
                      className={`px-3 py-1.5 text-base cursor-pointer ${selectedWords.includes(word) ? 'opacity-50' : ''}`}
                      onClick={() => selectWord(word)}
                    >
                      {word}
                    </Badge>
                  ))}
                </div>
                
                <Button 
                  onClick={checkOrderAnswer} 
                  disabled={selectedWords.length !== gameState.options.length || gameState.userAnswer !== ''}
                  className="w-full"
                >
                  Ellenőrzés
                </Button>
                
                {gameState.isCorrect !== null && (
                  <div className={`p-3 rounded-lg ${gameState.isCorrect ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}`}>
                    {gameState.isCorrect ? 'Helyes! A mondat sorrendje megfelelő.' : 'Helytelen! A helyes sorrend:'}
                    {!gameState.isCorrect && (
                      <div className="font-medium mt-2">
                        {(gameState.answer as string[]).join(' ')}
                      </div>
                    )}
                  </div>
                )}
              </div>
            )}
            
            {gameState.mode === 'conjugate' && gameState.currentQuestion && (
              <div className="space-y-4">
                <div className="text-center text-lg font-medium">
                  Válaszd ki a helyesen ragozott igealakot!
                </div>
                
                <div className="bg-gray-50 p-3 rounded-lg">
                  <p className="font-medium">Igekötős ige: {gameState.currentQuestion.prefix + gameState.currentQuestion.verb}</p>
                  <p className="text-sm">Jelentés: {gameState.currentQuestion.meaning}</p>
                  <p className="text-sm mt-2">Ragozd ezt a személyt: <strong>{gameState.currentQuestion.person}</strong></p>
                </div>
                
                <div className="grid grid-cols-1 gap-2">
                  {gameState.options.map((option, index) => (
                    <Button
                      key={index}
                      variant={gameState.userAnswer === option 
                        ? (gameState.isCorrect ? "default" : "destructive") 
                        : "outline"}
                      disabled={gameState.userAnswer !== ''}
                      onClick={() => checkAnswer(option)}
                      className="h-12 text-lg justify-start"
                    >
                      {option}
                    </Button>
                  ))}
                </div>
              </div>
            )}
            
            {/* Visszajelzés és pontozás */}
            {gameState.feedback && (
              <div className={`p-3 rounded-lg text-center font-medium ${
                gameState.isCorrect ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
              }`}>
                {gameState.feedback}
              </div>
            )}
            
            <div className="flex justify-between items-center">
              <div>
                <span className="font-medium">Pontszám:</span> {gameState.score} / {gameState.totalQuestions}
              </div>
              
              {gameState.userAnswer !== '' && (
                <Button onClick={nextQuestion}>
                  Következő feladat
                </Button>
              )}
            </div>
          </>
        )}
      </CardContent>
      
      <CardFooter>
        <Button onClick={restartGame} variant="outline" className="w-full">
          Újrakezdés
        </Button>
      </CardFooter>
    </Card>
  );
};

export default SeparableVerbGame;