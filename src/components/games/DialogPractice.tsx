import React, { useState, useEffect } from 'react';
import { But<PERSON> } from "../ui/button";
import { Card, CardContent, CardD<PERSON><PERSON>, CardFooter, CardHeader, CardTitle } from "../ui/card";

interface DialogLine {
  speaker: 'user' | 'other';
  text: string;
}

interface DialogOption {
  text: string;
  nextId: number;
  isCorrect: boolean;
}

interface DialogStep {
  id: number;
  lines: DialogLine[];
  options: DialogOption[];
  feedback?: {
    correct: string;
    incorrect: string;
  };
}

interface DialogScenario {
  title: string;
  description: string;
  steps: DialogStep[];
}

// Példa dialógus szcenárió - Hobby és szabadidő témakörben
const hobbyScenario: DialogScenario = {
  title: 'Hobbi és szabadid<PERSON>',
  description: '<PERSON><PERSON><PERSON><PERSON><PERSON>s egy új baráttal a hobbijairól',
  steps: [
    {
      id: 1,
      lines: [
        { speaker: 'other', text: 'Hall<PERSON>! Ich bin <PERSON>. Wie heißt du?' },
      ],
      options: [
        { text: 'Ich heiße... Freut mich, dich kennenzulernen!', nextId: 2, isCorrect: true },
        { text: 'Mein Name ist... Wie geht\'s dir?', nextId: 2, isCorrect: true },
        { text: 'Du heißt Marie.', nextId: 2, isCorrect: false },
      ],
      feedback: {
        correct: 'Szép bemutatkozás!',
        incorrect: 'Nem válaszoltál a kérdésre. Amikor valaki bemutatkozik, illik visszabemutatkozni.'
      }
    },
    {
      id: 2,
      lines: [
        { speaker: 'other', text: 'Was machst du in deiner Freizeit?' },
      ],
      options: [
        { text: 'In meiner Freizeit spiele ich gern Fußball.', nextId: 3, isCorrect: true },
        { text: 'Ich lese gern Bücher und höre Musik.', nextId: 3, isCorrect: true },
        { text: 'Ich habe keine Freizeit.', nextId: 3, isCorrect: true },
        { text: 'Freizeit ist nicht wichtig.', nextId: 3, isCorrect: false },
      ],
      feedback: {
        correct: 'Nagyszerű! Megosztottad a hobbidat.',
        incorrect: 'Ez nem válasz a kérdésre. A kérdező a szabadidős tevékenységeidre kíváncsi.'
      }
    },
    {
      id: 3,
      lines: [
        { speaker: 'other', text: 'Ich spiele Tennis und gehe oft schwimmen. Spielst du auch Tennis?' },
      ],
      options: [
        { text: 'Ja, ich spiele auch Tennis. Wann spielst du?', nextId: 4, isCorrect: true },
        { text: 'Nein, ich spiele kein Tennis, aber ich schwimme auch gern.', nextId: 4, isCorrect: true },
        { text: 'Tennis ist langweilig.', nextId: 4, isCorrect: false },
      ],
      feedback: {
        correct: 'Jól van! Válaszoltál a kérdésre és folytatod a beszélgetést.',
        incorrect: 'Ez udvariatlan válasz. Még ha nem is szereted a teniszt, próbálj udvariasan válaszolni.'
      }
    },
    {
      id: 4,
      lines: [
        { speaker: 'other', text: 'Ich spiele immer am Wochenende. Hast du am Samstag Zeit?' },
      ],
      options: [
        { text: 'Ja, am Samstag habe ich Zeit. Wann und wo treffen wir uns?', nextId: 5, isCorrect: true },
        { text: 'Nein, am Samstag habe ich leider keine Zeit. Wie wäre es am Sonntag?', nextId: 5, isCorrect: true },
        { text: 'Samstag ist schlecht.', nextId: 5, isCorrect: false },
      ],
      feedback: {
        correct: 'Kitűnő! Reagáltál a javaslatra és folytatod a beszélgetést.',
        incorrect: 'Ez túl rövid és udvariatlan válasz. Adj több információt vagy kérdezz vissza.'
      }
    },
    {
      id: 5,
      lines: [
        { speaker: 'other', text: 'Super! Wir können uns um 14 Uhr am Tennisplatz im Park treffen. Kennst du den?' },
      ],
      options: [
        { text: 'Ja, ich kenne den Tennisplatz. Bis Samstag um 14 Uhr!', nextId: 6, isCorrect: true },
        { text: 'Nein, ich kenne den Platz nicht. Kannst du mir die Adresse schicken?', nextId: 6, isCorrect: true },
        { text: 'Das ist zu früh für mich.', nextId: 6, isCorrect: false },
      ],
      feedback: {
        correct: 'Remek! A beszélgetés sikeresen lezárult.',
        incorrect: 'Jobb lenne, ha konkrétabb lennél az időponttal kapcsolatban, vagy alternatívát javasolnál.'
      }
    },
    {
      id: 6,
      lines: [
        { speaker: 'other', text: 'Alles klar! Ich freue mich schon. Bis dann!' },
      ],
      options: [
        { text: 'Ich freue mich auch! Bis dann!', nextId: 7, isCorrect: true },
        { text: 'Tschüss!', nextId: 7, isCorrect: true },
        { text: 'OK.', nextId: 7, isCorrect: false },
      ],
      feedback: {
        correct: 'Nagyszerű! Sikeresen lezártad a beszélgetést.',
        incorrect: 'Ez túl rövid és távolságtartó válasz. Légy kicsit lelkesebb!'
      }
    },
    {
      id: 7,
      lines: [
        { speaker: 'other', text: 'Gratulálok! Sikeresen befejezted a gyakorlatot!' },
      ],
      options: [
        { text: 'Újrakezdés', nextId: 1, isCorrect: true },
      ],
      feedback: {
        correct: '',
        incorrect: ''
      }
    }
  ]
};

// Időpont egyeztetési szcenárió
const timeScenario: DialogScenario = {
  title: 'Időpont egyeztetés',
  description: 'Találkozó megbeszélése egy baráttal',
  steps: [
    {
      id: 1,
      lines: [
        { speaker: 'other', text: 'Hallo! Wie geht es dir?' },
      ],
      options: [
        { text: 'Hallo! Mir geht es gut, danke. Und dir?', nextId: 2, isCorrect: true },
        { text: 'Gut, danke.', nextId: 2, isCorrect: true },
        { text: 'Was willst du?', nextId: 2, isCorrect: false },
      ],
      feedback: {
        correct: 'Jó kezdés!',
        incorrect: 'Ez udvariatlannak tűnik. Próbálj barátságosabb lenni a beszélgetés kezdetén.'
      }
    },
    {
      id: 2,
      lines: [
        { speaker: 'other', text: 'Danke, mir geht es auch gut! Hast du Lust, diese Woche etwas zu unternehmen?' },
      ],
      options: [
        { text: 'Ja, gerne! Was möchtest du machen?', nextId: 3, isCorrect: true },
        { text: 'Klingt gut! Wann hast du Zeit?', nextId: 3, isCorrect: true },
        { text: 'Diese Woche ist schlecht.', nextId: 3, isCorrect: false },
      ],
      feedback: {
        correct: 'Nagyszerű! Nyitott vagy a találkozóra.',
        incorrect: 'Ez a válasz túl elutasító. Ha nem érsz rá, javasolj másik időpontot.'
      }
    },
    {
      id: 3,
      lines: [
        { speaker: 'other', text: 'Wir könnten ins Kino gehen oder einen Kaffee trinken. Was denkst du?' },
      ],
      options: [
        { text: 'Ins Kino klingt gut! Welchen Film möchtest du sehen?', nextId: 4, isCorrect: true },
        { text: 'Ich würde lieber einen Kaffee trinken. Kennst du ein gutes Café?', nextId: 4, isCorrect: true },
        { text: 'Beides langweilig.', nextId: 4, isCorrect: false },
      ],
      feedback: {
        correct: 'Jól van! Választottál egy programot.',
        incorrect: 'Ez udvariatlan válasz. Ha egyik javaslat sem tetszik, illik alternatívát javasolni.'
      }
    },
    {
      id: 4,
      lines: [
        { speaker: 'other', text: 'Super! Wann passt es dir? Ich habe am Dienstag und Freitag Zeit.' },
      ],
      options: [
        { text: 'Dienstag passt mir gut. Um wie viel Uhr?', nextId: 5, isCorrect: true },
        { text: 'Freitag ist besser für mich. Hast du am Nachmittag Zeit?', nextId: 5, isCorrect: true },
        { text: 'Ich kann nur am Wochenende.', nextId: 5, isCorrect: true },
        { text: 'Dienstag und Freitag sind schlecht.', nextId: 5, isCorrect: false },
      ],
      feedback: {
        correct: 'Kiváló! Konkrét időpontot javasoltál vagy alternatívát kínáltál.',
        incorrect: 'Ha egyik nap sem jó, javasolj konkrét alternatívát.'
      }
    },
    {
      id: 5,
      lines: [
        { speaker: 'other', text: 'Am Dienstag um 18 Uhr? Treffen wir uns vor dem Kino/Café?' },
      ],
      options: [
        { text: 'Ja, Dienstag um 18 Uhr passt perfekt. Vor dem Kino/Café ist gut.', nextId: 6, isCorrect: true },
        { text: '18 Uhr ist etwas zu früh für mich. Können wir uns um 19 Uhr treffen?', nextId: 6, isCorrect: true },
        { text: 'Nein.', nextId: 6, isCorrect: false },
      ],
      feedback: {
        correct: 'Nagyszerű! Az időpont egyeztetés sikeres.',
        incorrect: 'Ez túl rövid válasz. Ha az időpont nem megfelelő, javasolj másikat.'
      }
    },
    {
      id: 6,
      lines: [
        { speaker: 'other', text: 'Alles klar! Dann sehen wir uns am Dienstag. Ich freue mich schon!' },
      ],
      options: [
        { text: 'Ich freue mich auch! Bis Dienstag!', nextId: 7, isCorrect: true },
        { text: 'Super, bis dann!', nextId: 7, isCorrect: true },
        { text: 'OK.', nextId: 7, isCorrect: false },
      ],
      feedback: {
        correct: 'Remek! Sikeresen lezártad a beszélgetést.',
        incorrect: 'Ez túl rövid és távolságtartó válasz. Mutass több lelkesedést!'
      }
    },
    {
      id: 7,
      lines: [
        { speaker: 'other', text: 'Gratulálok! Sikeresen befejezted a gyakorlatot!' },
      ],
      options: [
        { text: 'Újrakezdés', nextId: 1, isCorrect: true },
      ],
      feedback: {
        correct: '',
        incorrect: ''
      }
    }
  ]
};

// Az összes elérhető szcenárió
const scenarios: DialogScenario[] = [
  hobbyScenario,
  timeScenario
];

const DialogPractice: React.FC = () => {
  const [selectedScenario, setSelectedScenario] = useState<DialogScenario | null>(null);
  const [currentStep, setCurrentStep] = useState<DialogStep | null>(null);
  const [conversation, setConversation] = useState<DialogLine[]>([]);
  const [feedbackMessage, setFeedbackMessage] = useState<string | null>(null);
  const [feedbackType, setFeedbackType] = useState<'correct' | 'incorrect' | null>(null);
  const [completedSteps, setCompletedSteps] = useState<number>(0);
  const [score, setScore] = useState<number>(0);

  // Szcenárió kiválasztása
  const selectScenario = (scenario: DialogScenario) => {
    setSelectedScenario(scenario);
    const firstStep = scenario.steps.find(step => step.id === 1);
    if (firstStep) {
      setCurrentStep(firstStep);
      setConversation([...firstStep.lines]);
    }
    setCompletedSteps(0);
    setScore(0);
    setFeedbackMessage(null);
    setFeedbackType(null);
  };

  // Válasz választása
  const selectOption = (option: DialogOption) => {
    if (!currentStep || !selectedScenario) return;
    
    // Felhasználó válaszának hozzáadása a beszélgetéshez
    const updatedConversation = [
      ...conversation,
      { speaker: 'user', text: option.text }
    ];
    
    setConversation(updatedConversation);
    
    // Pontszám és visszajelzés frissítése
    if (option.isCorrect) {
      setScore(score + 1);
      if (currentStep.feedback) {
        setFeedbackMessage(currentStep.feedback.correct);
        setFeedbackType('correct');
      }
    } else {
      if (currentStep.feedback) {
        setFeedbackMessage(currentStep.feedback.incorrect);
        setFeedbackType('incorrect');
      }
    }
    
    setCompletedSteps(completedSteps + 1);
    
    // Késleltetés a következő lépésre, hogy a felhasználó lássa a saját válaszát
    setTimeout(() => {
      // Következő lépés keresése
      const nextStep = selectedScenario.steps.find(step => step.id === option.nextId);
      
      if (nextStep) {
        // Beszélgetés frissítése az új lépés soraival
        const newConversation = [
          ...updatedConversation,
          ...nextStep.lines
        ];
        
        setConversation(newConversation);
        setCurrentStep(nextStep);
        setFeedbackMessage(null);
        setFeedbackType(null);
      }
    }, 1000);
  };

  // Újrakezdés
  const restartDialog = () => {
    if (selectedScenario) {
      selectScenario(selectedScenario);
    }
  };

  // Vissza a szcenárió választáshoz
  const backToScenarios = () => {
    setSelectedScenario(null);
    setCurrentStep(null);
    setConversation([]);
    setFeedbackMessage(null);
    setFeedbackType(null);
    setCompletedSteps(0);
    setScore(0);
  };

  return (
    <Card className="w-full max-w-2xl mx-auto">
      <CardHeader>
        <CardTitle>
          {selectedScenario ? selectedScenario.title : 'Dialógus gyakorlatok'}
        </CardTitle>
        <CardDescription>
          {selectedScenario 
            ? selectedScenario.description 
            : 'Válassz egy beszélgetési szituációt a gyakorláshoz'}
        </CardDescription>
      </CardHeader>
      
      <CardContent className="space-y-4">
        {!selectedScenario ? (
          // Szcenárió kiválasztása
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {scenarios.map((scenario, index) => (
              <Button 
                key={index} 
                onClick={() => selectScenario(scenario)}
                variant="outline"
                className="h-24 flex flex-col justify-center items-center space-y-2 p-4"
              >
                <div className="font-bold">{scenario.title}</div>
                <div className="text-xs text-center">{scenario.description}</div>
              </Button>
            ))}
          </div>
        ) : (
          // Dialógus megjelenítése
          <div className="space-y-4">
            <div className="space-y-2 max-h-80 overflow-y-auto p-2 border rounded-lg">
              {conversation.map((line, index) => (
                <div 
                  key={index} 
                  className={`p-2 rounded-lg ${
                    line.speaker === 'user' 
                      ? 'bg-blue-100 ml-8' 
                      : 'bg-gray-100 mr-8'
                  }`}
                >
                  {line.text}
                </div>
              ))}
            </div>
            
            {feedbackMessage && (
              <div className={`p-3 rounded-lg ${
                feedbackType === 'correct' 
                  ? 'bg-green-100 text-green-800' 
                  : 'bg-red-100 text-red-800'
              }`}>
                {feedbackMessage}
              </div>
            )}
            
            {currentStep && currentStep.options.length > 0 && (
              <div className="space-y-2">
                <p className="font-medium">Válassz egy választ:</p>
                <div className="space-y-2">
                  {currentStep.options.map((option, index) => (
                    <Button
                      key={index}
                      onClick={() => selectOption(option)}
                      variant="outline"
                      className="w-full text-left justify-start h-auto py-3"
                    >
                      {option.text}
                    </Button>
                  ))}
                </div>
              </div>
            )}
            
            <div className="text-right text-sm">
              <span className="font-medium">Pontszám:</span> {score} / {completedSteps}
            </div>
          </div>
        )}
      </CardContent>
      
      <CardFooter className="flex justify-between">
        {selectedScenario ? (
          <>
            <Button onClick={backToScenarios} variant="outline">
              Vissza a témákhoz
            </Button>
            <Button onClick={restartDialog}>
              Újrakezdés
            </Button>
          </>
        ) : (
          <div className="w-full text-center text-gray-500">
            Válassz egy témát a fentiek közül a kezdéshez!
          </div>
        )}
      </CardFooter>
    </Card>
  );
};

export default DialogPractice;