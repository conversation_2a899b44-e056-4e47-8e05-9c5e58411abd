import React, { useState, useEffect, useRef } from 'react';
import { bestimmterArtikelItems } from '../../data/games/bestimmterArtikel';
import { cn } from '../../lib/utils';
import { Button } from '../ui/button';
import { useToast } from '../ui/use-toast';
import { textToSpeech } from '../../services/openaiService';
import { Volume2, HelpCircle, Loader2, Info } from 'lucide-react';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "../ui/tooltip";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
  DialogClose,
} from "../ui/dialog";

export const BestimmterArtikelGame: React.FC = () => {
  const [items, setItems] = useState(bestimmterArtikelItems);
  const [userAnswers, setUserAnswers] = useState<Record<number, string>>({});
  const [checkedAnswers, setCheckedAnswers] = useState<Record<number, boolean>>({});
  const [showResults, setShowResults] = useState(false);
  const [correctCount, setCorrectCount] = useState(0);
  const [isPlayingAudio, setIsPlayingAudio] = useState<Record<number, boolean>>({});
  const [showHint, setShowHint] = useState<Record<number, boolean>>({});
  const { toast } = useToast();
  const audioRef = useRef<HTMLAudioElement | null>(null);

  // Segítség információk a névelőkhöz
  const articleHints = {
    der: {
      title: "Der (hímnem)",
      examples: ["der Mann (a férfi)", "der Tisch (az asztal)", "der Löffel (a kanál)"],
      rules: [
        "Legtöbb férfit jelölő főnév",
        "Évszakok, hónapok, napok",
        "Legtöbb -er végződésű főnév"
      ]
    },
    die: {
      title: "Die (nőnem)",
      examples: ["die Frau (a nő)", "die Tasche (a táska)", "die Schule (az iskola)"],
      rules: [
        "Legtöbb nőt jelölő főnév",
        "Legtöbb -ung, -heit, -keit, -schaft, -ion végződésű főnév",
        "Legtöbb -e végződésű főnév"
      ]
    },
    das: {
      title: "Das (semlegesnem)",
      examples: ["das Kind (a gyerek)", "das Buch (a könyv)", "das Fenster (az ablak)"],
      rules: [
        "Legtöbb kicsinyítő képzős -chen, -lein végződésű főnév",
        "Legtöbb állat- és gyümölcsnév",
        "Legtöbb -um, -ment végződésű főnév"
      ]
    }
  };

  useEffect(() => {
    // Randomize the order of items
    setItems([...bestimmterArtikelItems].sort(() => Math.random() - 0.5));
    
    // Komponens tisztítása unmount esetén
    return () => {
      if (audioRef.current) {
        audioRef.current.pause();
        if (audioRef.current.src) {
          URL.revokeObjectURL(audioRef.current.src);
        }
      }
    };
  }, []);

  const handleAnswerChange = (index: number, value: string) => {
    setUserAnswers({ ...userAnswers, [index]: value });
  };

  const checkAnswers = () => {
    const newCheckedAnswers: Record<number, boolean> = {};
    let correct = 0;

    items.forEach((item, index) => {
      const isCorrect = userAnswers[index] === item.article;
      newCheckedAnswers[index] = isCorrect;
      if (isCorrect) correct++;
    });

    setCheckedAnswers(newCheckedAnswers);
    setShowResults(true);
    setCorrectCount(correct);

    toast({
      title: `Eredmény: ${correct}/${items.length}`,
      description: `${correct} helyes válasz a ${items.length}-ból`,
    });
  };

  const resetGame = () => {
    setUserAnswers({});
    setCheckedAnswers({});
    setShowResults(false);
    setShowHint({});
    setItems([...bestimmterArtikelItems].sort(() => Math.random() - 0.5));
  };

  // Szó felolvasása
  const handleSpeech = async (word: string, index: number) => {
    try {
      setIsPlayingAudio({ ...isPlayingAudio, [index]: true });
      
      // Ha van korábbi audio, azt leállítjuk és felszabadítjuk
      if (audioRef.current) {
        audioRef.current.pause();
        URL.revokeObjectURL(audioRef.current.src);
      }

      // Lekérjük az audio URL-t az OpenAI API-tól
      const text = word; // A szó, amit fel akarunk olvasni
      const audioUrl = await textToSpeech(text, 'de-DE');

      // Létrehozunk egy új audio elemet
      const audio = new Audio(audioUrl);
      audioRef.current = audio;

      // Lejátszás befejezésekor frissítjük az állapotot és felszabadítjuk az erőforrást
      audio.onended = () => {
        setIsPlayingAudio({ ...isPlayingAudio, [index]: false });
      };

      // Lejátszás
      await audio.play();
    } catch (error) {
      console.error('Hiba a szövegfelolvasás során:', error);
      setIsPlayingAudio({ ...isPlayingAudio, [index]: false });
      toast({
        title: "Hiba a felolvasás során",
        description: "Nem sikerült felolvasni a szöveget. Ellenőrizd, hogy van-e elég pontod.",
        variant: "destructive",
      });
    }
  };

  // Segítség mutatása
  const toggleHint = (index: number) => {
    setShowHint({ ...showHint, [index]: !showHint[index] });
  };

  return (
    <div className="w-full max-w-3xl mx-auto p-4 bg-white rounded-lg shadow-md">
      <div className="flex items-center justify-between mb-6">
        <h2 className="text-2xl font-bold text-center">Határozott névelő gyakorlása</h2>
        
        <Dialog>
          <DialogTrigger asChild>
            <Button variant="ghost" size="icon" className="rounded-full">
              <Info className="h-5 w-5" />
            </Button>
          </DialogTrigger>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>Határozott névelők a németben</DialogTitle>
              <DialogDescription>
                A német nyelvben három határozott névelő van: der (hímnem), die (nőnem) és das (semlegesnem).
              </DialogDescription>
            </DialogHeader>
            <div className="space-y-4 mt-4">
              {Object.entries(articleHints).map(([article, info]) => (
                <div key={article} className="border rounded p-3">
                  <h3 className="font-bold mb-2">{info.title}</h3>
                  <p className="text-sm mb-2">Példák: {info.examples.join(", ")}</p>
                  <p className="text-sm font-medium mt-2">Főbb szabályok:</p>
                  <ul className="text-sm list-disc pl-5">
                    {info.rules.map((rule, i) => (
                      <li key={i}>{rule}</li>
                    ))}
                  </ul>
                </div>
              ))}
            </div>
            <DialogClose asChild>
              <Button className="mt-4 w-full">Bezárás</Button>
            </DialogClose>
          </DialogContent>
        </Dialog>
      </div>
      
      <p className="mb-4 text-center">Válaszd ki a megfelelő névelőt (der, die, das) minden főnévhez!</p>
      
      <div className="space-y-4">
        {items.map((item, index) => (
          <div 
            key={index} 
            className={cn(
              "p-4 rounded-md border",
              showResults && (
                checkedAnswers[index] 
                  ? "border-green-500 bg-green-50" 
                  : "border-red-500 bg-red-50"
              )
            )}
          >
            <div className="flex items-center justify-between mb-2">
              <div className="flex items-center">
                <div className="flex space-x-3 mr-4">
                  {['der', 'die', 'das'].map((article) => (
                    <label 
                      key={article} 
                      className={cn(
                        "flex items-center space-x-2 cursor-pointer",
                        showResults && article === item.article && "font-bold text-green-600"
                      )}
                    >
                      <input
                        type="radio"
                        name={`article-${index}`}
                        value={article}
                        checked={userAnswers[index] === article}
                        onChange={() => handleAnswerChange(index, article)}
                        disabled={showResults}
                        className="h-4 w-4"
                      />
                      <span>{article}</span>
                    </label>
                  ))}
                </div>
                <span className="font-medium">{item.word}</span>
              </div>
              
              <div className="flex space-x-2">
                <TooltipProvider>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <Button 
                        variant="ghost" 
                        size="icon" 
                        className="h-8 w-8" 
                        onClick={() => handleSpeech(item.word, index)}
                        disabled={isPlayingAudio[index]}
                      >
                        {isPlayingAudio[index] ? (
                          <Loader2 className="h-4 w-4 animate-spin" />
                        ) : (
                          <Volume2 className="h-4 w-4" />
                        )}
                      </Button>
                    </TooltipTrigger>
                    <TooltipContent>
                      <p>Kiejtés meghallgatása (1 pont)</p>
                    </TooltipContent>
                  </Tooltip>
                </TooltipProvider>
                
                <TooltipProvider>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <Button 
                        variant="ghost" 
                        size="icon" 
                        className="h-8 w-8" 
                        onClick={() => toggleHint(index)}
                      >
                        <HelpCircle className="h-4 w-4" />
                      </Button>
                    </TooltipTrigger>
                    <TooltipContent>
                      <p>Segítség kérése</p>
                    </TooltipContent>
                  </Tooltip>
                </TooltipProvider>
              </div>
            </div>

            {showHint[index] && (
              <div className="mt-2 p-2 bg-blue-50 rounded text-sm">
                <p className="font-medium text-blue-700">Tipp:</p>
                <ul className="list-disc pl-5 text-blue-600">
                  <li>
                    {item.article === 'der' && 'A legtöbb hímnemű főnévnek "der" a névelője.'}
                    {item.article === 'die' && 'A legtöbb nőnemű főnévnek "die" a névelője.'}
                    {item.article === 'das' && 'A legtöbb semlegesnemű főnévnek "das" a névelője.'}
                  </li>
                  <li>
                    {item.article === 'der' && '-er végződésű főnevek gyakran hímneműek.'}
                    {item.article === 'die' && '-ung, -heit, -keit végződésű főnevek általában nőneműek.'}
                    {item.article === 'das' && '-chen, -lein kicsinyítő képzős főnevek mindig semlegesneműek.'}
                  </li>
                </ul>
              </div>
            )}
            
            {showResults && !checkedAnswers[index] && (
              <div className="mt-2 text-sm text-red-600">
                Helyes válasz: <span className="font-medium">{item.article}</span>
              </div>
            )}
          </div>
        ))}
      </div>

      <div className="mt-6 flex justify-center space-x-4">
        {!showResults ? (
          <Button onClick={checkAnswers} disabled={Object.keys(userAnswers).length !== items.length}>
            Ellenőrzés
          </Button>
        ) : (
          <>
            <div className="text-lg font-medium">
              Eredmény: {correctCount}/{items.length} ({Math.round((correctCount / items.length) * 100)}%)
            </div>
            <Button onClick={resetGame}>Újrakezdés</Button>
          </>
        )}
      </div>
    </div>
  );
};

export default BestimmterArtikelGame;