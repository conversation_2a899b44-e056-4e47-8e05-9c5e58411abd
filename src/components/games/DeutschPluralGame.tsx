import React, { useState, useEffect, useRef } from 'react';
import { deutschPluralItems } from '../../data/games/deutschPlural';
import { cn } from '../../lib/utils';
import { Button } from '../ui/button';
import { Input } from '../ui/input';
import { useToast } from '../ui/use-toast';
import { textToSpeech } from '../../services/openaiService';
import { Volume2, HelpCircle, Loader2, Info } from 'lucide-react';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "../ui/tooltip";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
  DialogClose,
} from "../ui/dialog";

export const DeutschPluralGame: React.FC = () => {
  const [items, setItems] = useState(deutschPluralItems);
  const [userArticles, setUserArticles] = useState<Record<number, string>>({});
  const [userPlurals, setUserPlurals] = useState<Record<number, string>>({});
  const [checkedArticles, setCheckedArticles] = useState<Record<number, boolean>>({});
  const [checkedPlurals, setCheckedPlurals] = useState<Record<number, boolean>>({});
  const [showResults, setShowResults] = useState(false);
  const [correctCount, setCorrectCount] = useState(0);
  const [isPlayingAudio, setIsPlayingAudio] = useState<Record<number, boolean>>({});
  const [showHint, setShowHint] = useState<Record<number, boolean>>({});
  const { toast } = useToast();
  const audioRef = useRef<HTMLAudioElement | null>(null);

  // A német többesszám képzésére vonatkozó szabályok
  const pluralRules = [
    {
      title: "-e végződés",
      description: "Sok főnév a többesszámban -e végződést kap, különösen az egyszótagú főnevek.",
      examples: ["der Tag → die Tage", "der Tisch → die Tische", "das Jahr → die Jahre"]
    },
    {
      title: "-er végződés",
      description: "Számos semlegesnemű és néhány hímnemű főnév -er végződést kap, gyakran Umlaut-tal (magánhangzó-változással).",
      examples: ["das Kind → die Kinder", "das Bild → die Bilder", "der Mann → die Männer"]
    },
    {
      title: "-n/-en végződés",
      description: "A legtöbb nőnemű és -e-re végződő hímnemű főnév -n vagy -en végződést kap.",
      examples: ["die Frau → die Frauen", "die Tasche → die Taschen", "der Junge → die Jungen"]
    },
    {
      title: "-s végződés",
      description: "Idegen eredetű szavak és rövidítések gyakran -s végződést kapnak.",
      examples: ["das Auto → die Autos", "das Handy → die Handys", "das Hobby → die Hobbys"]
    },
    {
      title: "Magánhangzó-változás (Umlaut)",
      description: "Sok főnév esetében az a, o, u magánhangzó ä, ö, ü-re változik a többesszámban.",
      examples: ["der Apfel → die Äpfel", "die Mutter → die Mütter", "der Garten → die Gärten"]
    },
    {
      title: "Változatlan forma",
      description: "Egyes főnevek többesszámban nem változnak, csak a névelő lesz többesszámú (die).",
      examples: ["der Lehrer → die Lehrer", "das Fenster → die Fenster", "der Koffer → die Koffer"]
    }
  ];

  // Segítségnyújtó tippek a különböző típusú főnevek többesszámához
  const getHint = (word: string, article: string, plural: string) => {
    // Meghatározzuk a többesszám típusát
    let hintType = "";
    const lowerWord = word.toLowerCase();
    const lowerPlural = plural.toLowerCase();

    if (lowerPlural.endsWith("e") && !lowerWord.endsWith("e")) {
      hintType = "-e";
    } else if (lowerPlural.endsWith("er") && !lowerWord.endsWith("er")) {
      hintType = "-er";
    } else if ((lowerPlural.endsWith("n") || lowerPlural.endsWith("en")) && 
              (!lowerWord.endsWith("n") && !lowerWord.endsWith("en"))) {
      hintType = "-n/-en";
    } else if (lowerPlural.endsWith("s") && !lowerWord.endsWith("s")) {
      hintType = "-s";
    } else if (lowerPlural === lowerWord) {
      hintType = "változatlan";
    }

    // Ellenőrizzük a magánhangzó-változást (Umlaut)
    const hasUmlaut = (
      (lowerPlural.includes("ä") && lowerWord.includes("a")) ||
      (lowerPlural.includes("ö") && lowerWord.includes("o")) ||
      (lowerPlural.includes("ü") && lowerWord.includes("u"))
    );

    // Létrehozzuk a tippet
    let hints = [];
    
    // Névelővel kapcsolatos tipp
    hints.push(`A "${article} ${word}" többesszáma mindig "die"-vel kezdődik.`);
    
    // Végződéssel kapcsolatos tipp
    if (hintType === "-e") {
      hints.push("Egyszótagú főnevek gyakran -e végződést kapnak a többesszámban.");
    } else if (hintType === "-er") {
      hints.push("Semlegesnemű és egyes hímnemű főnevek gyakran -er végződést kapnak.");
    } else if (hintType === "-n/-en") {
      hints.push("Nőnemű főnevek és -e végződésű hímnemű főnevek gyakran -n vagy -en végződést kapnak.");
    } else if (hintType === "-s") {
      hints.push("Idegen eredetű szavak gyakran -s végződést kapnak a többesszámban.");
    } else if (hintType === "változatlan") {
      hints.push("Egyes főnevek alakja nem változik a többesszámban, csak a névelő lesz \"die\".");
    }
    
    // Umlaut-tal kapcsolatos tipp
    if (hasUmlaut) {
      hints.push("Figyelj a magánhangzó-változásra (a→ä, o→ö, u→ü) a többesszámban.");
    }

    return hints;
  };

  useEffect(() => {
    // Randomize the order of items
    setItems([...deutschPluralItems].sort(() => Math.random() - 0.5));
    
    // Komponens tisztítása unmount esetén
    return () => {
      if (audioRef.current) {
        audioRef.current.pause();
        if (audioRef.current.src) {
          URL.revokeObjectURL(audioRef.current.src);
        }
      }
    };
  }, []);

  const handleArticleChange = (index: number, value: string) => {
    setUserArticles({ ...userArticles, [index]: value });
  };

  const handlePluralChange = (index: number, value: string) => {
    setUserPlurals({ ...userPlurals, [index]: value });
  };

  const checkAnswers = () => {
    const newCheckedArticles: Record<number, boolean> = {};
    const newCheckedPlurals: Record<number, boolean> = {};
    let correct = 0;
    let total = items.length * 2; // Article and plural for each item

    items.forEach((item, index) => {
      const isArticleCorrect = userArticles[index]?.toLowerCase() === item.article.toLowerCase();
      const isPluralCorrect = userPlurals[index]?.toLowerCase() === item.plural.toLowerCase();
      
      newCheckedArticles[index] = isArticleCorrect;
      newCheckedPlurals[index] = isPluralCorrect;
      
      if (isArticleCorrect) correct++;
      if (isPluralCorrect) correct++;
    });

    setCheckedArticles(newCheckedArticles);
    setCheckedPlurals(newCheckedPlurals);
    setShowResults(true);
    setCorrectCount(correct);

    toast({
      title: `Eredmény: ${correct}/${total}`,
      description: `${correct} helyes válasz a ${total}-ból`,
    });
  };

  const resetGame = () => {
    setUserArticles({});
    setUserPlurals({});
    setCheckedArticles({});
    setCheckedPlurals({});
    setShowResults(false);
    setShowHint({});
    setItems([...deutschPluralItems].sort(() => Math.random() - 0.5));
  };

  const allFieldsCompleted = () => {
    return items.every((_, index) => 
      userArticles[index] && userPlurals[index]
    );
  };

  // Szó felolvasása
  const handleSpeech = async (word: string, index: number) => {
    try {
      setIsPlayingAudio({ ...isPlayingAudio, [index]: true });
      
      // Ha van korábbi audio, azt leállítjuk és felszabadítjuk
      if (audioRef.current) {
        audioRef.current.pause();
        URL.revokeObjectURL(audioRef.current.src);
      }

      // Lekérjük az audio URL-t az OpenAI API-tól
      const text = word; // A szó, amit fel akarunk olvasni
      const audioUrl = await textToSpeech(text, 'de-DE');

      // Létrehozunk egy új audio elemet
      const audio = new Audio(audioUrl);
      audioRef.current = audio;

      // Lejátszás befejezésekor frissítjük az állapotot és felszabadítjuk az erőforrást
      audio.onended = () => {
        setIsPlayingAudio({ ...isPlayingAudio, [index]: false });
      };

      // Lejátszás
      await audio.play();
    } catch (error) {
      console.error('Hiba a szövegfelolvasás során:', error);
      setIsPlayingAudio({ ...isPlayingAudio, [index]: false });
      toast({
        title: "Hiba a felolvasás során",
        description: "Nem sikerült felolvasni a szöveget. Ellenőrizd, hogy van-e elég pontod.",
        variant: "destructive",
      });
    }
  };

  // Többesszám felolvasása
  const handlePluralSpeech = async (plural: string, index: number) => {
    try {
      setIsPlayingAudio({ ...isPlayingAudio, [`plural-${index}`]: true });
      
      // Ha van korábbi audio, azt leállítjuk és felszabadítjuk
      if (audioRef.current) {
        audioRef.current.pause();
        URL.revokeObjectURL(audioRef.current.src);
      }

      // Lekérjük az audio URL-t az OpenAI API-tól
      const audioUrl = await textToSpeech(plural, 'de-DE');

      // Létrehozunk egy új audio elemet
      const audio = new Audio(audioUrl);
      audioRef.current = audio;

      // Lejátszás befejezésekor frissítjük az állapotot és felszabadítjuk az erőforrást
      audio.onended = () => {
        setIsPlayingAudio({ ...isPlayingAudio, [`plural-${index}`]: false });
      };

      // Lejátszás
      await audio.play();
    } catch (error) {
      console.error('Hiba a szövegfelolvasás során:', error);
      setIsPlayingAudio({ ...isPlayingAudio, [`plural-${index}`]: false });
      toast({
        title: "Hiba a felolvasás során",
        description: "Nem sikerült felolvasni a szöveget. Ellenőrizd, hogy van-e elég pontod.",
        variant: "destructive",
      });
    }
  };

  // Segítség mutatása
  const toggleHint = (index: number) => {
    setShowHint({ ...showHint, [index]: !showHint[index] });
  };

  return (
    <div className="w-full max-w-3xl mx-auto p-4 bg-white rounded-lg shadow-md">
      <div className="flex items-center justify-between mb-6">
        <h2 className="text-2xl font-bold text-center">Német főnevek többesszáma</h2>
        
        <Dialog>
          <DialogTrigger asChild>
            <Button variant="ghost" size="icon" className="rounded-full">
              <Info className="h-5 w-5" />
            </Button>
          </DialogTrigger>
          <DialogContent className="max-h-[80vh] overflow-y-auto">
            <DialogHeader>
              <DialogTitle>Többesszám a német nyelvben</DialogTitle>
              <DialogDescription>
                A német főnevek többesszáma különböző szabályokat követ. Íme a legfontosabb szabályok:
              </DialogDescription>
            </DialogHeader>
            <div className="space-y-4 mt-4">
              {pluralRules.map((rule, index) => (
                <div key={index} className="border rounded p-3">
                  <h3 className="font-bold mb-2">{rule.title}</h3>
                  <p className="text-sm mb-2">{rule.description}</p>
                  <p className="text-sm font-medium mt-2">Példák:</p>
                  <ul className="text-sm list-disc pl-5">
                    {rule.examples.map((example, i) => (
                      <li key={i}>{example}</li>
                    ))}
                  </ul>
                </div>
              ))}
            </div>
            <DialogClose asChild>
              <Button className="mt-4 w-full">Bezárás</Button>
            </DialogClose>
          </DialogContent>
        </Dialog>
      </div>
      
      <p className="mb-4 text-center">Add meg a megfelelő névelőt (der, die, das) és többesszámot minden főnévhez!</p>
      <p className="mb-6 text-center text-sm text-gray-600">Minta: der Arzt, die Ärzte</p>
      
      <div className="space-y-6">
        {items.map((item, index) => (
          <div key={index} className="border rounded-md p-4">
            <div className="flex justify-end mb-2 space-x-2">
              <TooltipProvider>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <Button 
                      variant="ghost" 
                      size="icon" 
                      className="h-8 w-8" 
                      onClick={() => handleSpeech(item.word, index)}
                      disabled={isPlayingAudio[index]}
                    >
                      {isPlayingAudio[index] ? (
                        <Loader2 className="h-4 w-4 animate-spin" />
                      ) : (
                        <Volume2 className="h-4 w-4" />
                      )}
                    </Button>
                  </TooltipTrigger>
                  <TooltipContent>
                    <p>Kiejtés meghallgatása (1 pont)</p>
                  </TooltipContent>
                </Tooltip>
              </TooltipProvider>
              
              <TooltipProvider>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <Button 
                      variant="ghost" 
                      size="icon" 
                      className="h-8 w-8" 
                      onClick={() => toggleHint(index)}
                    >
                      <HelpCircle className="h-4 w-4" />
                    </Button>
                  </TooltipTrigger>
                  <TooltipContent>
                    <p>Segítség kérése</p>
                  </TooltipContent>
                </Tooltip>
              </TooltipProvider>
            </div>
            
            <div className="grid grid-cols-3 gap-4">
              <div 
                className={cn(
                  "col-span-1",
                  showResults && (
                    checkedArticles[index] 
                      ? "text-green-600" 
                      : "text-red-600"
                  )
                )}
              >
                <label className="block text-sm font-medium mb-1">Névelő</label>
                <div className="flex space-x-3">
                  {['der', 'die', 'das'].map((article) => (
                    <label 
                      key={article} 
                      className={cn(
                        "flex items-center space-x-2 cursor-pointer",
                        showResults && article === item.article && "font-bold text-green-600"
                      )}
                    >
                      <input
                        type="radio"
                        name={`article-${index}`}
                        value={article}
                        checked={userArticles[index] === article}
                        onChange={() => handleArticleChange(index, article)}
                        disabled={showResults}
                        className="h-4 w-4"
                      />
                      <span>{article}</span>
                    </label>
                  ))}
                </div>
                
                {showResults && !checkedArticles[index] && (
                  <div className="mt-1 text-sm text-red-600">
                    Helyes: <span className="font-medium">{item.article}</span>
                  </div>
                )}
              </div>
              
              <div className="col-span-1 flex items-center">
                <span className="font-medium text-lg">{item.word}</span>
              </div>
              
              <div 
                className={cn(
                  "col-span-1",
                  showResults && (
                    checkedPlurals[index] 
                      ? "text-green-600" 
                      : "text-red-600"
                  )
                )}
              >
                <div className="flex justify-between items-center">
                  <label className="block text-sm font-medium mb-1">Többesszám</label>
                  {showResults && (
                    <TooltipProvider>
                      <Tooltip>
                        <TooltipTrigger asChild>
                          <Button 
                            variant="ghost" 
                            size="icon" 
                            className="h-8 w-8 -mt-1" 
                            onClick={() => handlePluralSpeech(item.plural, index)}
                            disabled={isPlayingAudio[`plural-${index}`]}
                          >
                            {isPlayingAudio[`plural-${index}`] ? (
                              <Loader2 className="h-4 w-4 animate-spin" />
                            ) : (
                              <Volume2 className="h-4 w-4" />
                            )}
                          </Button>
                        </TooltipTrigger>
                        <TooltipContent>
                          <p>Többesszám kiejtése (1 pont)</p>
                        </TooltipContent>
                      </Tooltip>
                    </TooltipProvider>
                  )}
                </div>
                <Input
                  type="text"
                  value={userPlurals[index] || ''}
                  onChange={(e) => handlePluralChange(index, e.target.value)}
                  placeholder="pl. die Ärzte"
                  disabled={showResults}
                  className={cn(
                    showResults && (
                      checkedPlurals[index]
                        ? "border-green-500 text-green-600"
                        : "border-red-500 text-red-600"
                    )
                  )}
                />
                
                {showResults && !checkedPlurals[index] && (
                  <div className="mt-1 text-sm text-red-600">
                    Helyes: <span className="font-medium">{item.plural}</span>
                  </div>
                )}
              </div>
            </div>
            
            {showHint[index] && (
              <div className="mt-4 p-3 bg-blue-50 rounded text-sm">
                <p className="font-medium text-blue-700">Tippek:</p>
                <ul className="list-disc pl-5 text-blue-600">
                  {getHint(item.word, item.article, item.plural).map((hint, i) => (
                    <li key={i}>{hint}</li>
                  ))}
                </ul>
              </div>
            )}
          </div>
        ))}
      </div>

      <div className="mt-6 flex justify-center space-x-4">
        {!showResults ? (
          <Button onClick={checkAnswers} disabled={!allFieldsCompleted()}>
            Ellenőrzés
          </Button>
        ) : (
          <>
            <div className="text-lg font-medium">
              Eredmény: {correctCount}/{items.length * 2} ({Math.round((correctCount / (items.length * 2)) * 100)}%)
            </div>
            <Button onClick={resetGame}>Újrakezdés</Button>
          </>
        )}
      </div>
    </div>
  );
};

export default DeutschPluralGame;