import React, { useState, useEffect } from 'react';
import { Button } from "../ui/button";
import { Input } from "../ui/input";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "../ui/card";

interface TimeAnswerType {
  formal: string;
  informal: string[];
}

const TimeExercise: React.FC = () => {
  const [hours, setHours] = useState(12);
  const [minutes, setMinutes] = useState(0);
  const [userAnswer, setUserAnswer] = useState('');
  const [feedback, setFeedback] = useState('');
  const [correctAnswer, setCorrectAnswer] = useState<TimeAnswerType>({ formal: '', informal: [] });
  const [showAnswer, setShowAnswer] = useState(false);
  const [score, setScore] = useState(0);
  const [attempts, setAttempts] = useState(0);

  // Formális idő formázása (pl. "14:30 Uhr")
  const formatFormalTime = (h: number, m: number): string => {
    return `${h.toString().padStart(2, '0')}:${m.toString().padStart(2, '0')} Uhr`;
  };

  // Informális idő generálása németül
  const getGermanTimeDescription = (h: number, m: number): string[] => {
    const answers: string[] = [];
    
    // Óra átalakítása 12 órás formátumba a beszélt némethez
    let hour = h;
    if (hour > 12) hour -= 12;
    if (hour === 0) hour = 12;
    
    // Különböző időkifejezések generálása
    if (m === 0) {
      answers.push(`${hour} Uhr`);
    } else if (m === 30) {
      const nextHour = hour === 12 ? 1 : hour + 1;
      answers.push(`halb ${nextHour}`);
    } else if (m === 15) {
      answers.push(`Viertel nach ${hour}`);
      answers.push(`viertel nach ${hour}`);
    } else if (m === 45) {
      const nextHour = hour === 12 ? 1 : hour + 1;
      answers.push(`Viertel vor ${nextHour}`);
      answers.push(`viertel vor ${nextHour}`);
    } else if (m < 30) {
      answers.push(`${m} Minuten nach ${hour}`);
      answers.push(`${m} nach ${hour}`);
    } else if (m > 30) {
      const minutesToNextHour = 60 - m;
      const nextHour = hour === 12 ? 1 : hour + 1;
      answers.push(`${minutesToNextHour} Minuten vor ${nextHour}`);
      answers.push(`${minutesToNextHour} vor ${nextHour}`);
    }
    
    return answers;
  };

  // Véletlenszerű időpont generálása
  const generateRandomTime = () => {
    // Véletlenszerű óra (0-23) és perc (0, 15, 30, 45 - negyedórák)
    const newHours = Math.floor(Math.random() * 24);
    const minuteOptions = [0, 15, 30, 45];
    const newMinutes = minuteOptions[Math.floor(Math.random() * 4)];
    
    setHours(newHours);
    setMinutes(newMinutes);
    setUserAnswer('');
    setFeedback('');
    setShowAnswer(false);
    
    // Helyes válaszok beállítása
    setCorrectAnswer({
      formal: formatFormalTime(newHours, newMinutes),
      informal: getGermanTimeDescription(newHours, newMinutes)
    });
  };

  // Válasz ellenőrzése
  const checkAnswer = () => {
    setAttempts(attempts + 1);
    
    // Kisbetűs összehasonlítás
    const normalizedUserAnswer = userAnswer.trim().toLowerCase();
    
    // Formális idő ellenőrzése
    if (normalizedUserAnswer === correctAnswer.formal.toLowerCase()) {
      setFeedback('Richtig! (Formale Zeit)');
      setScore(score + 1);
      return;
    }
    
    // Informális idő ellenőrzése (több helyes válasz is lehet)
    const isCorrectInformal = correctAnswer.informal.some(
      answer => normalizedUserAnswer === answer.toLowerCase()
    );
    
    if (isCorrectInformal) {
      setFeedback('Richtig! (Informale Zeit)');
      setScore(score + 1);
    } else {
      setFeedback('Nicht richtig. Versuche es noch einmal!');
    }
  };

  // Komponens betöltésekor generálunk egy új időpontot
  useEffect(() => {
    generateRandomTime();
  }, []);

  // Óra mutatók szögeinek számítása
  const hourDegrees = (hours % 12) * 30 + minutes * 0.5;
  const minuteDegrees = minutes * 6;

  return (
    <Card className="w-full max-w-md mx-auto">
      <CardHeader>
        <CardTitle>Időpont-gyakorló</CardTitle>
        <CardDescription>
          Nézd meg az órát és írd le az időpontot németül! Használhatsz formális (pl. "14:30 Uhr") 
          vagy informális (pl. "halb drei") formát.
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Óra megjelenítése */}
        <div className="relative w-48 h-48 mx-auto rounded-full border-2 border-gray-300 bg-white">
          {/* Óraszámok */}
          {[...Array(12)].map((_, i) => {
            const angle = ((i + 1) * 30 * Math.PI) / 180;
            const x = 24 * Math.sin(angle);
            const y = -24 * Math.cos(angle);
            return (
              <div
                key={i}
                className="absolute font-bold text-sm"
                style={{
                  left: `calc(50% + ${72 * Math.sin(angle)}px - 8px)`,
                  top: `calc(50% - ${72 * Math.cos(angle)}px - 8px)`,
                }}
              >
                {i + 1}
              </div>
            );
          })}
          
          {/* Óra mutató */}
          <div
            className="absolute left-1/2 top-1/2 w-1 h-16 bg-black origin-bottom rounded-full"
            style={{
              transform: `translateX(-50%) rotate(${hourDegrees}deg)`,
              transformOrigin: 'bottom',
            }}
          ></div>
          
          {/* Perc mutató */}
          <div
            className="absolute left-1/2 top-1/2 w-0.5 h-20 bg-black origin-bottom rounded-full"
            style={{
              transform: `translateX(-50%) rotate(${minuteDegrees}deg)`,
              transformOrigin: 'bottom',
            }}
          ></div>
          
          {/* Középpont */}
          <div className="absolute left-1/2 top-1/2 transform -translate-x-1/2 -translate-y-1/2 w-3 h-3 rounded-full bg-red-500"></div>
        </div>

        {/* Digitális idő megjelenítése tanulási célból, ha szükséges */}
        <div className="text-center text-lg font-medium mt-2">
          {showAnswer && (
            <div className="text-gray-700">
              <p>Formális: {correctAnswer.formal}</p>
              <p>Informális: {correctAnswer.informal.join(' / ')}</p>
            </div>
          )}
        </div>

        {/* Válasz beviteli mező */}
        <Input
          type="text"
          placeholder="Írd le az időt németül..."
          value={userAnswer}
          onChange={(e) => setUserAnswer(e.target.value)}
          onKeyDown={(e) => {
            if (e.key === 'Enter') checkAnswer();
          }}
          className="mt-4"
        />

        {/* Visszajelzés */}
        {feedback && (
          <div className={`text-center font-medium ${feedback.includes('Richtig') ? 'text-green-600' : 'text-red-600'}`}>
            {feedback}
          </div>
        )}

        {/* Pontszám */}
        <div className="text-center">
          <p>Pontszám: {score} / {attempts}</p>
        </div>
      </CardContent>
      <CardFooter className="flex justify-between">
        <Button onClick={() => setShowAnswer(!showAnswer)}>
          {showAnswer ? 'Válasz elrejtése' : 'Megoldás mutatása'}
        </Button>
        <Button onClick={checkAnswer} disabled={!userAnswer.trim()}>
          Ellenőrzés
        </Button>
        <Button onClick={generateRandomTime} variant="outline">
          Új időpont
        </Button>
      </CardFooter>
    </Card>
  );
};

export default TimeExercise;