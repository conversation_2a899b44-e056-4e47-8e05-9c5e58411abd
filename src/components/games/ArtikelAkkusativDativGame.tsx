import React, { useState, useEffect, useRef } from 'react';
import { bestimmterArtikelAkkusativItems } from '../../data/games/bestimmterArtikelAkkusativ';
import { bestimmterArtikelDativItems } from '../../data/games/bestimmterArtikelDativ';
import { cn } from '../../lib/utils';
import { Button } from '../ui/button';
import { useToast } from '../ui/use-toast';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '../ui/tabs';
import { textToSpeech } from '../../services/openaiService';
import { Volume2, HelpCircle, Loader2, Info } from 'lucide-react';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "../ui/tooltip";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
  DialogClose,
} from "../ui/dialog";

interface ArtikelExerciseProps {
  type: 'akkusativ' | 'dativ';
}

const ArtikelAkkusativDativGame: React.FC<ArtikelExerciseProps> = ({ type }) => {
  const items = type === 'akkusativ' ? bestimmterArtikelAkkusativItems : bestimmterArtikelDativItems;
  const options = type === 'akkusativ' ? ['den', 'die', 'das'] : ['dem', 'der', 'den'];
  
  const [exercises, setExercises] = useState(items);
  const [userAnswers, setUserAnswers] = useState<Record<number, string>>({});
  const [checkedAnswers, setCheckedAnswers] = useState<Record<number, boolean>>({});
  const [showResults, setShowResults] = useState(false);
  const [correctCount, setCorrectCount] = useState(0);
  const [isPlayingAudio, setIsPlayingAudio] = useState<Record<number, boolean>>({});
  const [showHint, setShowHint] = useState<Record<number, boolean>>({});
  const { toast } = useToast();
  const audioRef = useRef<HTMLAudioElement | null>(null);

  // Információk az akkuzatív és datív esetekről
  const caseInfo = {
    akkusativ: {
      title: "Akkuzatív eset (Akkusativ)",
      description: "Az akkuzatív eset a cselekvés közvetlen tárgyát jelöli.",
      rules: [
        "Az akkuzatívban a hímnemű névelő 'der' → 'den'-re változik.",
        "A nőnemű névelő 'die' és a semlegesnemű névelő 'das' változatlan marad.",
        "Többesszámban a 'die' változatlan marad."
      ],
      examples: [
        "Ich sehe den Mann. (Látom a férfit.)",
        "Ich kaufe die Tasche. (Megveszem a táskát.)",
        "Er liest das Buch. (Olvassa a könyvet.)"
      ]
    },
    dativ: {
      title: "Datív eset (Dativ)",
      description: "A datív eset általában a részeshatározót jelöli, és bizonyos elöljárószavak és igék után használjuk.",
      rules: [
        "A datívban a hímnemű névelő 'der' → 'dem'-re változik.",
        "A nőnemű névelő 'die' → 'der'-re változik.",
        "A semlegesnemű névelő 'das' → 'dem'-re változik.",
        "Többesszámban a 'die' → 'den'-re változik."
      ],
      examples: [
        "Ich gebe dem Mann das Buch. (Odaadom a férfinak a könyvet.)",
        "Das Geschenk gefällt der Frau. (Az ajándék tetszik a nőnek.)",
        "Wir helfen dem Kind. (Segítünk a gyereknek.)",
        "Ich schreibe den Freunden. (Írok a barátoknak.)"
      ]
    }
  };

  // Példák a gyakran használt akkuzatív és datív igékre
  const commonVerbs = {
    akkusativ: [
      "haben (birtokolni)",
      "kaufen (vásárolni)",
      "brauchen (szüksége van)",
      "sehen (látni)",
      "finden (találni)",
      "hören (hallani)",
      "lesen (olvasni)",
      "trinken (inni)",
      "essen (enni)",
      "suchen (keresni)"
    ],
    dativ: [
      "helfen (segíteni)",
      "danken (megköszönni)",
      "glauben (hinni)",
      "gefallen (tetszeni)",
      "passen (illeni)",
      "gehören (tartozni)",
      "folgen (követni)",
      "antworten (válaszolni)",
      "gratulieren (gratulálni)",
      "zuhören (meghallgatni)"
    ]
  };

  useEffect(() => {
    // Randomize the order of exercises
    setExercises([...items].sort(() => Math.random() - 0.5));
    setUserAnswers({});
    setCheckedAnswers({});
    setShowResults(false);
    setShowHint({});
    setCorrectCount(0);
    
    // Komponens tisztítása unmount esetén
    return () => {
      if (audioRef.current) {
        audioRef.current.pause();
        if (audioRef.current.src) {
          URL.revokeObjectURL(audioRef.current.src);
        }
      }
    };
  }, [type, items]);

  const handleAnswerChange = (index: number, value: string) => {
    setUserAnswers({ ...userAnswers, [index]: value });
  };

  const checkAnswers = () => {
    const newCheckedAnswers: Record<number, boolean> = {};
    let correct = 0;

    exercises.forEach((item, index) => {
      const isCorrect = userAnswers[index] === item.article;
      newCheckedAnswers[index] = isCorrect;
      if (isCorrect) correct++;
    });

    setCheckedAnswers(newCheckedAnswers);
    setShowResults(true);
    setCorrectCount(correct);

    toast({
      title: `Eredmény: ${correct}/${exercises.length}`,
      description: `${correct} helyes válasz a ${exercises.length}-ból`,
    });
  };

  const resetGame = () => {
    setUserAnswers({});
    setCheckedAnswers({});
    setShowResults(false);
    setShowHint({});
    setExercises([...items].sort(() => Math.random() - 0.5));
  };

  const formatSentenceWithBlanks = (sentence: string, position: number, userAnswer?: string) => {
    const words = sentence.split(' ');
    const blankIndex = position;

    return (
      <>
        {words.slice(0, blankIndex).join(' ')}{' '}
        <span className="font-bold">
          {userAnswer || '_____'}
        </span>{' '}
        {words.slice(blankIndex + 1).join(' ')}
      </>
    );
  };

  // Mondat felolvasása
  const handleSpeech = async (sentence: string, index: number) => {
    try {
      setIsPlayingAudio({ ...isPlayingAudio, [index]: true });
      
      // Ha van korábbi audio, azt leállítjuk és felszabadítjuk
      if (audioRef.current) {
        audioRef.current.pause();
        URL.revokeObjectURL(audioRef.current.src);
      }

      // A mondat szavai
      const words = sentence.split(' ');
      // Kicseréljük a helyőrzőt a helyes névelővel a felolvasáshoz
      const item = exercises[index];
      words[item.position] = item.article;
      // Teljes mondat helyes névelővel
      const textToRead = words.join(' ');
      
      // Lekérjük az audio URL-t az OpenAI API-tól
      const audioUrl = await textToSpeech(textToRead, 'de-DE');

      // Létrehozunk egy új audio elemet
      const audio = new Audio(audioUrl);
      audioRef.current = audio;

      // Lejátszás befejezésekor frissítjük az állapotot és felszabadítjuk az erőforrást
      audio.onended = () => {
        setIsPlayingAudio({ ...isPlayingAudio, [index]: false });
      };

      // Lejátszás
      await audio.play();
    } catch (error) {
      console.error('Hiba a szövegfelolvasás során:', error);
      setIsPlayingAudio({ ...isPlayingAudio, [index]: false });
      toast({
        title: "Hiba a felolvasás során",
        description: "Nem sikerült felolvasni a szöveget. Ellenőrizd, hogy van-e elég pontod.",
        variant: "destructive",
      });
    }
  };

  // Segítség mutatása
  const toggleHint = (index: number) => {
    setShowHint({ ...showHint, [index]: !showHint[index] });
  };

  // Segítség generálása
  const getHint = (item: any) => {
    const sentenceWords = item.sentence.split(' ');
    const nounPosition = item.position + 1; // A névelő után következő főnév pozíciója
    
    if (nounPosition >= sentenceWords.length) {
      return ["Figyelj az igére, amely meghatározza az esetet."];
    }
    
    const noun = sentenceWords[nounPosition].replace(/[.,!?;:]/, ''); // Eltávolítjuk az esetleges írásjeleket
    
    // Keressünk akkuzatív/datív igét a mondatban
    const verbHints = [];
    const verbList = type === 'akkusativ' ? commonVerbs.akkusativ : commonVerbs.dativ;
    
    for (const verbPair of verbList) {
      const verb = verbPair.split(' ')[0];
      if (item.sentence.includes(verb)) {
        verbHints.push(`A "${verb}" ige ${type === 'akkusativ' ? 'akkuzatív' : 'datív'} esetet vonz.`);
        break;
      }
    }
    
    // Alapvető szabályok emlékeztetője
    const rules = [];
    if (type === 'akkusativ') {
      rules.push("Akkuzatívban csak a hímnemű névelő változik: der → den.");
    } else {
      rules.push("Datívban minden névelő változik: der → dem, die → der, das → dem.");
    }
    
    return [...verbHints, ...rules];
  };

  return (
    <div className="w-full max-w-3xl mx-auto p-4 bg-white rounded-lg shadow-md">
      <div className="flex items-center justify-between mb-6">
        <h2 className="text-2xl font-bold text-center">
          {type === 'akkusativ' ? 'Határozott névelő akkuzatívban' : 'Határozott névelő datívban'}
        </h2>
        
        <Dialog>
          <DialogTrigger asChild>
            <Button variant="ghost" size="icon" className="rounded-full">
              <Info className="h-5 w-5" />
            </Button>
          </DialogTrigger>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>{caseInfo[type].title}</DialogTitle>
              <DialogDescription>
                {caseInfo[type].description}
              </DialogDescription>
            </DialogHeader>
            <div className="space-y-4 mt-4">
              <div>
                <h3 className="font-medium mb-2">Szabályok:</h3>
                <ul className="list-disc pl-5 space-y-1">
                  {caseInfo[type].rules.map((rule, i) => (
                    <li key={i}>{rule}</li>
                  ))}
                </ul>
              </div>
              
              <div>
                <h3 className="font-medium mb-2">Példák:</h3>
                <ul className="list-disc pl-5 space-y-1">
                  {caseInfo[type].examples.map((example, i) => (
                    <li key={i}>{example}</li>
                  ))}
                </ul>
              </div>
              
              <div>
                <h3 className="font-medium mb-2">Gyakori {type === 'akkusativ' ? 'akkuzatív' : 'datív'} igék:</h3>
                <div className="grid grid-cols-2 gap-1">
                  {commonVerbs[type].map((verb, i) => (
                    <div key={i} className="text-sm">{verb}</div>
                  ))}
                </div>
              </div>
            </div>
            <DialogClose asChild>
              <Button className="mt-4 w-full">Bezárás</Button>
            </DialogClose>
          </DialogContent>
        </Dialog>
      </div>
      
      <p className="mb-4 text-center">
        {type === 'akkusativ' 
          ? 'Válaszd ki a megfelelő névelőt (den, die, das) akkuzatív esetben!'
          : 'Válaszd ki a megfelelő névelőt (dem, der, den) datív esetben!'}
      </p>
      
      <div className="space-y-4">
        {exercises.map((item, index) => (
          <div 
            key={index} 
            className={cn(
              "p-4 rounded-md border",
              showResults && (
                checkedAnswers[index] 
                  ? "border-green-500 bg-green-50" 
                  : "border-red-500 bg-red-50"
              )
            )}
          >
            <div className="flex justify-between items-center mb-3">
              <div className="mb-2">
                {formatSentenceWithBlanks(item.sentence, item.position, userAnswers[index])}
              </div>
              
              <div className="flex space-x-2">
                <TooltipProvider>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <Button 
                        variant="ghost" 
                        size="icon" 
                        className="h-8 w-8" 
                        onClick={() => handleSpeech(item.sentence, index)}
                        disabled={isPlayingAudio[index]}
                      >
                        {isPlayingAudio[index] ? (
                          <Loader2 className="h-4 w-4 animate-spin" />
                        ) : (
                          <Volume2 className="h-4 w-4" />
                        )}
                      </Button>
                    </TooltipTrigger>
                    <TooltipContent>
                      <p>Mondat meghallgatása (1 pont)</p>
                    </TooltipContent>
                  </Tooltip>
                </TooltipProvider>
                
                <TooltipProvider>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <Button 
                        variant="ghost" 
                        size="icon" 
                        className="h-8 w-8" 
                        onClick={() => toggleHint(index)}
                      >
                        <HelpCircle className="h-4 w-4" />
                      </Button>
                    </TooltipTrigger>
                    <TooltipContent>
                      <p>Segítség kérése</p>
                    </TooltipContent>
                  </Tooltip>
                </TooltipProvider>
              </div>
            </div>
            
            <div className="flex space-x-3">
              {options.map((article) => (
                <label 
                  key={article} 
                  className={cn(
                    "flex items-center space-x-2 cursor-pointer",
                    showResults && article === item.article && "font-bold text-green-600"
                  )}
                >
                  <input
                    type="radio"
                    name={`article-${index}`}
                    value={article}
                    checked={userAnswers[index] === article}
                    onChange={() => handleAnswerChange(index, article)}
                    disabled={showResults}
                    className="h-4 w-4"
                  />
                  <span>{article}</span>
                </label>
              ))}
            </div>
            
            {showHint[index] && (
              <div className="mt-2 p-2 bg-blue-50 rounded text-sm">
                <p className="font-medium text-blue-700">Tipp:</p>
                <ul className="list-disc pl-5 text-blue-600">
                  {getHint(item).map((hint, i) => (
                    <li key={i}>{hint}</li>
                  ))}
                </ul>
              </div>
            )}
            
            {showResults && !checkedAnswers[index] && (
              <div className="mt-2 text-sm text-red-600">
                Helyes válasz: <span className="font-medium">{item.article}</span>
              </div>
            )}
          </div>
        ))}
      </div>

      <div className="mt-6 flex justify-center space-x-4">
        {!showResults ? (
          <Button onClick={checkAnswers} disabled={Object.keys(userAnswers).length !== exercises.length}>
            Ellenőrzés
          </Button>
        ) : (
          <>
            <div className="text-lg font-medium">
              Eredmény: {correctCount}/{exercises.length} ({Math.round((correctCount / exercises.length) * 100)}%)
            </div>
            <Button onClick={resetGame}>Újrakezdés</Button>
          </>
        )}
      </div>
    </div>
  );
};

export const ArtikelCaseExercise: React.FC = () => {
  const [activeTab, setActiveTab] = useState('akkusativ');

  return (
    <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
      <TabsList className="grid grid-cols-2 mb-6">
        <TabsTrigger value="akkusativ">Akkuzatív</TabsTrigger>
        <TabsTrigger value="dativ">Datív</TabsTrigger>
      </TabsList>
      
      <TabsContent value="akkusativ">
        <ArtikelAkkusativDativGame type="akkusativ" />
      </TabsContent>
      
      <TabsContent value="dativ">
        <ArtikelAkkusativDativGame type="dativ" />
      </TabsContent>
    </Tabs>
  );
};

export default ArtikelCaseExercise;