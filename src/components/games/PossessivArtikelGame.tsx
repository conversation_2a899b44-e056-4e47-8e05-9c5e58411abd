import React, { useState, useEffect, useRef } from 'react';
import { 
  possessivArtikelNominativItems, 
  possessivArtikelAkkusativItems 
} from '../../data/games/possessivArtikel';
import { cn } from '../../lib/utils';
import { Button } from '../ui/button';
import { Input } from '../ui/input'; 
import { useToast } from '../ui/use-toast';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '../ui/tabs';
import { textToSpeech } from '../../services/openaiService';
import { Volume2, HelpCircle, Loader2, Info } from 'lucide-react';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "../ui/tooltip";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
  DialogClose,
} from "../ui/dialog";

interface PossessivArtikelExerciseProps {
  type: 'nominativ' | 'akkusativ';
}

const PossessivArtikelExercise: React.FC<PossessivArtikelExerciseProps> = ({ type }) => {
  const items = type === 'nominativ' 
    ? possessivArtikelNominativItems 
    : possessivArtikelAkkusativItems;
  
  const [exercises, setExercises] = useState(items);
  const [userAnswers, setUserAnswers] = useState<Record<number, string>>({});
  const [checkedAnswers, setCheckedAnswers] = useState<Record<number, boolean>>({});
  const [showResults, setShowResults] = useState(false);
  const [correctCount, setCorrectCount] = useState(0);
  const [isPlayingAudio, setIsPlayingAudio] = useState<Record<number, boolean>>({});
  const [showHint, setShowHint] = useState<Record<number, boolean>>({});
  const { toast } = useToast();
  const audioRef = useRef<HTMLAudioElement | null>(null);

  // Birtokos névmások táblázata
  const possessivArtikelTables = {
    nominativ: {
      title: "Birtokos névmások alanyesetben",
      description: "A birtokos névmások kifejezik, hogy valami kinek a tulajdona vagy kihez tartozik.",
      masculine: [
        { pronoun: "ich (én)", possessive: "mein (az én...m)" },
        { pronoun: "du (te)", possessive: "dein (a te...d)" },
        { pronoun: "er (ő - férfi)", possessive: "sein (az ő...je)" },
        { pronoun: "sie (ő - nő)", possessive: "ihr (az ő...je)" },
        { pronoun: "es (az/ő - semleges)", possessive: "sein (az ő...je)" },
        { pronoun: "wir (mi)", possessive: "unser (a mi...nk)" },
        { pronoun: "ihr (ti)", possessive: "euer (a ti...tek)" },
        { pronoun: "sie (ők)", possessive: "ihr (az ő...jük)" },
        { pronoun: "Sie (Ön/Önök)", possessive: "Ihr (az Ön/Önök...je)" }
      ],
      feminine: [
        { pronoun: "ich (én)", possessive: "meine (az én...m)" },
        { pronoun: "du (te)", possessive: "deine (a te...d)" },
        { pronoun: "er (ő - férfi)", possessive: "seine (az ő...je)" },
        { pronoun: "sie (ő - nő)", possessive: "ihre (az ő...je)" },
        { pronoun: "es (az/ő - semleges)", possessive: "seine (az ő...je)" },
        { pronoun: "wir (mi)", possessive: "unsere (a mi...nk)" },
        { pronoun: "ihr (ti)", possessive: "eure (a ti...tek)" },
        { pronoun: "sie (ők)", possessive: "ihre (az ő...jük)" },
        { pronoun: "Sie (Ön/Önök)", possessive: "Ihre (az Ön/Önök...je)" }
      ],
      neuter: [
        { pronoun: "ich (én)", possessive: "mein (az én...m)" },
        { pronoun: "du (te)", possessive: "dein (a te...d)" },
        { pronoun: "er (ő - férfi)", possessive: "sein (az ő...je)" },
        { pronoun: "sie (ő - nő)", possessive: "ihr (az ő...je)" },
        { pronoun: "es (az/ő - semleges)", possessive: "sein (az ő...je)" },
        { pronoun: "wir (mi)", possessive: "unser (a mi...nk)" },
        { pronoun: "ihr (ti)", possessive: "euer (a ti...tek)" },
        { pronoun: "sie (ők)", possessive: "ihr (az ő...jük)" },
        { pronoun: "Sie (Ön/Önök)", possessive: "Ihr (az Ön/Önök...je)" }
      ],
      examples: [
        "Das ist <strong>mein</strong> Bruder. (Ez az én testvérem.)",
        "<strong>Ihre</strong> Tasche ist blau. (Az Ön táskája kék.)",
        "<strong>Unser</strong> Auto ist neu. (A mi autónk új.)"
      ]
    },
    akkusativ: {
      title: "Birtokos névmások tárgyesetben",
      description: "A birtokos névmások alakja változik, ha a főnév akkuzatívban (tárgyesetben) áll.",
      masculine: [
        { pronoun: "ich (én)", possessive: "meinen (az én...mat)" },
        { pronoun: "du (te)", possessive: "deinen (a te...dat)" },
        { pronoun: "er (ő - férfi)", possessive: "seinen (az ő...jét)" },
        { pronoun: "sie (ő - nő)", possessive: "ihren (az ő...jét)" },
        { pronoun: "es (az/ő - semleges)", possessive: "seinen (az ő...jét)" },
        { pronoun: "wir (mi)", possessive: "unseren (a mi...nkat)" },
        { pronoun: "ihr (ti)", possessive: "euren (a ti...teket)" },
        { pronoun: "sie (ők)", possessive: "ihren (az ő...jüket)" },
        { pronoun: "Sie (Ön/Önök)", possessive: "Ihren (az Ön/Önök...jét)" }
      ],
      feminine: [
        { pronoun: "ich (én)", possessive: "meine (az én...mat)" },
        { pronoun: "du (te)", possessive: "deine (a te...dat)" },
        { pronoun: "er (ő - férfi)", possessive: "seine (az ő...jét)" },
        { pronoun: "sie (ő - nő)", possessive: "ihre (az ő...jét)" },
        { pronoun: "es (az/ő - semleges)", possessive: "seine (az ő...jét)" },
        { pronoun: "wir (mi)", possessive: "unsere (a mi...nkat)" },
        { pronoun: "ihr (ti)", possessive: "eure (a ti...teket)" },
        { pronoun: "sie (ők)", possessive: "ihre (az ő...jüket)" },
        { pronoun: "Sie (Ön/Önök)", possessive: "Ihre (az Ön/Önök...jét)" }
      ],
      neuter: [
        { pronoun: "ich (én)", possessive: "mein (az én...mat)" },
        { pronoun: "du (te)", possessive: "dein (a te...dat)" },
        { pronoun: "er (ő - férfi)", possessive: "sein (az ő...jét)" },
        { pronoun: "sie (ő - nő)", possessive: "ihr (az ő...jét)" },
        { pronoun: "es (az/ő - semleges)", possessive: "sein (az ő...jét)" },
        { pronoun: "wir (mi)", possessive: "unser (a mi...nkat)" },
        { pronoun: "ihr (ti)", possessive: "euer (a ti...teket)" },
        { pronoun: "sie (ők)", possessive: "ihr (az ő...jüket)" },
        { pronoun: "Sie (Ön/Önök)", possessive: "Ihr (az Ön/Önök...jét)" }
      ],
      examples: [
        "Ich sehe <strong>deinen</strong> Bruder. (Látom a te testvéredet.)",
        "Er kauft <strong>meine</strong> Tasche. (Megveszi az én táskámat.)",
        "Wir lieben <strong>unsere</strong> Katze. (Szeretjük a macskánkat.)"
      ]
    }
  };

  useEffect(() => {
    // Randomize the order of exercises
    setExercises([...items].sort(() => Math.random() - 0.5));
    setUserAnswers({});
    setCheckedAnswers({});
    setShowResults(false);
    setShowHint({});
    setCorrectCount(0);
    
    // Komponens tisztítása unmount esetén
    return () => {
      if (audioRef.current) {
        audioRef.current.pause();
        if (audioRef.current.src) {
          URL.revokeObjectURL(audioRef.current.src);
        }
      }
    };
  }, [type, items]);

  const handleAnswerChange = (index: number, value: string) => {
    setUserAnswers({ ...userAnswers, [index]: value });
  };

  const checkAnswers = () => {
    const newCheckedAnswers: Record<number, boolean> = {};
    let correct = 0;

    exercises.forEach((item, index) => {
      const isCorrect = userAnswers[index]?.toLowerCase() === item.pronoun.toLowerCase();
      newCheckedAnswers[index] = isCorrect;
      if (isCorrect) correct++;
    });

    setCheckedAnswers(newCheckedAnswers);
    setShowResults(true);
    setCorrectCount(correct);

    toast({
      title: `Eredmény: ${correct}/${exercises.length}`,
      description: `${correct} helyes válasz a ${exercises.length}-ból`,
    });
  };

  const resetGame = () => {
    setUserAnswers({});
    setCheckedAnswers({});
    setShowResults(false);
    setShowHint({});
    setExercises([...items].sort(() => Math.random() - 0.5));
  };

  const formatSentenceWithBlanks = (sentence: string, position: number, userAnswer?: string) => {
    const words = sentence.split(' ');
    const blankIndex = position;

    return (
      <>
        {words.slice(0, blankIndex).join(' ')}{' '}
        <span className="font-bold underline">
          {userAnswer || '_______'}
        </span>{' '}
        {words.slice(blankIndex + 1).join(' ')}
      </>
    );
  };

  // Mondat felolvasása
  const handleSpeech = async (sentence: string, index: number) => {
    try {
      setIsPlayingAudio({ ...isPlayingAudio, [index]: true });
      
      // Ha van korábbi audio, azt leállítjuk és felszabadítjuk
      if (audioRef.current) {
        audioRef.current.pause();
        URL.revokeObjectURL(audioRef.current.src);
      }

      // A mondat szavai
      const words = sentence.split(' ');
      // Kicseréljük a helyőrzőt a helyes névmással a felolvasáshoz
      const item = exercises[index];
      words[item.position] = item.pronoun;
      // Teljes mondat helyes névmással
      const textToRead = words.join(' ');
      
      // Lekérjük az audio URL-t az OpenAI API-tól
      const audioUrl = await textToSpeech(textToRead, 'de-DE');

      // Létrehozunk egy új audio elemet
      const audio = new Audio(audioUrl);
      audioRef.current = audio;

      // Lejátszás befejezésekor frissítjük az állapotot és felszabadítjuk az erőforrást
      audio.onended = () => {
        setIsPlayingAudio({ ...isPlayingAudio, [index]: false });
      };

      // Lejátszás
      await audio.play();
    } catch (error) {
      console.error('Hiba a szövegfelolvasás során:', error);
      setIsPlayingAudio({ ...isPlayingAudio, [index]: false });
      toast({
        title: "Hiba a felolvasás során",
        description: "Nem sikerült felolvasni a szöveget. Ellenőrizd, hogy van-e elég pontod.",
        variant: "destructive",
      });
    }
  };

  // Segítség mutatása/elrejtése
  const toggleHint = (index: number) => {
    setShowHint({ ...showHint, [index]: !showHint[index] });
  };

  // Segítség generálása
  const getHint = (item: any) => {
    const hints = [];
    
    // Segítség a hint alapján, ha van
    if ('hint' in item && item.hint) {
      const hintLower = item.hint.toLowerCase();
      
      if (hintLower === 'ich') {
        hints.push(type === 'nominativ' 
          ? 'Az "ich" (én) birtokos névmása: "mein" (hímnem/semlegesnem) vagy "meine" (nőnem).' 
          : 'Az "ich" (én) birtokos névmása tárgyesetben: "meinen" (hímnem), "meine" (nőnem) vagy "mein" (semlegesnem).');
      } else if (hintLower === 'du') {
        hints.push(type === 'nominativ' 
          ? 'A "du" (te) birtokos névmása: "dein" (hímnem/semlegesnem) vagy "deine" (nőnem).' 
          : 'A "du" (te) birtokos névmása tárgyesetben: "deinen" (hímnem), "deine" (nőnem) vagy "dein" (semlegesnem).');
      } else if (hintLower === 'er') {
        hints.push(type === 'nominativ' 
          ? 'Az "er" (ő - férfi) birtokos névmása: "sein" (hímnem/semlegesnem) vagy "seine" (nőnem).' 
          : 'Az "er" (ő - férfi) birtokos névmása tárgyesetben: "seinen" (hímnem), "seine" (nőnem) vagy "sein" (semlegesnem).');
      } else if (hintLower === 'sie' && !hintLower.includes('ők')) {
        hints.push(type === 'nominativ' 
          ? 'A "sie" (ő - nő) birtokos névmása: "ihr" (hímnem/semlegesnem) vagy "ihre" (nőnem).' 
          : 'A "sie" (ő - nő) birtokos névmása tárgyesetben: "ihren" (hímnem), "ihre" (nőnem) vagy "ihr" (semlegesnem).');
      } else if (hintLower === 'wir') {
        hints.push(type === 'nominativ' 
          ? 'A "wir" (mi) birtokos névmása: "unser" (hímnem/semlegesnem) vagy "unsere" (nőnem).' 
          : 'A "wir" (mi) birtokos névmása tárgyesetben: "unseren" (hímnem), "unsere" (nőnem) vagy "unser" (semlegesnem).');
      } else if (hintLower === 'ihr' && !hintLower.includes('ihre')) {
        hints.push(type === 'nominativ' 
          ? 'Az "ihr" (ti) birtokos névmása: "euer" (hímnem/semlegesnem) vagy "eure" (nőnem).' 
          : 'Az "ihr" (ti) birtokos névmása tárgyesetben: "euren" (hímnem), "eure" (nőnem) vagy "euer" (semlegesnem).');
      } else if (hintLower.includes('sie') && (hintLower.includes('ők') || hintLower.includes('több'))) {
        hints.push(type === 'nominativ' 
          ? 'A "sie" (ők) birtokos névmása: "ihr" (hímnem/semlegesnem) vagy "ihre" (nőnem).' 
          : 'A "sie" (ők) birtokos névmása tárgyesetben: "ihren" (hímnem), "ihre" (nőnem) vagy "ihr" (semlegesnem).');
      }
    }
    
    // Általános segítség
    hints.push(type === 'nominativ' 
      ? 'A birtokos névmások a főnév előtt állnak és a főnév nemétől, számától és esetétől függően változnak az alakjaik.' 
      : 'Tárgyesetben a birtokos névmások alakja megváltozik, különösen a hímnemű főneveknél.');
    
    return hints;
  };

  return (
    <div className="w-full max-w-3xl mx-auto p-4 bg-white rounded-lg shadow-md">
      <div className="flex items-center justify-between mb-6">
        <h2 className="text-2xl font-bold text-center">
          {type === 'nominativ' ? 'Birtokos névmások alanyesetben' : 'Birtokos névmások tárgyesetben'}
        </h2>
        
        <Dialog>
          <DialogTrigger asChild>
            <Button variant="ghost" size="icon" className="rounded-full">
              <Info className="h-5 w-5" />
            </Button>
          </DialogTrigger>
          <DialogContent className="max-h-[80vh] overflow-y-auto">
            <DialogHeader>
              <DialogTitle>{possessivArtikelTables[type].title}</DialogTitle>
              <DialogDescription>
                {possessivArtikelTables[type].description}
              </DialogDescription>
            </DialogHeader>
            <div className="space-y-4 mt-4">
              <div>
                <h3 className="font-medium mb-2">Hímnemű főnevekkel:</h3>
                <div className="border rounded-md overflow-hidden">
                  <table className="w-full">
                    <thead>
                      <tr className="bg-gray-100">
                        <th className="px-4 py-2 text-left">Személyes névmás</th>
                        <th className="px-4 py-2 text-left">Birtokos névmás</th>
                      </tr>
                    </thead>
                    <tbody>
                      {possessivArtikelTables[type].masculine.map((row, i) => (
                        <tr key={i} className={i % 2 === 0 ? 'bg-white' : 'bg-gray-50'}>
                          <td className="px-4 py-2">{row.pronoun}</td>
                          <td className="px-4 py-2">{row.possessive}</td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              </div>
              
              <div>
                <h3 className="font-medium mb-2">Nőnemű főnevekkel:</h3>
                <div className="border rounded-md overflow-hidden">
                  <table className="w-full">
                    <thead>
                      <tr className="bg-gray-100">
                        <th className="px-4 py-2 text-left">Személyes névmás</th>
                        <th className="px-4 py-2 text-left">Birtokos névmás</th>
                      </tr>
                    </thead>
                    <tbody>
                      {possessivArtikelTables[type].feminine.map((row, i) => (
                        <tr key={i} className={i % 2 === 0 ? 'bg-white' : 'bg-gray-50'}>
                          <td className="px-4 py-2">{row.pronoun}</td>
                          <td className="px-4 py-2">{row.possessive}</td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              </div>
              
              <div>
                <h3 className="font-medium mb-2">Példák:</h3>
                <ul className="list-disc pl-5 space-y-1">
                  {possessivArtikelTables[type].examples.map((example, i) => (
                    <li key={i} dangerouslySetInnerHTML={{ __html: example }} />
                  ))}
                </ul>
              </div>
            </div>
            <DialogClose asChild>
              <Button className="mt-4 w-full">Bezárás</Button>
            </DialogClose>
          </DialogContent>
        </Dialog>
      </div>
      
      <p className="mb-4 text-center">
        {type === 'nominativ' 
          ? 'Egészítsd ki a mondatokat a megfelelő birtokos névmással alanyesetben!'
          : 'Egészítsd ki a mondatokat a megfelelő birtokos névmással tárgyesetben!'}
      </p>
      
      <div className="space-y-6">
        {exercises.map((item, index) => (
          <div 
            key={index} 
            className={cn(
              "p-4 rounded-md border",
              showResults && (
                checkedAnswers[index] 
                  ? "border-green-500 bg-green-50" 
                  : "border-red-500 bg-red-50"
              )
            )}
          >
            <div className="flex justify-between items-center mb-3">
              <div className="text-lg">
                {formatSentenceWithBlanks(item.sentence, item.position, userAnswers[index])}
                {'hint' in item && item.hint && (
                  <div className="text-sm text-gray-500 mt-1">
                    ({item.hint})
                  </div>
                )}
              </div>
              
              <div className="flex space-x-2">
                <TooltipProvider>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <Button 
                        variant="ghost" 
                        size="icon" 
                        className="h-8 w-8" 
                        onClick={() => handleSpeech(item.sentence, index)}
                        disabled={isPlayingAudio[index]}
                      >
                        {isPlayingAudio[index] ? (
                          <Loader2 className="h-4 w-4 animate-spin" />
                        ) : (
                          <Volume2 className="h-4 w-4" />
                        )}
                      </Button>
                    </TooltipTrigger>
                    <TooltipContent>
                      <p>Mondat meghallgatása (1 pont)</p>
                    </TooltipContent>
                  </Tooltip>
                </TooltipProvider>
                
                <TooltipProvider>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <Button 
                        variant="ghost" 
                        size="icon" 
                        className="h-8 w-8" 
                        onClick={() => toggleHint(index)}
                      >
                        <HelpCircle className="h-4 w-4" />
                      </Button>
                    </TooltipTrigger>
                    <TooltipContent>
                      <p>Segítség kérése</p>
                    </TooltipContent>
                  </Tooltip>
                </TooltipProvider>
              </div>
            </div>
            
            <div className="flex space-x-2 items-center">
              <Input
                type="text"
                value={userAnswers[index] || ''}
                onChange={(e) => handleAnswerChange(index, e.target.value)}
                placeholder="Írd be a birtokos névmást..."
                disabled={showResults}
                className="max-w-xs"
              />
              
              {showResults && (
                <div className={cn(
                  "ml-4 font-medium",
                  checkedAnswers[index] ? "text-green-600" : "text-red-600"
                )}>
                  {checkedAnswers[index] ? (
                    <span>✓ Helyes</span>
                  ) : (
                    <span>✗ Helyes: {item.pronoun}</span>
                  )}
                </div>
              )}
            </div>
            
            {showHint[index] && (
              <div className="mt-2 p-2 bg-blue-50 rounded text-sm">
                <p className="font-medium text-blue-700">Tipp:</p>
                <ul className="list-disc pl-5 text-blue-600">
                  {getHint(item).map((hint, i) => (
                    <li key={i}>{hint}</li>
                  ))}
                </ul>
              </div>
            )}
          </div>
        ))}
      </div>

      <div className="mt-6 flex justify-center space-x-4">
        {!showResults ? (
          <Button onClick={checkAnswers} disabled={Object.keys(userAnswers).length !== exercises.length}>
            Ellenőrzés
          </Button>
        ) : (
          <>
            <div className="text-lg font-medium">
              Eredmény: {correctCount}/{exercises.length} ({Math.round((correctCount / exercises.length) * 100)}%)
            </div>
            <Button onClick={resetGame}>Újrakezdés</Button>
          </>
        )}
      </div>
    </div>
  );
};

const PossessivArtikelGame: React.FC = () => {
  const [activeTab, setActiveTab] = useState('nominativ');

  return (
    <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
      <TabsList className="grid grid-cols-2 mb-6">
        <TabsTrigger value="nominativ">Alanyeset</TabsTrigger>
        <TabsTrigger value="akkusativ">Tárgyeset</TabsTrigger>
      </TabsList>
      
      <TabsContent value="nominativ">
        <PossessivArtikelExercise type="nominativ" />
      </TabsContent>
      
      <TabsContent value="akkusativ">
        <PossessivArtikelExercise type="akkusativ" />
      </TabsContent>
    </Tabs>
  );
};

export default PossessivArtikelGame;