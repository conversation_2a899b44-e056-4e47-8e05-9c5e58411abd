import React, { useState } from 'react';
import { Button } from "../ui/button";
import { Input } from "../ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "../ui/select";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "../ui/card";
import { Badge } from "../ui/badge";
import { Separator } from "../ui/separator";

// Napi tevékenységek listája
const dailyActivities = [
  { id: 'aufstehen', german: 'aufstehen', hungarian: 'felkelni' },
  { id: 'frühstücken', german: 'frühstücken', hungarian: 'reggelizni' },
  { id: 'duschen', german: 'duschen', hungarian: 'zuhanyozni' },
  { id: 'zurArbeitGehen', german: 'zur Arbeit gehen', hungarian: 'munkába menni' },
  { id: 'zurSchuleGehen', german: 'zur Schule gehen', hungarian: 'iskol<PERSON><PERSON> menni' },
  { id: 'mittag<PERSON>sen', german: 'Mittag essen', hungarian: 'eb<PERSON>delni' },
  { id: 'sportMachen', german: 'Sport machen', hungarian: 'sportolni' },
  { id: 'fernsehen', german: 'fernsehen', hungarian: 'tévét nézni' },
  { id: 'abendEssen', german: 'Abend essen', hungarian: 'vacsorázni' },
  { id: 'lesenBuch', german: 'ein Buch lesen', hungarian: 'könyvet olvasni' },
  { id: 'freundeTreffen', german: 'Freunde treffen', hungarian: 'barátokkal találkozni' },
  { id: 'musikHören', german: 'Musik hören', hungarian: 'zenét hallgatni' },
  { id: 'einkaufen', german: 'einkaufen', hungarian: 'bevásárolni' },
  { id: 'kochen', german: 'kochen', hungarian: 'főzni' },
  { id: 'insKinoGehen', german: 'ins Kino gehen', hungarian: 'moziba menni' },
  { id: 'schlafen', german: 'schlafen gehen', hungarian: 'aludni menni' }
];

// A hét napjai
const weekdays = [
  { id: 'montag', german: 'Montag', hungarian: 'hétfő' },
  { id: 'dienstag', german: 'Dienstag', hungarian: 'kedd' },
  { id: 'mittwoch', german: 'Mittwoch', hungarian: 'szerda' },
  { id: 'donnerstag', german: 'Donnerstag', hungarian: 'csütörtök' },
  { id: 'freitag', german: 'Freitag', hungarian: 'péntek' },
  { id: 'samstag', german: 'Samstag', hungarian: 'szombat' },
  { id: 'sonntag', german: 'Sonntag', hungarian: 'vasárnap' }
];

// Napszakok
const dayParts = [
  { id: 'morgen', german: 'am Morgen', hungarian: 'reggel' },
  { id: 'vormittag', german: 'am Vormittag', hungarian: 'délelőtt' },
  { id: 'mittag', german: 'am Mittag', hungarian: 'délben' },
  { id: 'nachmittag', german: 'am Nachmittag', hungarian: 'délután' },
  { id: 'abend', german: 'am Abend', hungarian: 'este' },
  { id: 'nacht', german: 'in der Nacht', hungarian: 'éjjel' }
];

// Időpontok (egyszerűsített)
const timeOptions = [
  '06:00', '06:30', '07:00', '07:30', '08:00', '08:30', '09:00', '09:30', 
  '10:00', '10:30', '11:00', '11:30', '12:00', '12:30', '13:00', '13:30', 
  '14:00', '14:30', '15:00', '15:30', '16:00', '16:30', '17:00', '17:30', 
  '18:00', '18:30', '19:00', '19:30', '20:00', '20:30', '21:00', '21:30', 
  '22:00', '22:30', '23:00', '23:30'
];

// Segédfüggvény a pontos idő és az informális idő közötti konverzióhoz
const getGermanInformalTime = (time: string): string => {
  const [hours, minutes] = time.split(':').map(Number);
  
  if (minutes === 0) {
    return `${hours} Uhr`;
  } else if (minutes === 30) {
    const nextHour = hours === 12 ? 1 : (hours === 24 ? 1 : hours + 1);
    return `halb ${nextHour}`;
  } else if (minutes === 15) {
    return `Viertel nach ${hours}`;
  } else if (minutes === 45) {
    const nextHour = hours === 12 ? 1 : (hours === 24 ? 1 : hours + 1);
    return `Viertel vor ${nextHour}`;
  } else if (minutes < 30) {
    return `${minutes} Minuten nach ${hours}`;
  } else {
    const minutesToNextHour = 60 - minutes;
    const nextHour = hours === 12 ? 1 : (hours === 24 ? 1 : hours + 1);
    return `${minutesToNextHour} Minuten vor ${nextHour}`;
  }
};

interface ScheduledActivity {
  id: string;
  day: string;
  dayPart: string;
  time: string;
  activity: string;
}

interface ScheduleEntry {
  id: string;
  day: string;
  dayPart: string;
  time: string;
  activity: string;
  formattedText: string;
}

const WeeklyPlanner: React.FC = () => {
  const [selectedDay, setSelectedDay] = useState<string>('');
  const [selectedDayPart, setSelectedDayPart] = useState<string>('');
  const [selectedTime, setSelectedTime] = useState<string>('');
  const [selectedActivity, setSelectedActivity] = useState<string>('');
  const [schedule, setSchedule] = useState<ScheduleEntry[]>([]);
  const [language, setLanguage] = useState<'german' | 'hungarian'>('german');
  const [showFeedback, setShowFeedback] = useState<boolean>(false);
  const [feedback, setFeedback] = useState<string>('');
  const [isSuccessful, setIsSuccessful] = useState<boolean>(false);

  // Tevékenység hozzáadása az időbeosztáshoz
  const addActivity = () => {
    if (!selectedDay || !selectedDayPart || !selectedTime || !selectedActivity) {
      setFeedback('Kérlek válassz minden opciót!');
      setIsSuccessful(false);
      setShowFeedback(true);
      setTimeout(() => setShowFeedback(false), 3000);
      return;
    }

    const selectedDayObj = weekdays.find(day => day.id === selectedDay);
    const selectedDayPartObj = dayParts.find(part => part.id === selectedDayPart);
    const selectedActivityObj = dailyActivities.find(activity => activity.id === selectedActivity);

    if (!selectedDayObj || !selectedDayPartObj || !selectedActivityObj) {
      return;
    }

    const newEntry: ScheduleEntry = {
      id: `${Date.now()}`,
      day: selectedDay,
      dayPart: selectedDayPart,
      time: selectedTime,
      activity: selectedActivity,
      formattedText: language === 'german' 
        ? `${selectedDayObj.german} ${selectedDayPartObj.german} um ${selectedTime} Uhr ${selectedActivityObj.german} ich.`
        : `${selectedDayObj.hungarian}n ${selectedDayPartObj.hungarian} ${selectedTime}-kor ${selectedActivityObj.hungarian} fogok.`
    };

    setSchedule([...schedule, newEntry]);
    setFeedback('Tevékenység sikeresen hozzáadva!');
    setIsSuccessful(true);
    setShowFeedback(true);
    setTimeout(() => setShowFeedback(false), 3000);

    // Töröljük a kiválasztott értékeket
    setSelectedTime('');
    setSelectedActivity('');
  };

  // Tevékenység törlése az időbeosztásból
  const removeActivity = (id: string) => {
    setSchedule(schedule.filter(entry => entry.id !== id));
  };

  // Időbeosztás törlése
  const clearSchedule = () => {
    setSchedule([]);
  };

  // Teljes időbeosztás generálása véletlenszerűen
  const generateRandomSchedule = () => {
    clearSchedule();
    
    const newSchedule: ScheduleEntry[] = [];
    
    // Minden napra generálunk 3-5 tevékenységet
    weekdays.forEach(day => {
      // Véletlenszerű számú tevékenység (3-5)
      const activitiesCount = Math.floor(Math.random() * 3) + 3;
      
      // Már használt időpontok nyilvántartása, hogy elkerüljük az ütközéseket
      const usedTimes = new Set<string>();
      
      for (let i = 0; i < activitiesCount; i++) {
        // Véletlenszerű napszak
        const randomDayPartIndex = Math.floor(Math.random() * dayParts.length);
        const randomDayPart = dayParts[randomDayPartIndex];
        
        // Véletlenszerű tevékenység
        const randomActivityIndex = Math.floor(Math.random() * dailyActivities.length);
        const randomActivity = dailyActivities[randomActivityIndex];
        
        // Véletlenszerű időpont generálása, amely még nincs használva
        let randomTime;
        do {
          const randomTimeIndex = Math.floor(Math.random() * timeOptions.length);
          randomTime = timeOptions[randomTimeIndex];
        } while (usedTimes.has(randomTime));
        
        usedTimes.add(randomTime);
        
        const newEntry: ScheduleEntry = {
          id: `${Date.now()}-${day.id}-${i}`,
          day: day.id,
          dayPart: randomDayPart.id,
          time: randomTime,
          activity: randomActivity.id,
          formattedText: language === 'german' 
            ? `${day.german} ${randomDayPart.german} um ${randomTime} Uhr ${randomActivity.german} ich.`
            : `${day.hungarian}n ${randomDayPart.hungarian} ${randomTime}-kor ${randomActivity.hungarian} fogok.`
        };
        
        newSchedule.push(newEntry);
      }
    });
    
    setSchedule(newSchedule);
  };

  // Nyelv váltása
  const toggleLanguage = () => {
    const newLanguage = language === 'german' ? 'hungarian' : 'german';
    setLanguage(newLanguage);
    
    // Frissítsük a meglévő időbeosztások szövegét is
    const updatedSchedule = schedule.map(entry => {
      const day = weekdays.find(d => d.id === entry.day);
      const dayPart = dayParts.find(p => p.id === entry.dayPart);
      const activity = dailyActivities.find(a => a.id === entry.activity);
      
      if (!day || !dayPart || !activity) return entry;
      
      return {
        ...entry,
        formattedText: newLanguage === 'german' 
          ? `${day.german} ${dayPart.german} um ${entry.time} Uhr ${activity.german} ich.`
          : `${day.hungarian}n ${dayPart.hungarian} ${entry.time}-kor ${activity.hungarian} fogok.`
      };
    });
    
    setSchedule(updatedSchedule);
  };

  return (
    <Card className="w-full max-w-4xl mx-auto">
      <CardHeader>
        <CardTitle>Heti időbeosztás tervező</CardTitle>
        <CardDescription>
          Tervezd meg a heti programodat és gyakorold a német időkifejezéseket és napi rutin tevékenységeket!
        </CardDescription>
      </CardHeader>
      
      <CardContent className="space-y-6">
        {/* Nyelvi választó */}
        <div className="flex justify-end">
          <Button 
            variant="outline" 
            onClick={toggleLanguage}
            className="flex items-center gap-2"
          >
            <span>Nyelv: </span>
            <Badge variant="secondary">
              {language === 'german' ? 'Német' : 'Magyar'}
            </Badge>
          </Button>
        </div>
        
        {/* Új tevékenység hozzáadása */}
        <div className="bg-gray-50 p-4 rounded-lg">
          <h3 className="text-lg font-medium mb-3">Új tevékenység hozzáadása</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium mb-1">Nap:</label>
              <Select value={selectedDay} onValueChange={setSelectedDay}>
                <SelectTrigger>
                  <SelectValue placeholder="Válassz egy napot" />
                </SelectTrigger>
                <SelectContent>
                  {weekdays.map(day => (
                    <SelectItem key={day.id} value={day.id}>
                      {language === 'german' ? day.german : day.hungarian}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            
            <div>
              <label className="block text-sm font-medium mb-1">Napszak:</label>
              <Select value={selectedDayPart} onValueChange={setSelectedDayPart}>
                <SelectTrigger>
                  <SelectValue placeholder="Válassz egy napszakot" />
                </SelectTrigger>
                <SelectContent>
                  {dayParts.map(part => (
                    <SelectItem key={part.id} value={part.id}>
                      {language === 'german' ? part.german : part.hungarian}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            
            <div>
              <label className="block text-sm font-medium mb-1">Időpont:</label>
              <Select value={selectedTime} onValueChange={setSelectedTime}>
                <SelectTrigger>
                  <SelectValue placeholder="Válassz egy időpontot" />
                </SelectTrigger>
                <SelectContent>
                  {timeOptions.map(time => (
                    <SelectItem key={time} value={time}>
                      {language === 'german'
                        ? `${time} Uhr (${getGermanInformalTime(time)})`
                        : `${time} óra`
                      }
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            
            <div>
              <label className="block text-sm font-medium mb-1">Tevékenység:</label>
              <Select value={selectedActivity} onValueChange={setSelectedActivity}>
                <SelectTrigger>
                  <SelectValue placeholder="Válassz egy tevékenységet" />
                </SelectTrigger>
                <SelectContent>
                  {dailyActivities.map(activity => (
                    <SelectItem key={activity.id} value={activity.id}>
                      {language === 'german' ? activity.german : activity.hungarian}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>
          
          <Button onClick={addActivity} className="w-full mt-4">
            Hozzáadás
          </Button>

          {showFeedback && (
            <div className={`mt-2 p-2 rounded-md text-center ${isSuccessful ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}`}>
              {feedback}
            </div>
          )}
        </div>
        
        {/* Időbeosztás megjelenítése */}
        <div>
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-medium">Az időbeosztásod</h3>
            <div className="space-x-2">
              <Button variant="outline" onClick={generateRandomSchedule}>
                Véletlenszerű időbeosztás
              </Button>
              <Button variant="outline" onClick={clearSchedule}>
                Törlés
              </Button>
            </div>
          </div>
          
          <div className="space-y-4">
            {weekdays.map(day => {
              const dayActivities = schedule.filter(entry => entry.day === day.id);
              
              return dayActivities.length > 0 ? (
                <div key={day.id} className="border rounded-md p-3">
                  <h4 className="font-medium mb-2">
                    {language === 'german' ? day.german : day.hungarian}
                  </h4>
                  
                  <div className="space-y-2">
                    {dayActivities
                      .sort((a, b) => a.time.localeCompare(b.time)) // Időpont szerint rendezzük
                      .map(entry => {
                        // Megkeressük a megfelelő objektumokat az adatokhoz
                        const dayPart = dayParts.find(p => p.id === entry.dayPart);
                        const activity = dailyActivities.find(a => a.id === entry.activity);
                        
                        return (
                          <div 
                            key={entry.id} 
                            className="flex items-center justify-between bg-gray-50 p-2 rounded"
                          >
                            <div>
                              <span className="font-medium">{entry.time}</span>
                              {' - '}
                              <span>
                                {language === 'german' 
                                  ? `${dayPart?.german}, ${activity?.german}`
                                  : `${dayPart?.hungarian}, ${activity?.hungarian}`
                                }
                              </span>
                            </div>
                            <Button 
                              variant="ghost" 
                              size="sm" 
                              onClick={() => removeActivity(entry.id)}
                            >
                              X
                            </Button>
                          </div>
                        );
                      })}
                  </div>
                </div>
              ) : null;
            })}
            
            {schedule.length === 0 && (
              <div className="text-center text-gray-500 p-4 border border-dashed rounded-md">
                Még nincs tevékenység az időbeosztásban. Adj hozzá újakat vagy generálj véletlenszerűen!
              </div>
            )}
          </div>
        </div>
        
        {/* Teljes időbeosztás mondatokban */}
        {schedule.length > 0 && (
          <div className="mt-6">
            <h3 className="text-lg font-medium mb-2">Az időbeosztásod mondatokban</h3>
            <div className="bg-gray-50 p-4 rounded-lg">
              {schedule
                .sort((a, b) => {
                  // Rendezzés nap szerint, majd időpont szerint
                  const dayIndex1 = weekdays.findIndex(day => day.id === a.day);
                  const dayIndex2 = weekdays.findIndex(day => day.id === b.day);
                  
                  if (dayIndex1 !== dayIndex2) return dayIndex1 - dayIndex2;
                  return a.time.localeCompare(b.time);
                })
                .map(entry => (
                  <p key={entry.id} className="mb-1">
                    {entry.formattedText}
                  </p>
                ))}
            </div>
          </div>
        )}
      </CardContent>
      
      <CardFooter className="flex justify-end">
        <p className="text-sm text-gray-500">
          Használd ezt az eszközt a német időkifejezések és napi rutin tevékenységek gyakorlására!
        </p>
      </CardFooter>
    </Card>
  );
};

export default WeeklyPlanner;