import React, { useState, useRef, useEffect } from 'react';
import { But<PERSON> } from '../ui/button';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '../ui/card';
import { Progress } from '../ui/progress';
import { Badge } from '../ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '../ui/tabs';
import { Mic, Volume2, Loader2, Check, X, RefreshCw, Info } from 'lucide-react';
import { useToast } from '../ui/use-toast';
import { textToSpeech } from '../../services/openaiService';
import { recognizeSpeech, evaluatePronunciation } from '../../services/speechRecognitionService';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "../ui/tooltip";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "../ui/dialog";

// Példamondatok a gyakorláshoz
const practiceSentences = {
  beginner: [
    { id: 1, text: 'G<PERSON>n Tag', translation: 'Jó napot' },
    { id: 2, text: 'Wie geht es dir?', translation: 'Hogy vagy?' },
    { id: 3, text: 'Ich heiße Anna', translation: 'Anna vagyok' },
    { id: 4, text: 'Danke schön', translation: 'Köszönöm szépen' },
    { id: 5, text: 'Auf Wiedersehen', translation: 'Viszontlátásra' },
  ],
  intermediate: [
    { id: 1, text: 'Ich lerne seit zwei Jahren Deutsch', translation: 'Két éve tanulok németül' },
    { id: 2, text: 'Kannst du mir bitte helfen?', translation: 'Segítenél nekem, kérlek?' },
    { id: 3, text: 'Das Wetter ist heute schön', translation: 'Ma szép az idő' },
    { id: 4, text: 'Ich möchte eine Tasse Kaffee', translation: 'Szeretnék egy csésze kávét' },
    { id: 5, text: 'Wir treffen uns morgen', translation: 'Holnap találkozunk' },
  ],
  advanced: [
    { id: 1, text: 'Die deutsche Sprache ist sehr interessant', translation: 'A német nyelv nagyon érdekes' },
    { id: 2, text: 'Ich habe gestern einen Film auf Deutsch gesehen', translation: 'Tegnap láttam egy filmet németül' },
    { id: 3, text: 'Könnten Sie mir bitte erklären, wie ich zum Bahnhof komme?', translation: 'Elmagyarázná, hogyan jutok el a pályaudvarra?' },
    { id: 4, text: 'Die Aussprache ist manchmal schwierig', translation: 'A kiejtés néha nehéz' },
    { id: 5, text: 'Ich freue mich darauf, meine Deutschkenntnisse zu verbessern', translation: 'Várom, hogy fejlesszem a némettudásomat' },
  ],
};

// Szavak a gyakorláshoz
const practiceWords = {
  beginner: [
    { id: 1, text: 'Buch', translation: 'könyv' },
    { id: 2, text: 'Haus', translation: 'ház' },
    { id: 3, text: 'Schule', translation: 'iskola' },
    { id: 4, text: 'Freund', translation: 'barát' },
    { id: 5, text: 'Wasser', translation: 'víz' },
  ],
  intermediate: [
    { id: 1, text: 'Geburtstag', translation: 'születésnap' },
    { id: 2, text: 'Frühstück', translation: 'reggeli' },
    { id: 3, text: 'Schlüssel', translation: 'kulcs' },
    { id: 4, text: 'Kühlschrank', translation: 'hűtőszekrény' },
    { id: 5, text: 'Straße', translation: 'utca' },
  ],
  advanced: [
    { id: 1, text: 'Geschwindigkeit', translation: 'sebesség' },
    { id: 2, text: 'Entschuldigung', translation: 'bocsánat' },
    { id: 3, text: 'Schwierigkeiten', translation: 'nehézségek' },
    { id: 4, text: 'Wissenschaft', translation: 'tudomány' },
    { id: 5, text: 'Verantwortung', translation: 'felelősség' },
  ],
};

type PracticeItem = {
  id: number;
  text: string;
  translation: string;
};

type Level = 'beginner' | 'intermediate' | 'advanced';
type PracticeType = 'words' | 'sentences';

const PronunciationPractice: React.FC = () => {
  const [activeTab, setActiveTab] = useState<PracticeType>('words');
  const [level, setLevel] = useState<Level>('beginner');
  const [currentItemIndex, setCurrentItemIndex] = useState(0);
  const [isRecording, setIsRecording] = useState(false);
  const [isProcessing, setIsProcessing] = useState(false);
  const [isPlayingAudio, setIsPlayingAudio] = useState(false);
  const [score, setScore] = useState<number | null>(null);
  const [feedback, setFeedback] = useState<string>('');
  const [recognizedText, setRecognizedText] = useState<string>('');

  const mediaRecorderRef = useRef<MediaRecorder | null>(null);
  const audioChunksRef = useRef<Blob[]>([]);
  const audioRef = useRef<HTMLAudioElement | null>(null);

  const { toast } = useToast();

  // Az aktuális gyakorlási elemek kiválasztása
  const practiceItems = activeTab === 'words'
    ? practiceWords[level]
    : practiceSentences[level];

  const currentItem = practiceItems[currentItemIndex];

  // Hangfelvétel indítása
  const startRecording = async () => {
    try {
      // Mikrofon hozzáférés kérése
      const stream = await navigator.mediaDevices.getUserMedia({ audio: true });

      // MediaRecorder létrehozása
      const mediaRecorder = new MediaRecorder(stream);
      mediaRecorderRef.current = mediaRecorder;
      audioChunksRef.current = [];

      // Eseménykezelők beállítása
      mediaRecorder.ondataavailable = (event) => {
        if (event.data.size > 0) {
          audioChunksRef.current.push(event.data);
        }
      };

      mediaRecorder.onstop = async () => {
        // Hangfelvétel feldolgozása
        const audioBlob = new Blob(audioChunksRef.current, { type: 'audio/webm' });
        processRecording(audioBlob);

        // Stream leállítása
        stream.getTracks().forEach(track => track.stop());
      };

      // Felvétel indítása
      mediaRecorder.start();
      setIsRecording(true);
      setScore(null);
      setFeedback('');
      setRecognizedText('');

    } catch (error) {
      console.error('Hiba a hangfelvétel indításakor:', error);
      toast({
        title: 'Hiba',
        description: 'Nem sikerült elindítani a hangfelvételt. Ellenőrizd, hogy engedélyezve van-e a mikrofon használata.',
        variant: 'destructive',
      });
    }
  };

  // Hangfelvétel leállítása
  const stopRecording = () => {
    if (mediaRecorderRef.current && mediaRecorderRef.current.state !== 'inactive') {
      mediaRecorderRef.current.stop();
      setIsRecording(false);
      setIsProcessing(true);
    }
  };

  // Hangfelvétel feldolgozása
  const processRecording = async (audioBlob: Blob) => {
    try {
      // Hangfelismerés az OpenAI API segítségével
      const text = await recognizeSpeech(audioBlob, 'de');
      setRecognizedText(text);

      // Kiejtés értékelése
      const { score, feedback } = evaluatePronunciation(text, currentItem.text);
      setScore(score);
      setFeedback(feedback);

    } catch (error) {
      console.error('Hiba a hangfelvétel feldolgozásakor:', error);
      toast({
        title: 'Hiba',
        description: 'Nem sikerült feldolgozni a hangfelvételt. Próbáld újra később.',
        variant: 'destructive',
      });
    } finally {
      setIsProcessing(false);
    }
  };

  // Szöveg felolvasása
  const handleSpeech = async () => {
    try {
      setIsPlayingAudio(true);

      // Ha van korábbi audio, azt leállítjuk és felszabadítjuk
      if (audioRef.current) {
        audioRef.current.pause();
        URL.revokeObjectURL(audioRef.current.src);
      }

      // Lekérjük az audio URL-t az OpenAI API-tól
      const audioUrl = await textToSpeech(currentItem.text, 'de-DE');

      // Létrehozunk egy új audio elemet
      const audio = new Audio(audioUrl);
      audioRef.current = audio;

      // Lejátszás befejezésekor frissítjük az állapotot és felszabadítjuk az erőforrást
      audio.onended = () => {
        setIsPlayingAudio(false);
      };

      // Lejátszás
      await audio.play();
    } catch (error) {
      console.error('Hiba a szövegfelolvasás során:', error);
      setIsPlayingAudio(false);
      toast({
        title: 'Hiba',
        description: 'Nem sikerült lejátszani a hangot. Próbáld újra később.',
        variant: 'destructive',
      });
    }
  };

  // Következő elem
  const handleNext = () => {
    setCurrentItemIndex((prevIndex) =>
      prevIndex === practiceItems.length - 1 ? 0 : prevIndex + 1
    );
    setScore(null);
    setFeedback('');
    setRecognizedText('');
  };

  // Előző elem
  const handlePrevious = () => {
    setCurrentItemIndex((prevIndex) =>
      prevIndex === 0 ? practiceItems.length - 1 : prevIndex - 1
    );
    setScore(null);
    setFeedback('');
    setRecognizedText('');
  };

  // Komponens unmount esetén felszabadítjuk az erőforrásokat
  useEffect(() => {
    return () => {
      if (audioRef.current) {
        audioRef.current.pause();
        URL.revokeObjectURL(audioRef.current.src);
      }
    };
  }, []);

  return (
    <div className="w-full max-w-4xl mx-auto">
      <Tabs value={activeTab} onValueChange={(value) => setActiveTab(value as PracticeType)} className="w-full mb-6">
        <TabsList className="grid grid-cols-2">
          <TabsTrigger value="words">Szavak</TabsTrigger>
          <TabsTrigger value="sentences">Mondatok</TabsTrigger>
        </TabsList>
      </Tabs>

      <div className="flex justify-center mb-6">
        <Tabs value={level} onValueChange={(value) => setLevel(value as Level)}>
          <TabsList className="grid grid-cols-3">
            <TabsTrigger
              value="beginner"
              className={level === 'beginner' ? 'bg-green-100 data-[state=active]:bg-green-200' : ''}
            >
              Kezdő
            </TabsTrigger>
            <TabsTrigger
              value="intermediate"
              className={level === 'intermediate' ? 'bg-yellow-100 data-[state=active]:bg-yellow-200' : ''}
            >
              Középhaladó
            </TabsTrigger>
            <TabsTrigger
              value="advanced"
              className={level === 'advanced' ? 'bg-red-100 data-[state=active]:bg-red-200' : ''}
            >
              Haladó
            </TabsTrigger>
          </TabsList>
        </Tabs>
      </div>

      <Card className="mb-6">
        <CardHeader>
          <div className="flex justify-between items-center">
            <CardTitle>Kiejtésgyakorlás</CardTitle>
            <Dialog>
              <TooltipProvider>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <DialogTrigger asChild>
                      <Button variant="ghost" size="icon">
                        <Info className="h-5 w-5" />
                      </Button>
                    </DialogTrigger>
                  </TooltipTrigger>
                  <TooltipContent>
                    <p>Segítség</p>
                  </TooltipContent>
                </Tooltip>
              </TooltipProvider>
              <DialogContent>
                <DialogHeader>
                  <DialogTitle>Kiejtésgyakorló használata</DialogTitle>
                </DialogHeader>
                <DialogDescription>
                  <p className="mb-4">
                    A kiejtésgyakorló segítségével fejlesztheted a német kiejtésedet. Kövesd az alábbi lépéseket:
                  </p>
                  <ol className="list-decimal pl-5 space-y-2">
                    <li>Válaszd ki a gyakorlás típusát (szavak vagy mondatok) és a nehézségi szintet.</li>
                    <li>Kattints a "Meghallgat" gombra a helyes kiejtés meghallgatásához.</li>
                    <li>Kattints a "Felvétel" gombra, és mondd ki a szót vagy mondatot.</li>
                    <li>Kattints a "Leállítás" gombra a felvétel befejezéséhez.</li>
                    <li>A rendszer értékeli a kiejtésedet és visszajelzést ad.</li>
                    <li>Az "Előző" és "Következő" gombokkal navigálhatsz a gyakorlatok között.</li>
                  </ol>
                  <p className="mt-4">
                    Tipp: Gyakorolj rendszeresen a legjobb eredmény érdekében!
                  </p>
                </DialogDescription>
              </DialogContent>
            </Dialog>
          </div>
          <CardDescription>
            Hallgasd meg a szót/mondatot, majd próbáld meg kiejteni. A rendszer értékeli a kiejtésedet.
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex flex-col items-center space-y-6">
            <div className="text-center">
              <h3 className="text-2xl font-bold mb-2">{currentItem.text}</h3>
              <p className="text-gray-500">{currentItem.translation}</p>
            </div>

            <div className="flex space-x-4">
              <Button
                onClick={handlePrevious}
                variant="outline"
              >
                Előző
              </Button>
              <Button
                onClick={handleSpeech}
                disabled={isPlayingAudio}
                className="w-32"
              >
                {isPlayingAudio ? (
                  <Loader2 className="h-4 w-4 animate-spin mr-2" />
                ) : (
                  <Volume2 className="h-4 w-4 mr-2" />
                )}
                Meghallgat
              </Button>
              <Button
                onClick={handleNext}
                variant="outline"
              >
                Következő
              </Button>
            </div>

            <div className="w-full max-w-md">
              <div className="flex justify-center mb-4">
                <Button
                  onClick={isRecording ? stopRecording : startRecording}
                  disabled={isProcessing}
                  className={`w-40 ${isRecording ? 'bg-red-500 hover:bg-red-600' : 'hover:bg-language-primary'}`}
                >
                  {isProcessing ? (
                    <Loader2 className="h-4 w-4 animate-spin mr-2" />
                  ) : isRecording ? (
                    <X className="h-4 w-4 mr-2" />
                  ) : (
                    <Mic className="h-4 w-4 mr-2" />
                  )}
                  {isProcessing ? 'Feldolgozás...' : isRecording ? 'Leállítás' : 'Felvétel'}
                </Button>
              </div>

              {score !== null && (
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <span>Pontszám:</span>
                    <Badge variant={score >= 75 ? 'success' : score >= 50 ? 'warning' : 'destructive'}>
                      {score}%
                    </Badge>
                  </div>
                  <Progress value={score} className="h-2" />
                  <div className="text-center mt-2">
                    <p className="font-medium">{feedback}</p>
                  </div>
                  {recognizedText && (
                    <div className="mt-4 p-3 bg-gray-100 rounded-md">
                      <p className="text-sm text-gray-500 mb-1">Felismert szöveg:</p>
                      <p>{recognizedText}</p>
                    </div>
                  )}
                </div>
              )}
            </div>
          </div>
        </CardContent>
        <CardFooter className="flex justify-center">
          <Button
            variant="outline"
            onClick={() => {
              setScore(null);
              setFeedback('');
              setRecognizedText('');
            }}
            disabled={score === null}
          >
            <RefreshCw className="h-4 w-4 mr-2" />
            Újra
          </Button>
        </CardFooter>
      </Card>
    </div>
  );
};

export default PronunciationPractice;
