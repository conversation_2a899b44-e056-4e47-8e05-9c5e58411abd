import React, { useEffect } from 'react';

interface SEOHeadProps {
  title?: string;
  description?: string;
  keywords?: string;
  image?: string;
  url?: string;
  type?: string;
  author?: string;
  publishedTime?: string;
  modifiedTime?: string;
  section?: string;
  tags?: string[];
  noIndex?: boolean;
  canonical?: string;
}

const SEOHead: React.FC<SEOHeadProps> = ({
  title = "Magyar-Német Nyelvtanuló - Interaktív Német Nyelvtanulás",
  description = "Tanulj németül interaktív módszerekkel! Szókincs, mondatok, kvízek, játékok és kiejtésgyakorlás. Kezdő és középhaladó szintű német nyelvtanulás magyarul.",
  keywords = "német nyelvtanulás, német szókincs, német mondatok, német kvíz, német játékok, német kiejtés, nyelvtanulás, német nyelv, A1 német, B1 német, interaktív nyelvtanulás",
  image = "https://digitalisnemet.hu/og-image.jpg",
  url = "https://digitalisnemet.hu",
  type = "website",
  author = "Magyar-Német Nyelvtanuló",
  publishedTime,
  modifiedTime,
  section,
  tags = [],
  noIndex = false,
  canonical
}) => {
  const fullTitle = title.includes("Magyar-Német Nyelvtanuló") 
    ? title 
    : `${title} | Magyar-Német Nyelvtanuló`;

  const structuredData = {
    "@context": "https://schema.org",
    "@type": "EducationalOrganization",
    "name": "Magyar-Német Nyelvtanuló",
    "description": description,
    "url": url,
    "logo": {
      "@type": "ImageObject",
      "url": "https://digitalisnemet.hu/logo.png"
    },
    "sameAs": [
      "https://digitalisnemet.hu"
    ],
    "educationalCredentialAwarded": "Német nyelvtudás fejlesztés",
    "hasEducationalUse": "Nyelvtanulás",
    "learningResourceType": "Interaktív tananyag",
    "educationalLevel": ["Kezdő", "Középhaladó"],
    "inLanguage": ["hu", "de"],
    "audience": {
      "@type": "EducationalAudience",
      "educationalRole": "student"
    }
  };

  useEffect(() => {
    // Dinamikusan frissítjük a document head-et
    document.title = fullTitle;

    // Meta tagek frissítése
    updateMetaTag('description', description);
    updateMetaTag('keywords', keywords);
    updateMetaTag('author', author);

    // Open Graph tagek
    updateMetaTag('og:type', type, 'property');
    updateMetaTag('og:title', fullTitle, 'property');
    updateMetaTag('og:description', description, 'property');
    updateMetaTag('og:image', image, 'property');
    updateMetaTag('og:url', url, 'property');
    updateMetaTag('og:site_name', 'Magyar-Német Nyelvtanuló', 'property');

    // Twitter Card tagek
    updateMetaTag('twitter:card', 'summary_large_image');
    updateMetaTag('twitter:title', fullTitle);
    updateMetaTag('twitter:description', description);
    updateMetaTag('twitter:image', image);

    // Robots meta tag
    const robotsContent = noIndex ? 'noindex, nofollow' : 'index, follow, max-snippet:-1, max-image-preview:large, max-video-preview:-1';
    updateMetaTag('robots', robotsContent);

    // Canonical link
    if (canonical) {
      updateCanonicalLink(canonical);
    }

    // Structured Data
    updateStructuredData(structuredData);

  }, [fullTitle, description, keywords, author, type, image, url, noIndex, canonical, structuredData]);

  const updateMetaTag = (name: string, content: string, attribute: string = 'name') => {
    let meta = document.querySelector(`meta[${attribute}="${name}"]`) as HTMLMetaElement;
    if (!meta) {
      meta = document.createElement('meta');
      meta.setAttribute(attribute, name);
      document.head.appendChild(meta);
    }
    meta.content = content;
  };

  const updateCanonicalLink = (href: string) => {
    let link = document.querySelector('link[rel="canonical"]') as HTMLLinkElement;
    if (!link) {
      link = document.createElement('link');
      link.rel = 'canonical';
      document.head.appendChild(link);
    }
    link.href = href;
  };

  const updateStructuredData = (data: any) => {
    // Eltávolítjuk a korábbi structured data script-et
    const existingScript = document.querySelector('script[type="application/ld+json"]');
    if (existingScript) {
      existingScript.remove();
    }

    // Új structured data script hozzáadása
    const script = document.createElement('script');
    script.type = 'application/ld+json';
    script.textContent = JSON.stringify(data);
    document.head.appendChild(script);
  };

  return null;
};

export default SEOHead;
