import React from 'react';
import { Helmet } from 'react-helmet-async';

interface SEOHeadProps {
  title?: string;
  description?: string;
  keywords?: string;
  image?: string;
  url?: string;
  type?: string;
  author?: string;
  publishedTime?: string;
  modifiedTime?: string;
  section?: string;
  tags?: string[];
  noIndex?: boolean;
  canonical?: string;
}

const SEOHead: React.FC<SEOHeadProps> = ({
  title = "Magyar-Német Nyelvtanuló - Interaktív Német Nyelvtanulás",
  description = "Tanulj németül interaktív módszerekkel! Szókincs, mondatok, kvízek, játékok és kiejtésgyakorlás. Kezdő és középhaladó szintű német nyelvtanulás magyarul.",
  keywords = "német nyelvtanulás, német szókincs, német mondatok, német kvíz, német játékok, német kiejtés, nyelvtanulás, német nyelv, A1 német, B1 német, interaktív nyelvtanulás",
  image = "https://digitalisnemet.hu/og-image.jpg",
  url = "https://digitalisnemet.hu",
  type = "website",
  author = "Magyar-Német Nyelvtanuló",
  publishedTime,
  modifiedTime,
  section,
  tags = [],
  noIndex = false,
  canonical
}) => {
  const fullTitle = title.includes("Magyar-Német Nyelvtanuló") 
    ? title 
    : `${title} | Magyar-Német Nyelvtanuló`;

  const structuredData = {
    "@context": "https://schema.org",
    "@type": "EducationalOrganization",
    "name": "Magyar-Német Nyelvtanuló",
    "description": description,
    "url": url,
    "logo": {
      "@type": "ImageObject",
      "url": "https://digitalisnemet.hu/logo.png"
    },
    "sameAs": [
      "https://digitalisnemet.hu"
    ],
    "educationalCredentialAwarded": "Német nyelvtudás fejlesztés",
    "hasEducationalUse": "Nyelvtanulás",
    "learningResourceType": "Interaktív tananyag",
    "educationalLevel": ["Kezdő", "Középhaladó"],
    "inLanguage": ["hu", "de"],
    "audience": {
      "@type": "EducationalAudience",
      "educationalRole": "student"
    }
  };

  return (
    <Helmet>
      {/* Alapvető meta tagek */}
      <title>{fullTitle}</title>
      <meta name="description" content={description} />
      <meta name="keywords" content={keywords} />
      <meta name="author" content={author} />

      {/* Robots és indexelés */}
      {noIndex ? (
        <meta name="robots" content="noindex, nofollow" />
      ) : (
        <meta name="robots" content="index, follow, max-snippet:-1, max-image-preview:large, max-video-preview:-1" />
      )}

      {/* Canonical URL */}
      {canonical && <link rel="canonical" href={canonical} />}

      {/* Open Graph / Facebook */}
      <meta property="og:type" content={type} />
      <meta property="og:title" content={fullTitle} />
      <meta property="og:description" content={description} />
      <meta property="og:image" content={image} />
      <meta property="og:url" content={url} />
      <meta property="og:site_name" content="Magyar-Német Nyelvtanuló" />
      <meta property="og:locale" content="hu_HU" />
      <meta property="og:locale:alternate" content="de_DE" />

      {/* Article specifikus Open Graph tagek */}
      {type === "article" && (
        <>
          {author && <meta property="article:author" content={author} />}
          {publishedTime && <meta property="article:published_time" content={publishedTime} />}
          {modifiedTime && <meta property="article:modified_time" content={modifiedTime} />}
          {section && <meta property="article:section" content={section} />}
          {tags.map((tag, index) => (
            <meta key={index} property="article:tag" content={tag} />
          ))}
        </>
      )}

      {/* Twitter Card */}
      <meta name="twitter:card" content="summary_large_image" />
      <meta name="twitter:title" content={fullTitle} />
      <meta name="twitter:description" content={description} />
      <meta name="twitter:image" content={image} />
      <meta name="twitter:site" content="@digitalisnemet" />
      <meta name="twitter:creator" content="@digitalisnemet" />

      {/* További SEO meta tagek */}
      <meta name="theme-color" content="#2563eb" />
      <meta name="msapplication-TileColor" content="#2563eb" />
      <meta name="application-name" content="Magyar-Német Nyelvtanuló" />

      {/* Nyelv és régió */}
      <meta httpEquiv="content-language" content="hu" />
      <meta name="geo.region" content="HU" />
      <meta name="geo.country" content="Hungary" />

      {/* Structured Data */}
      <script type="application/ld+json">
        {JSON.stringify(structuredData)}
      </script>
    </Helmet>
  );
};

export default SEOHead;
