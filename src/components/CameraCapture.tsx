import React, { useRef, useState, useCallback } from 'react';
import { Button } from '@/components/ui/button';
import { Camera, X, Image, Loader2 } from 'lucide-react';
import { cn } from '@/lib/utils';

interface CameraCaptureProps {
  onCapture: (imageBlob: Blob) => void;
  onCancel: () => void;
  isLoading?: boolean;
}

const CameraCapture: React.FC<CameraCaptureProps> = ({
  onCapture,
  onCancel,
  isLoading = false
}) => {
  const videoRef = useRef<HTMLVideoElement>(null);
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const [stream, setStream] = useState<MediaStream | null>(null);
  const [isCameraActive, setIsCameraActive] = useState(false);
  const [capturedImage, setCapturedImage] = useState<string | null>(null);
  const [error, setError] = useState<string | null>(null);

  // Kamera indítása
  const startCamera = useCallback(async () => {
    try {
      setError(null);
      const mediaStream = await navigator.mediaDevices.getUserMedia({
        video: {
          facingMode: 'environment', // Hátsó kamera használata mobilon
          width: { ideal: 1280 },
          height: { ideal: 720 }
        }
      });

      if (videoRef.current) {
        videoRef.current.srcObject = mediaStream;
        setStream(mediaStream);
        setIsCameraActive(true);
      }
    } catch (err) {
      console.error('Hiba a kamera elérése során:', err);
      setError('Nem sikerült hozzáférni a kamerához. Kérlek, ellenőrizd a kamera engedélyeket.');
    }
  }, []);

  // Kamera leállítása
  const stopCamera = useCallback(() => {
    if (stream) {
      stream.getTracks().forEach(track => track.stop());
      setStream(null);
      setIsCameraActive(false);
    }
  }, [stream]);

  // Kép készítése
  const captureImage = useCallback(() => {
    if (videoRef.current && canvasRef.current) {
      const video = videoRef.current;
      const canvas = canvasRef.current;

      // Canvas méretének beállítása a videó méretére
      canvas.width = video.videoWidth;
      canvas.height = video.videoHeight;

      // Kép rajzolása a canvas-ra
      const context = canvas.getContext('2d');
      if (context) {
        context.drawImage(video, 0, 0, canvas.width, canvas.height);

        // Kép konvertálása blob-bá
        canvas.toBlob((blob) => {
          if (blob) {
            // Kép URL létrehozása a megjelenítéshez
            const imageUrl = URL.createObjectURL(blob);
            setCapturedImage(imageUrl);

            // Kamera leállítása
            stopCamera();

            // Kép átadása a szülő komponensnek
            onCapture(blob);
          }
        }, 'image/jpeg', 0.8);
      }
    }
  }, [onCapture, stopCamera]);

  // Kép törlése és kamera újraindítása
  const retakeImage = useCallback(() => {
    if (capturedImage) {
      URL.revokeObjectURL(capturedImage);
      setCapturedImage(null);
    }
    startCamera();
  }, [capturedImage, startCamera]);

  // Komponens törlődésekor leállítjuk a kamerát
  React.useEffect(() => {
    return () => {
      if (stream) {
        stream.getTracks().forEach(track => track.stop());
      }
      if (capturedImage) {
        URL.revokeObjectURL(capturedImage);
      }
    };
  }, [stream, capturedImage]);

  // Fájl kiválasztása
  const handleFileSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      const imageUrl = URL.createObjectURL(file);
      setCapturedImage(imageUrl);
      onCapture(file);
    }
  };

  return (
    <div className="w-full max-w-md mx-auto">
      {error && (
        <div className="mb-4 p-3 bg-red-100 text-red-700 rounded-md text-sm">
          {error}
        </div>
      )}

      <div className="relative bg-black rounded-lg overflow-hidden aspect-video mb-4">
        {!isCameraActive && !capturedImage && (
          <div className="absolute inset-0 flex flex-col items-center justify-center bg-gray-100">
            <Camera className="w-12 h-12 text-gray-400 mb-2" />
            <p className="text-gray-500 text-sm">Kamera nem aktív</p>
          </div>
        )}

        {isCameraActive && (
          <video
            ref={videoRef}
            autoPlay
            playsInline
            className="w-full h-full object-cover"
            onCanPlay={() => videoRef.current?.play()}
          />
        )}

        {capturedImage && (
          <img
            src={capturedImage}
            alt="Captured"
            className="w-full h-full object-contain"
          />
        )}

        <canvas ref={canvasRef} className="hidden" />
      </div>

      <div className="flex flex-wrap gap-2 justify-center">
        {!isCameraActive && !capturedImage && (
          <>
            <Button
              onClick={startCamera}
              className="bg-language-primary hover:bg-language-primary/90"
              disabled={isLoading}
            >
              <Camera className="mr-2 h-4 w-4" />
              Kamera indítása
            </Button>

            <div className="relative">
              <Button
                variant="outline"
                className="relative"
                disabled={isLoading}
              >
                <Image className="mr-2 h-4 w-4" />
                Kép kiválasztása
                <input
                  type="file"
                  accept="image/*"
                  className="absolute inset-0 opacity-0 cursor-pointer"
                  onChange={handleFileSelect}
                  disabled={isLoading}
                />
              </Button>
            </div>

            <Button
              variant="outline"
              onClick={onCancel}
              className="border-gray-300"
              disabled={isLoading}
            >
              <X className="mr-2 h-4 w-4" />
              Mégse
            </Button>
          </>
        )}

        {isCameraActive && (
          <>
            <Button
              onClick={captureImage}
              className={cn(
                "bg-language-primary hover:bg-language-primary/90",
                isLoading && "opacity-50 cursor-not-allowed"
              )}
              disabled={isLoading}
            >
              <Camera className="mr-2 h-4 w-4" />
              Kép készítése
            </Button>

            <Button
              variant="outline"
              onClick={stopCamera}
              className="border-gray-300"
              disabled={isLoading}
            >
              <X className="mr-2 h-4 w-4" />
              Mégse
            </Button>
          </>
        )}

        {capturedImage && !isLoading && (
          <>
            <Button
              onClick={retakeImage}
              variant="outline"
              className="border-gray-300"
            >
              <Camera className="mr-2 h-4 w-4" />
              Új kép
            </Button>

            <Button
              variant="outline"
              onClick={onCancel}
              className="border-gray-300"
            >
              <X className="mr-2 h-4 w-4" />
              Mégse
            </Button>
          </>
        )}

        {isLoading && (
          <div className="flex items-center justify-center p-2">
            <Loader2 className="h-5 w-5 animate-spin text-language-primary mr-2" />
            <span className="text-sm text-gray-600">Feldolgozás...</span>
          </div>
        )}
      </div>
    </div>
  );
};

export default CameraCapture;
