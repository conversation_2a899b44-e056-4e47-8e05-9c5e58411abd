import React, { useEffect, useRef } from "react";
import ReactMarkdown from "react-markdown";
import rehypeRaw from "rehype-raw";
import remarkGfm from "remark-gfm";
import { cn } from "@/lib/utils";

interface MarkdownRendererProps {
  content: string;
  className?: string;
}

const MarkdownRenderer: React.FC<MarkdownRendererProps> = ({ content, className }) => {
  const contentRef = useRef<HTMLDivElement>(null);

  // Táblázatok formázásának javítása
  useEffect(() => {
    if (contentRef.current) {
      // Táblázatok formázásának javítása
      const fixTables = () => {
        // Először ellenőrizzük, hogy van-e táblázat a tartalomban
        if (content.includes('|') && content.includes('---')) {
          // Várunk egy kicsit, hogy a markdown feldolgozása megtörténjen
          setTimeout(() => {
            const tables = contentRef.current?.querySelectorAll('table');
            tables?.forEach(table => {
              // Táblázat szélesség beállítása
              table.style.width = '100%';
              table.style.tableLayout = 'fixed';
              table.style.borderCollapse = 'collapse';
              table.style.border = '1px solid #e5e7eb';

              // Sorok formázása
              const rows = table.querySelectorAll('tr');
              rows.forEach(row => {
                row.style.borderBottom = '1px solid #e5e7eb';
              });

              // Cellák formázása
              const cells = table.querySelectorAll('th, td');
              cells.forEach(cell => {
                cell.style.padding = '8px';
                cell.style.border = '1px solid #e5e7eb';
                cell.style.wordBreak = 'break-word';
                cell.style.textAlign = 'left';
              });

              // Fejléc formázása
              const headers = table.querySelectorAll('th');
              headers.forEach(header => {
                header.style.backgroundColor = '#f9fafb';
                header.style.fontWeight = 'bold';
                header.style.borderBottom = '2px solid #d1d5db';
              });
            });
          }, 100); // 100ms késleltetés
        }
      };

      // Táblázatok javítása
      fixTables();
    }
  }, [content]);

  return (
    <div ref={contentRef} className={cn("markdown-content prose prose-slate max-w-none", className)}>
      <ReactMarkdown
        remarkPlugins={[remarkGfm]}
        rehypePlugins={[rehypeRaw]}
        components={{
          h1: ({ node, ...props }) => (
            <h1 {...props} className="text-2xl font-bold mt-6 mb-4 text-language-primary" />
          ),
          h2: ({ node, ...props }) => (
            <h2 {...props} className="text-xl font-bold mt-5 mb-3 text-language-primary" />
          ),
          h3: ({ node, ...props }) => (
            <h3 {...props} className="text-lg font-bold mt-4 mb-2 text-language-primary" />
          ),
          h4: ({ node, ...props }) => (
            <h4 {...props} className="text-base font-bold mt-3 mb-2 text-language-primary" />
          ),
          p: ({ node, ...props }) => <p {...props} className="my-3" />,
          ul: ({ node, ...props }) => <ul {...props} className="list-disc pl-6 my-3" />,
          ol: ({ node, ...props }) => <ol {...props} className="list-decimal pl-6 my-3" />,
          li: ({ node, ...props }) => <li {...props} className="my-1" />,
          a: ({ node, ...props }) => (
            <a {...props} className="text-blue-600 hover:underline" target="_blank" rel="noopener noreferrer" />
          ),
          blockquote: ({ node, ...props }) => (
            <blockquote {...props} className="border-l-4 border-gray-300 pl-4 italic my-4" />
          ),
          code: ({ node, inline, ...props }) =>
            inline ? (
              <code {...props} className="bg-gray-100 px-1 py-0.5 rounded text-sm font-mono" />
            ) : (
              <code {...props} className="block bg-gray-100 p-3 rounded-md text-sm font-mono overflow-x-auto my-4" />
            ),
          pre: ({ node, ...props }) => <pre {...props} className="bg-transparent p-0 my-0" />,
          hr: ({ node, ...props }) => <hr {...props} className="my-6 border-t border-gray-300" />,
          table: ({ node, ...props }) => (
            <div className="overflow-x-auto my-4 border border-gray-300 rounded shadow-sm">
              <table {...props} className="min-w-full divide-y divide-gray-300 table-fixed border-collapse" />
            </div>
          ),
          thead: ({ node, ...props }) => <thead {...props} className="bg-gray-50" />,
          tbody: ({ node, ...props }) => <tbody {...props} className="divide-y divide-gray-300 bg-white" />,
          tr: ({ node, ...props }) => <tr {...props} className="hover:bg-gray-50 border-b border-gray-300" />,
          th: ({ node, ...props }) => (
            <th {...props} className="px-4 py-3 text-left text-sm font-semibold text-gray-900 border border-gray-300 break-words bg-gray-50" />
          ),
          td: ({ node, ...props }) => (
            <td {...props} className="px-4 py-3 text-sm text-gray-700 border border-gray-300 break-words" />
          ),
          strong: ({ node, ...props }) => <strong {...props} className="font-bold" />,
          em: ({ node, ...props }) => <em {...props} className="italic" />,
        }}
      >
        {content}
      </ReactMarkdown>
    </div>
  );
};

export default MarkdownRenderer;
