import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { motion, AnimatePresence } from 'framer-motion';
import { X, CheckCircle, CreditCard, Users, Coins } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { useAuth } from '@/contexts/AuthContext';
import { FREE_TRIAL_PACKAGE } from '@/services/teacherService';

const WelcomeMessage: React.FC = () => {
  const [isVisible, setIsVisible] = useState(false);
  const { user } = useAuth();

  // Ellen<PERSON>zük, hogy új felhasználó-e (az elmúlt 1 percben regisztrált)
  useEffect(() => {
    if (user) {
      const registrationTime = new Date(user.createdAt || Date.now());
      const now = new Date();
      const diffInMinutes = (now.getTime() - registrationTime.getTime()) / (1000 * 60);

      // Ha az elmúlt 1 percben regisztrált, akkor új felhasználó
      if (diffInMinutes < 1) {
        // Kis késleltetéssel jelenítjük meg, hogy a felhasználó először lássa a főoldalt
        setTimeout(() => {
          setIsVisible(true);
        }, 1000);

        // Tároljuk el a localStorage-ban, hogy már megjelenítettük az üzenetet
        localStorage.setItem('welcomeMessageShown', 'true');
      } else {
        // Ellenőrizzük, hogy már megjelenítettük-e az üzenetet
        const welcomeMessageShown = localStorage.getItem('welcomeMessageShown');
        if (!welcomeMessageShown) {
          setIsVisible(true);
          localStorage.setItem('welcomeMessageShown', 'true');
        }
      }
    }
  }, [user]);

  const handleClose = () => {
    setIsVisible(false);
  };

  if (!isVisible) return null;

  return (
    <AnimatePresence>
      {isVisible && (
        <motion.div
          className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          onClick={handleClose}
        >
          <motion.div
            className="max-w-md w-full"
            initial={{ scale: 0.9, opacity: 0 }}
            animate={{ scale: 1, opacity: 1 }}
            exit={{ scale: 0.9, opacity: 0 }}
            transition={{ type: 'spring', damping: 25, stiffness: 300 }}
            onClick={(e) => e.stopPropagation()}
          >
            <Card className="border-2 border-green-500">
              <CardHeader className="pb-2 relative">
                <Button
                  variant="ghost"
                  size="icon"
                  className="absolute right-2 top-2"
                  onClick={handleClose}
                >
                  <X className="h-4 w-4" />
                </Button>
                <div className="flex justify-center mb-2">
                  <div className="rounded-full bg-green-100 p-3">
                    <CheckCircle className="h-8 w-8 text-green-600" />
                  </div>
                </div>
                <CardTitle className="text-2xl font-bold text-center">Sikeres regisztráció!</CardTitle>
                <CardDescription className="text-center">
                  Köszönjük, hogy regisztráltál a Magyar-Német Nyelvtanuló alkalmazásba!
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <p className="text-center">
                  Ingyenes próba csomagod aktiválva lett, amely <strong>{FREE_TRIAL_PACKAGE.points} pontot</strong> tartalmaz az AI funkciók használatához.
                </p>

                <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                  <h3 className="font-medium text-blue-700 mb-2 flex items-center">
                    <CheckCircle className="h-4 w-4 mr-2" />
                    Minden funkció elérhető számodra
                  </h3>
                  <p className="text-sm text-gray-600 mb-3">
                    Minden felhasználó hozzáfér az összes funkcióhoz, a különbség a használható pontok mennyiségében van.
                  </p>
                  <ul className="space-y-2 text-sm">
                    <li className="flex items-start">
                      <CheckCircle className="h-4 w-4 text-green-500 mr-2 mt-0.5 flex-shrink-0" />
                      <div>
                        <span className="font-medium">Ingyenes próba csomag</span>
                        <p className="text-gray-600">{FREE_TRIAL_PACKAGE.points} pont az AI funkciók használatához</p>
                      </div>
                    </li>
                    <li className="flex items-start">
                      <CreditCard className="h-4 w-4 text-purple-500 mr-2 mt-0.5 flex-shrink-0" />
                      <div>
                        <span className="font-medium">Havi előfizetés</span>
                        <p className="text-gray-600">500 pont havonta az AI funkciók használatához (2990 Ft/hó)</p>
                      </div>
                    </li>
                    <li className="flex items-start">
                      <Users className="h-4 w-4 text-blue-500 mr-2 mt-0.5 flex-shrink-0" />
                      <div>
                        <span className="font-medium">Tanári csomag</span>
                        <p className="text-gray-600">1000 pont havonta és diákok kezelése (7500 Ft/hó)</p>
                      </div>
                    </li>
                  </ul>
                </div>
              </CardContent>
              <CardFooter className="flex flex-col space-y-3">
                <Button
                  onClick={handleClose}
                  className="w-full"
                >
                  Kezdd el a tanulást
                </Button>
                <Link to="/subscription" className="w-full">
                  <Button
                    variant="outline"
                    className="w-full"
                    onClick={handleClose}
                  >
                    Csomagok megtekintése
                  </Button>
                </Link>
              </CardFooter>
            </Card>
          </motion.div>
        </motion.div>
      )}
    </AnimatePresence>
  );
};

export default WelcomeMessage;
