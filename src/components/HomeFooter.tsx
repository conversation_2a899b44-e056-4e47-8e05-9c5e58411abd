import React from "react";
import { <PERSON> } from "react-router-dom";
import { Github, Mail, ChevronRight, Facebook, Instagram, Twitter, Globe, BookOpen } from "lucide-react";
import { Separator } from "@/components/ui/separator";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { motion } from "framer-motion";

const HomeFooter = () => {
  const currentYear = new Date().getFullYear();

  return (
    <footer className="relative mt-24">
      {/* Dekoratív ív a footer tetején */}
      <div className="absolute top-0 left-0 w-full h-16 transform -translate-y-full overflow-hidden">
        <div className="absolute bottom-0 inset-x-0 h-16 bg-gradient-to-b from-transparent to-language-primary/5"></div>
        <svg
          className="absolute bottom-0 w-full h-16 text-white/90 fill-current"
          viewBox="0 0 1200 120"
          preserveAspectRatio="none"
        >
          <path
            d="M321.39,56.44c58-10.79,114.16-30.13,172-41.86,82.39-16.72,168.19-17.73,250.45-.39C823.78,31,906.67,72,985.66,92.83c70.05,18.48,146.53,26.09,214.34,3V120H0V0C-0.48,73,111.44,96.33,321.39,56.44Z"
          ></path>
        </svg>
      </div>

      <div className="bg-gradient-to-br from-white via-white to-language-primary/5 pt-16 pb-8">
        <div className="container mx-auto px-6">
          {/* Logo és tagline */}
          <div className="flex flex-col sm:flex-row justify-between items-center mb-12">
            <div className="flex items-center mb-6 sm:mb-0">
              <div className="relative">
                <motion.div
                  className="absolute -inset-1 rounded-full bg-gradient-to-r from-language-primary to-language-accent opacity-70 blur-sm"
                  animate={{
                    scale: [1, 1.05, 1],
                    opacity: [0.7, 0.8, 0.7]
                  }}
                  transition={{
                    duration: 3,
                    repeat: Infinity,
                    repeatType: "reverse"
                  }}
                />
                <div className="relative bg-white rounded-full p-1">
                  <BookOpen className="w-6 h-6 text-language-primary" />
                </div>
              </div>
              <div className="flex flex-col ml-3">
                <span className="bg-gradient-to-r from-language-primary to-language-accent bg-clip-text text-transparent leading-tight font-bold text-xl">Magyar-Német</span>
                <span className="text-xs text-gray-500 font-normal">Nyelvtanuló</span>
              </div>
            </div>
            <div className="flex space-x-3">
              <a
                href="https://facebook.com"
                target="_blank"
                rel="noopener noreferrer"
                className="bg-white p-2.5 rounded-full shadow-sm text-gray-500 hover:text-blue-600 hover:shadow-md transition-all"
                aria-label="Facebook"
              >
                <Facebook className="h-5 w-5" />
              </a>
              <a
                href="https://instagram.com"
                target="_blank"
                rel="noopener noreferrer"
                className="bg-white p-2.5 rounded-full shadow-sm text-gray-500 hover:text-pink-600 hover:shadow-md transition-all"
                aria-label="Instagram"
              >
                <Instagram className="h-5 w-5" />
              </a>
              <a
                href="https://twitter.com"
                target="_blank"
                rel="noopener noreferrer"
                className="bg-white p-2.5 rounded-full shadow-sm text-gray-500 hover:text-blue-400 hover:shadow-md transition-all"
                aria-label="Twitter"
              >
                <Twitter className="h-5 w-5" />
              </a>
              <a
                href="https://github.com/username/magyar-german-nyelv-tanulo"
                target="_blank"
                rel="noopener noreferrer"
                className="bg-white p-2.5 rounded-full shadow-sm text-gray-500 hover:text-gray-800 hover:shadow-md transition-all"
                aria-label="GitHub"
              >
                <Github className="h-5 w-5" />
              </a>
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-12 gap-10">
            {/* Applikáció leírás */}
            <div className="md:col-span-5">
              <h4 className="text-lg font-semibold text-gray-800 mb-4">Az alkalmazásról</h4>
              <p className="text-gray-600 mb-6 leading-relaxed">
                A Magyar-Német Nyelvtanuló App segít a németet tanulóknak B1 szintig fejleszteni a nyelvtudásukat
                interaktív és játékos módon. Tanulj bárhol, bármikor!
              </p>
              <div className="flex space-x-2">
                <Badge variant="plain" className="bg-language-primary/10 text-language-primary text-xs px-3 py-1">
                  240+ szó
                </Badge>
                <Badge variant="plain" className="bg-language-secondary/10 text-language-secondary text-xs px-3 py-1">
                  Mondatok
                </Badge>
                <Badge variant="plain" className="bg-language-accent/10 text-language-accent text-xs px-3 py-1">
                  Kvízkérdések
                </Badge>
                <Badge variant="plain" className="bg-language-quaternary/10 text-language-quaternary text-xs px-3 py-1">
                  6 játék
                </Badge>
              </div>
            </div>

            {/* Linkek szekciók */}
            <div className="md:col-span-7">
              <div className="grid grid-cols-1 sm:grid-cols-3 gap-8">
                {/* Navigáció szekció */}
                <div>
                  <h4 className="text-sm font-semibold text-gray-800 uppercase tracking-wider mb-4">Navigáció</h4>
                  <ul className="space-y-3">
                    <li>
                      <Link to="/vocabulary" className="text-gray-600 hover:text-language-primary transition-colors flex items-center group text-sm">
                        <ChevronRight className="h-3 w-3 mr-1 opacity-0 group-hover:opacity-100 transform translate-x-0 group-hover:translate-x-1 transition-all" />
                        Szókincs
                      </Link>
                    </li>
                    <li>
                      <Link to="/phrases" className="text-gray-600 hover:text-language-primary transition-colors flex items-center group text-sm">
                        <ChevronRight className="h-3 w-3 mr-1 opacity-0 group-hover:opacity-100 transform translate-x-0 group-hover:translate-x-1 transition-all" />
                        Mondatok
                      </Link>
                    </li>
                    <li>
                      <Link to="/quiz" className="text-gray-600 hover:text-language-primary transition-colors flex items-center group text-sm">
                        <ChevronRight className="h-3 w-3 mr-1 opacity-0 group-hover:opacity-100 transform translate-x-0 group-hover:translate-x-1 transition-all" />
                        Kvíz
                      </Link>
                    </li>
                    <li>
                      <Link to="/games" className="text-gray-600 hover:text-language-primary transition-colors flex items-center group text-sm">
                        <ChevronRight className="h-3 w-3 mr-1 opacity-0 group-hover:opacity-100 transform translate-x-0 group-hover:translate-x-1 transition-all" />
                        Játékok
                      </Link>
                    </li>
                    <li>
                      <Link to="/assistant" className="text-gray-600 hover:text-language-primary transition-colors flex items-center group text-sm">
                        <ChevronRight className="h-3 w-3 mr-1 opacity-0 group-hover:opacity-100 transform translate-x-0 group-hover:translate-x-1 transition-all" />
                        Asszisztens
                      </Link>
                    </li>
                  </ul>
                </div>

                {/* Fiók szekció */}
                <div>
                  <h4 className="text-sm font-semibold text-gray-800 uppercase tracking-wider mb-4">Fiók</h4>
                  <ul className="space-y-3">
                    <li>
                      <Link to="/profile" className="text-gray-600 hover:text-language-primary transition-colors flex items-center group text-sm">
                        <ChevronRight className="h-3 w-3 mr-1 opacity-0 group-hover:opacity-100 transform translate-x-0 group-hover:translate-x-1 transition-all" />
                        Profil
                      </Link>
                    </li>
                    <li>
                      <Link to="/subscription" className="text-gray-600 hover:text-language-primary transition-colors flex items-center group text-sm">
                        <ChevronRight className="h-3 w-3 mr-1 opacity-0 group-hover:opacity-100 transform translate-x-0 group-hover:translate-x-1 transition-all" />
                        Előfizetés
                      </Link>
                    </li>
                    <li>
                      <Link to="/points" className="text-gray-600 hover:text-language-primary transition-colors flex items-center group text-sm">
                        <ChevronRight className="h-3 w-3 mr-1 opacity-0 group-hover:opacity-100 transform translate-x-0 group-hover:translate-x-1 transition-all" />
                        Pontrendszer
                      </Link>
                    </li>
                    <li>
                      <Link to="/invitations" className="text-gray-600 hover:text-language-primary transition-colors flex items-center group text-sm">
                        <ChevronRight className="h-3 w-3 mr-1 opacity-0 group-hover:opacity-100 transform translate-x-0 group-hover:translate-x-1 transition-all" />
                        Meghívók
                      </Link>
                    </li>
                  </ul>
                </div>

                {/* Támogatás szekció */}
                <div>
                  <h4 className="text-sm font-semibold text-gray-800 uppercase tracking-wider mb-4">Támogatás</h4>
                  <ul className="space-y-3">
                    <li>
                      <Link to="/support" className="text-gray-600 hover:text-language-primary transition-colors flex items-center group text-sm">
                        <ChevronRight className="h-3 w-3 mr-1 opacity-0 group-hover:opacity-100 transform translate-x-0 group-hover:translate-x-1 transition-all" />
                        Segítség
                      </Link>
                    </li>
                    <li>
                      <a
                        href="mailto:<EMAIL>"
                        className="text-gray-600 hover:text-language-primary transition-colors flex items-center group text-sm"
                      >
                        <ChevronRight className="h-3 w-3 mr-1 opacity-0 group-hover:opacity-100 transform translate-x-0 group-hover:translate-x-1 transition-all" />
                        Email
                      </a>
                    </li>
                    <li>
                      <Link to="/faq" className="text-gray-600 hover:text-language-primary transition-colors flex items-center group text-sm">
                        <ChevronRight className="h-3 w-3 mr-1 opacity-0 group-hover:opacity-100 transform translate-x-0 group-hover:translate-x-1 transition-all" />
                        GYIK
                      </Link>
                    </li>
                  </ul>
                </div>
              </div>
            </div>
          </div>

          <div className="mt-16 pt-6 border-t border-gray-200">
            <div className="flex flex-col md:flex-row justify-between items-center">
              <div className="mb-4 md:mb-0">
                <div className="text-sm text-gray-500">
                  &copy; {currentYear} Magyar-Német Nyelvtanuló App
                </div>
                <div className="mt-1 text-xs text-gray-500">
                  <span>Tervezte és fejlesztette: Bartus Norbert | Minden jog fenntartva</span>
                </div>
              </div>
              <div className="flex flex-wrap gap-6 justify-center">
                <Link to="/privacy" className="text-sm text-gray-500 hover:text-language-primary transition-colors">
                  Adatvédelmi irányelvek
                </Link>
                <Link to="/terms" className="text-sm text-gray-500 hover:text-language-primary transition-colors">
                  Felhasználási feltételek
                </Link>
                <Link to="/cookies" className="text-sm text-gray-500 hover:text-language-primary transition-colors">
                  Cookie-k
                </Link>
              </div>
            </div>
          </div>
        </div>
      </div>
    </footer>
  );
};

export default HomeFooter;
