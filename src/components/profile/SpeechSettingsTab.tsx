import React from 'react';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Label } from '@/components/ui/label';
import { Slider } from '@/components/ui/slider';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { RotateCcw, Volume2, Loader2 } from 'lucide-react';
import { useSpeechSettings, VoiceOption } from '@/contexts/SpeechSettingsContext';
import { textToSpeech } from '@/services/openaiService';

const SpeechSettingsTab: React.FC = () => {
  const {
    settings,
    updateSpeed,
    updateDefaultVoice,
    updateHungarianVoice,
    updateGermanVoice,
    resetSettings
  } = useSpeechSettings();

  const [isPlayingHungarian, setIsPlayingHungarian] = React.useState(false);
  const [isPlayingGerman, setIsPlayingGerman] = React.useState(false);
  const audioRef = React.useRef<HTMLAudioElement | null>(null);

  // Hang tesztelése
  const testVoice = async (voice: VoiceOption, language: 'hu-HU' | 'de-DE') => {
    try {
      if (language === 'hu-HU') {
        setIsPlayingHungarian(true);
      } else {
        setIsPlayingGerman(true);
      }

      // Ha van korábbi audio, azt leállítjuk és felszabadítjuk
      if (audioRef.current) {
        audioRef.current.pause();
        URL.revokeObjectURL(audioRef.current.src);
      }

      // Tesztszöveg a kiválasztott nyelven
      const testText = language === 'hu-HU' 
        ? 'Üdvözöllek! Ez egy teszt a kiválasztott hanggal.' 
        : 'Hallo! Dies ist ein Test mit der ausgewählten Stimme.';

      // Lekérjük az audio URL-t az OpenAI API-tól
      const audioUrl = await textToSpeech(testText, language, settings.speed, voice);

      // Létrehozunk egy új audio elemet
      const audio = new Audio(audioUrl);
      audioRef.current = audio;

      // Lejátszás befejezésekor frissítjük az állapotot és felszabadítjuk az erőforrást
      audio.onended = () => {
        if (language === 'hu-HU') {
          setIsPlayingHungarian(false);
        } else {
          setIsPlayingGerman(false);
        }
        URL.revokeObjectURL(audioUrl);
      };

      // Lejátszás
      await audio.play();
    } catch (error) {
      console.error('Hiba a hang tesztelése során:', error);
      if (language === 'hu-HU') {
        setIsPlayingHungarian(false);
      } else {
        setIsPlayingGerman(false);
      }
    }
  };

  // Beállítások visszaállítása
  const handleReset = () => {
    resetSettings();
  };

  // Hangok leírása
  const voiceDescriptions: Record<VoiceOption, string> = {
    alloy: 'Semleges, kiegyensúlyozott hang',
    echo: 'Mélyebb, nyugodt hang',
    fable: 'Lágy, barátságos hang',
    onyx: 'Komoly, határozott hang',
    nova: 'Energikus, lelkes hang',
    shimmer: 'Világos, tiszta hang'
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle>Beszédbeállítások</CardTitle>
        <CardDescription>
          Itt állíthatod be a szövegfelolvasás paramétereit
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Beszédsebesség beállítása */}
        <div className="space-y-3">
          <div className="flex items-center justify-between">
            <Label htmlFor="speech-speed" className="text-base font-medium">
              Beszédsebesség: {settings.speed.toFixed(2)}x
            </Label>
          </div>
          <Slider
            id="speech-speed"
            min={0.25}
            max={2.0}
            step={0.05}
            value={[settings.speed]}
            onValueChange={(values) => updateSpeed(values[0])}
            className="w-full"
          />
          <div className="flex justify-between text-xs text-gray-500">
            <span>Lassú (0.25x)</span>
            <span>Normál (1.0x)</span>
            <span>Gyors (2.0x)</span>
          </div>
        </div>

        <div className="border-t pt-4">
          <h3 className="text-base font-medium mb-3">Magyar nyelv hangja</h3>
          <div className="space-y-4">
            <RadioGroup 
              value={settings.hungarianVoice} 
              onValueChange={(value) => updateHungarianVoice(value as VoiceOption)}
              className="grid grid-cols-1 md:grid-cols-2 gap-2"
            >
              {Object.entries(voiceDescriptions).map(([voice, description]) => (
                <div key={voice} className="flex items-start space-x-2">
                  <RadioGroupItem value={voice} id={`hu-${voice}`} />
                  <div className="grid gap-1">
                    <Label htmlFor={`hu-${voice}`} className="font-medium">
                      {voice.charAt(0).toUpperCase() + voice.slice(1)}
                    </Label>
                    <p className="text-xs text-gray-500">{description}</p>
                  </div>
                </div>
              ))}
            </RadioGroup>
            <Button
              variant="outline"
              size="sm"
              onClick={() => testVoice(settings.hungarianVoice, 'hu-HU')}
              disabled={isPlayingHungarian || isPlayingGerman}
              className="mt-2"
            >
              {isPlayingHungarian ? (
                <>
                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                  Lejátszás...
                </>
              ) : (
                <>
                  <Volume2 className="h-4 w-4 mr-2" />
                  Hang tesztelése
                </>
              )}
            </Button>
          </div>
        </div>

        <div className="border-t pt-4">
          <h3 className="text-base font-medium mb-3">Német nyelv hangja</h3>
          <div className="space-y-4">
            <RadioGroup 
              value={settings.germanVoice} 
              onValueChange={(value) => updateGermanVoice(value as VoiceOption)}
              className="grid grid-cols-1 md:grid-cols-2 gap-2"
            >
              {Object.entries(voiceDescriptions).map(([voice, description]) => (
                <div key={voice} className="flex items-start space-x-2">
                  <RadioGroupItem value={voice} id={`de-${voice}`} />
                  <div className="grid gap-1">
                    <Label htmlFor={`de-${voice}`} className="font-medium">
                      {voice.charAt(0).toUpperCase() + voice.slice(1)}
                    </Label>
                    <p className="text-xs text-gray-500">{description}</p>
                  </div>
                </div>
              ))}
            </RadioGroup>
            <Button
              variant="outline"
              size="sm"
              onClick={() => testVoice(settings.germanVoice, 'de-DE')}
              disabled={isPlayingHungarian || isPlayingGerman}
              className="mt-2"
            >
              {isPlayingGerman ? (
                <>
                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                  Lejátszás...
                </>
              ) : (
                <>
                  <Volume2 className="h-4 w-4 mr-2" />
                  Hang tesztelése
                </>
              )}
            </Button>
          </div>
        </div>
      </CardContent>
      <CardFooter>
        <Button
          variant="outline"
          onClick={handleReset}
          className="flex items-center"
        >
          <RotateCcw className="h-4 w-4 mr-2" />
          Alapértelmezett beállítások
        </Button>
      </CardFooter>
    </Card>
  );
};

export default SpeechSettingsTab;
