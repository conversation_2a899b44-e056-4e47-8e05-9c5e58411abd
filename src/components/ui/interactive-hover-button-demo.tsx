import React from "react";
import { Book, <PERSON><PERSON>, Crown, MessageSquare, Gamepad2, VolumeIcon } from "lucide-react";
import { InteractiveHoverButton } from "./interactive-hover-button";

function InteractiveHoverButtonDemo() {
  return (
    <div className="p-8 bg-gray-50 rounded-lg">
      <h2 className="text-2xl font-bold mb-6 text-center">InteractiveHoverButton Példák</h2>
      
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <div className="flex flex-col items-center space-y-4">
          <h3 className="font-medium">Alap gombok</h3>
          <div className="flex flex-col space-y-4">
            <InteractiveHoverButton 
              text="Szókincs" 
              icon={<Book className="h-4 w-4" />} 
              colorClass="bg-language-primary" 
              className="w-48 py-3 font-medium text-sm hover:text-white" 
            />
            <InteractiveHoverButton 
              text="Mondatok" 
              icon={<MessageSquare className="h-4 w-4" />} 
              colorClass="bg-language-secondary" 
              className="w-48 py-3 font-medium text-sm hover:text-white" 
            />
            <InteractiveHoverButton 
              text="Játékok" 
              icon={<Gamepad2 className="h-4 w-4" />} 
              colorClass="bg-language-quaternary" 
              className="w-48 py-3 font-medium text-sm hover:text-white" 
            />
          </div>
        </div>
        
        <div className="flex flex-col items-center space-y-4">
          <h3 className="font-medium">Nagy gombok</h3>
          <div className="flex flex-col space-y-4">
            <InteractiveHoverButton 
              text="Kezdj tanulni most" 
              icon={<Book className="h-5 w-5" />} 
              colorClass="bg-language-primary" 
              className="w-64 py-6 font-medium hover:text-white" 
            />
            <InteractiveHoverButton 
              text="Beszélgess az asszisztenssel" 
              icon={<Bot className="h-5 w-5" />} 
              colorClass="bg-language-secondary" 
              className="w-64 py-6 font-medium hover:text-white border-language-border bg-white/80" 
            />
          </div>
        </div>
        
        <div className="flex flex-col items-center space-y-4">
          <h3 className="font-medium">Egyéb színek</h3>
          <div className="flex flex-col space-y-4">
            <InteractiveHoverButton 
              text="Előfizetés" 
              icon={<Crown className="h-4 w-4" />} 
              colorClass="bg-purple-500" 
              className="w-48 py-3 font-medium text-sm hover:text-white" 
            />
            <InteractiveHoverButton 
              text="Kiejtés" 
              icon={<VolumeIcon className="h-4 w-4" />} 
              colorClass="bg-teal-500" 
              className="w-48 py-3 font-medium text-sm hover:text-white" 
            />
            <InteractiveHoverButton 
              text="Egyedi gomb" 
              colorClass="bg-gradient-to-r from-pink-500 to-orange-500" 
              className="w-48 py-3 font-medium text-sm hover:text-white" 
            />
          </div>
        </div>
      </div>
    </div>
  );
}

export { InteractiveHoverButtonDemo };
