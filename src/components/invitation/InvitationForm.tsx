import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { toast } from 'sonner';
import { invitationService } from '@/services/apiService';
import { Mail, Loader2 } from 'lucide-react';

interface InvitationFormProps {
  onSuccess?: () => void;
}

const InvitationForm: React.FC<InvitationFormProps> = ({ onSuccess }) => {
  const [email, setEmail] = useState('');
  const [isLoading, setIsLoading] = useState(false);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!email) {
      toast.error('Kérjük, adja meg az email címet');
      return;
    }

    try {
      setIsLoading(true);
      await invitationService.createInvitation(email);
      toast.success('<PERSON><PERSON><PERSON><PERSON><PERSON> sikeresen elküldve');
      setEmail('');
      if (onSuccess) {
        onSuccess();
      }
    } catch (error: any) {
      console.error('Hiba a meghívó küldésekor:', error);
      toast.error(error.response?.data?.message || 'Hiba a meghívó küldésekor');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle>Barát meghívása</CardTitle>
        <CardDescription>
          Hívd meg barátaidat a Magyar-Német Nyelvtanuló alkalmazásba
        </CardDescription>
      </CardHeader>
      <CardContent>
        <form onSubmit={handleSubmit}>
          <div className="grid w-full items-center gap-4">
            <div className="flex flex-col space-y-1.5">
              <Label htmlFor="email">Email cím</Label>
              <div className="flex">
                <Input
                  id="email"
                  type="email"
                  placeholder="barátod@példa.hu"
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                  disabled={isLoading}
                  className="flex-1"
                />
                <Button 
                  type="submit" 
                  disabled={isLoading} 
                  className="ml-2"
                >
                  {isLoading ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      Küldés...
                    </>
                  ) : (
                    <>
                      <Mail className="mr-2 h-4 w-4" />
                      Meghívás
                    </>
                  )}
                </Button>
              </div>
            </div>
          </div>
        </form>
      </CardContent>
      <CardFooter className="flex justify-between">
        <p className="text-sm text-muted-foreground">
          A meghívott személy email címére küldünk egy meghívó linket, amelyen keresztül regisztrálhat az alkalmazásba.
        </p>
      </CardFooter>
    </Card>
  );
};

export default InvitationForm;
