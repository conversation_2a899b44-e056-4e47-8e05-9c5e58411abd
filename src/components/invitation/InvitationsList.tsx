import React, { useState, useEffect } from 'react';
import { invitationService } from '@/services/apiService';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Badge } from '@/components/ui/badge';
import { toast } from 'sonner';
import { Mail, RefreshCw, Trash, Send } from 'lucide-react';
import { format } from 'date-fns';
import { hu } from 'date-fns/locale';

interface Invitation {
  id: string;
  email: string;
  status: 'pending' | 'accepted' | 'expired';
  expiresAt: string;
  createdAt: string;
  invitee?: {
    id: string;
    name: string;
    email: string;
  };
}

const InvitationsList: React.FC = () => {
  const [invitations, setInvitations] = useState<Invitation[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isDeleting, setIsDeleting] = useState<string | null>(null);
  const [isResending, setIsResending] = useState<string | null>(null);

  const loadInvitations = async () => {
    try {
      setIsLoading(true);
      const response = await invitationService.getInvitations();
      setInvitations(response.data);
    } catch (error) {
      console.error('Hiba a meghívók betöltésekor:', error);
      toast.error('Nem sikerült betölteni a meghívókat');
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    loadInvitations();
  }, []);

  const handleDelete = async (id: string) => {
    try {
      setIsDeleting(id);
      await invitationService.deleteInvitation(id);
      toast.success('Meghívó sikeresen törölve');
      setInvitations(invitations.filter(invitation => invitation.id !== id));
    } catch (error) {
      console.error('Hiba a meghívó törlésekor:', error);
      toast.error('Nem sikerült törölni a meghívót');
    } finally {
      setIsDeleting(null);
    }
  };

  const handleResend = async (id: string) => {
    try {
      setIsResending(id);
      await invitationService.resendInvitation(id);
      toast.success('Meghívó sikeresen újraküldve');
      loadInvitations();
    } catch (error) {
      console.error('Hiba a meghívó újraküldésekor:', error);
      toast.error('Nem sikerült újraküldeni a meghívót');
    } finally {
      setIsResending(null);
    }
  };

  const getStatusBadge = (status: string, expiresAt: string) => {
    const isExpired = new Date(expiresAt) < new Date();
    
    if (status === 'accepted') {
      return <Badge className="bg-green-500">Elfogadva</Badge>;
    } else if (status === 'pending' && !isExpired) {
      return <Badge className="bg-blue-500">Függőben</Badge>;
    } else {
      return <Badge className="bg-red-500">Lejárt</Badge>;
    }
  };

  const formatDate = (dateString: string) => {
    return format(new Date(dateString), 'yyyy. MMMM d. HH:mm', { locale: hu });
  };

  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between">
        <div>
          <CardTitle>Meghívók</CardTitle>
          <CardDescription>
            Az általad küldött meghívók listája
          </CardDescription>
        </div>
        <Button 
          variant="outline" 
          size="sm" 
          onClick={loadInvitations} 
          disabled={isLoading}
        >
          <RefreshCw className={`h-4 w-4 mr-2 ${isLoading ? 'animate-spin' : ''}`} />
          Frissítés
        </Button>
      </CardHeader>
      <CardContent>
        {invitations.length === 0 && !isLoading ? (
          <div className="text-center py-8">
            <Mail className="h-12 w-12 mx-auto text-gray-400 mb-4" />
            <h3 className="text-lg font-medium text-gray-900">Nincs még meghívó</h3>
            <p className="mt-1 text-sm text-gray-500">
              Használd a "Barát meghívása" űrlapot, hogy meghívj valakit az alkalmazásba.
            </p>
          </div>
        ) : (
          <div className="overflow-x-auto">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Email</TableHead>
                  <TableHead>Állapot</TableHead>
                  <TableHead>Létrehozva</TableHead>
                  <TableHead>Lejárat</TableHead>
                  <TableHead className="text-right">Műveletek</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {isLoading ? (
                  <TableRow>
                    <TableCell colSpan={5} className="text-center py-8">
                      <RefreshCw className="h-8 w-8 animate-spin mx-auto text-gray-400" />
                      <p className="mt-2 text-gray-500">Meghívók betöltése...</p>
                    </TableCell>
                  </TableRow>
                ) : (
                  invitations.map((invitation) => (
                    <TableRow key={invitation.id}>
                      <TableCell className="font-medium">
                        {invitation.email}
                        {invitation.invitee && (
                          <div className="text-xs text-gray-500 mt-1">
                            Elfogadta: {invitation.invitee.name}
                          </div>
                        )}
                      </TableCell>
                      <TableCell>
                        {getStatusBadge(invitation.status, invitation.expiresAt)}
                      </TableCell>
                      <TableCell className="text-sm">
                        {formatDate(invitation.createdAt)}
                      </TableCell>
                      <TableCell className="text-sm">
                        {formatDate(invitation.expiresAt)}
                      </TableCell>
                      <TableCell className="text-right">
                        <div className="flex justify-end space-x-2">
                          {invitation.status === 'pending' && new Date(invitation.expiresAt) > new Date() && (
                            <Button 
                              variant="outline" 
                              size="sm" 
                              onClick={() => handleResend(invitation.id)}
                              disabled={isResending === invitation.id}
                            >
                              {isResending === invitation.id ? (
                                <RefreshCw className="h-4 w-4 animate-spin" />
                              ) : (
                                <Send className="h-4 w-4" />
                              )}
                            </Button>
                          )}
                          <Button 
                            variant="outline" 
                            size="sm" 
                            onClick={() => handleDelete(invitation.id)}
                            disabled={isDeleting === invitation.id}
                          >
                            {isDeleting === invitation.id ? (
                              <RefreshCw className="h-4 w-4 animate-spin" />
                            ) : (
                              <Trash className="h-4 w-4" />
                            )}
                          </Button>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))
                )}
              </TableBody>
            </Table>
          </div>
        )}
      </CardContent>
    </Card>
  );
};

export default InvitationsList;
