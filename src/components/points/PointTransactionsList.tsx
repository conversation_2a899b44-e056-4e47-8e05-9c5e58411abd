import React, { useState, useEffect } from 'react';
import { getUserPoints, PointTransaction } from '@/services/pointService';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { RefreshCw, Coins, ArrowUpCircle, ArrowDownCircle } from 'lucide-react';
import { toast } from 'sonner';
import { useAuth } from '@/contexts/AuthContext';
import { formatDistanceToNow } from 'date-fns';
import { hu } from 'date-fns/locale';

const PointTransactionsList: React.FC = () => {
  const { user, checkUserPoints } = useAuth();
  const [points, setPoints] = useState<number>(0);
  const [transactions, setTransactions] = useState<PointTransaction[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const loadTransactions = async () => {
    try {
      setLoading(true);
      setError(null);

      // Pontok és tranzakciók betöltése
      const data = await getUserPoints();
      setPoints(data.points);
      setTransactions(data.transactions);

      // Pontok frissítése a globális állapotban
      if (user) {
        await checkUserPoints();
      }
    } catch (err) {
      console.error('Hiba a ponttranzakciók betöltésekor:', err);
      setError('Nem sikerült betölteni a ponttranzakciókat. Kérjük, próbáld újra később!');
      toast.error('Nem sikerült betölteni a ponttranzakciókat');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (user) {
      loadTransactions();
    }
  }, [user]);

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('hu-HU', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const formatTransactionType = (type: string) => {
    switch (type) {
      case 'purchase':
        return 'Vásárlás';
      case 'usage':
        return 'Felhasználás';
      case 'subscription':
        return 'Előfizetés';
      default:
        return type;
    }
  };

  const getTransactionIcon = (type: string, amount: number) => {
    if (amount > 0) {
      return <ArrowUpCircle className="h-4 w-4 text-green-500" />;
    } else {
      return <ArrowDownCircle className="h-4 w-4 text-red-500" />;
    }
  };

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h2 className="text-2xl font-bold">Ponttranzakciók</h2>
        <Button
          variant="outline"
          size="sm"
          onClick={loadTransactions}
          disabled={loading}
        >
          <RefreshCw className={`h-4 w-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
          Frissítés
        </Button>
      </div>

      {error && (
        <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-md">
          {error}
        </div>
      )}

      {loading && transactions.length === 0 ? (
        <div className="text-center py-8">
          <RefreshCw className="h-8 w-8 animate-spin mx-auto text-gray-400" />
          <p className="mt-2 text-gray-500">Adatok betöltése...</p>
        </div>
      ) : (
        <>
          <Card>
            <CardHeader>
              <CardTitle>Pontegyenleg</CardTitle>
              <CardDescription>Jelenlegi pontjaid és felhasználási lehetőségek</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="bg-primary/10 p-6 rounded-lg text-center">
                <h3 className="text-lg font-medium mb-2">Elérhető pontok</h3>
                <p className="text-4xl font-bold text-primary">{points}</p>
                <p className="mt-2 text-sm text-gray-500">Minden API hívás 1 pontba kerül</p>
              </div>
              <div className="mt-4 text-center">
                <Button onClick={() => window.location.href = '/points'} className="mt-2">
                  <Coins className="h-4 w-4 mr-2" />
                  Pontok kezelése
                </Button>
              </div>
            </CardContent>
          </Card>

          {transactions.length > 0 ? (
            <Card>
              <CardHeader>
                <CardTitle>Ponttranzakciók</CardTitle>
                <CardDescription>Az összes ponttranzakciód</CardDescription>
              </CardHeader>
              <CardContent>
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Típus</TableHead>
                      <TableHead>Leírás</TableHead>
                      <TableHead>Dátum</TableHead>
                      <TableHead className="text-right">Pontok</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {transactions.map((transaction) => (
                      <TableRow key={transaction.id}>
                        <TableCell>
                          <div className="flex items-center">
                            {getTransactionIcon(transaction.type, transaction.amount)}
                            <span className="ml-2">{formatTransactionType(transaction.type)}</span>
                          </div>
                        </TableCell>
                        <TableCell>{transaction.description || formatTransactionType(transaction.type)}</TableCell>
                        <TableCell>
                          <div className="flex flex-col">
                            <span>{formatDate(transaction.createdAt)}</span>
                            <span className="text-xs text-gray-500">
                              {formatDistanceToNow(new Date(transaction.createdAt), { addSuffix: true, locale: hu })}
                            </span>
                          </div>
                        </TableCell>
                        <TableCell className="text-right">
                          <span className={transaction.amount > 0 ? 'text-green-600 font-medium' : 'text-red-600 font-medium'}>
                            {transaction.amount > 0 ? '+' : ''}{transaction.amount}
                          </span>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </CardContent>
            </Card>
          ) : (
            <Card>
              <CardContent className="text-center py-8">
                <Coins className="h-12 w-12 mx-auto text-gray-400 mb-4" />
                <h3 className="text-lg font-medium text-gray-900">Nincs még ponttranzakció</h3>
                <p className="mt-1 text-sm text-gray-500">
                  A ponttranzakcióid itt fognak megjelenni, amikor használod az API-t vagy pontokat vásárolsz.
                </p>
              </CardContent>
            </Card>
          )}
        </>
      )}
    </div>
  );
};

export default PointTransactionsList;
