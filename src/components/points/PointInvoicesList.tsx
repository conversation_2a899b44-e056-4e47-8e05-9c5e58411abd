import React, { useState, useEffect } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { RefreshCw, Download, ExternalLink, FileText, CreditCard } from 'lucide-react';
import { toast } from 'sonner';
import { useAuth } from '@/contexts/AuthContext';
import api from '@/services/apiService';
import { formatCurrency } from '@/lib/utils';

interface PointPurchase {
  id: string;
  userId: string;
  amount: number;
  type: string;
  description: string;
  stripePaymentIntentId: string;
  createdAt: string;
  updatedAt: string;
}

interface Payment {
  id: string;
  amount: number;
  currency: string;
  status: string;
  description: string;
  created: string;
  receiptUrl: string | null;
  paymentIntent: string | null;
  isPointPurchase: boolean;
  pointAmount: number | null;
}

interface Invoice {
  id: string;
  number: string;
  amount: number;
  currency: string;
  status: string;
  created: string;
  periodStart: string | null;
  periodEnd: string | null;
  pdfUrl: string | null;
  hostedInvoiceUrl: string | null;
  description: string;
}

interface PointInvoicesResponse {
  invoices: Invoice[];
  payments: Payment[];
  pointPurchases: PointPurchase[];
}

const PointInvoicesList: React.FC = () => {
  const { user } = useAuth();
  const [data, setData] = useState<PointInvoicesResponse | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const loadInvoices = async () => {
    try {
      setLoading(true);
      setError(null);

      // Számlák betöltése
      const response = await api.get('/points/invoices');
      setData(response.data.data);
    } catch (err) {
      console.error('Hiba a pontfeltöltési számlák betöltésekor:', err);
      setError('Nem sikerült betölteni a pontfeltöltési számlákat. Kérjük, próbáld újra később!');
      toast.error('Nem sikerült betölteni a pontfeltöltési számlákat');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (user) {
      loadInvoices();
    }
  }, [user]);

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('hu-HU', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  const getStatusBadgeClass = (status: string) => {
    switch (status) {
      case 'paid':
      case 'succeeded':
        return 'bg-green-100 text-green-800';
      case 'open':
      case 'processing':
        return 'bg-blue-100 text-blue-800';
      case 'uncollectible':
      case 'failed':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'paid':
        return 'Fizetve';
      case 'open':
        return 'Nyitott';
      case 'uncollectible':
        return 'Behajthatatlan';
      case 'void':
        return 'Érvénytelen';
      case 'draft':
        return 'Piszkozat';
      case 'succeeded':
        return 'Sikeres';
      case 'processing':
        return 'Feldolgozás alatt';
      case 'failed':
        return 'Sikertelen';
      default:
        return status;
    }
  };

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h2 className="text-2xl font-bold">Pontfeltöltési számlák</h2>
        <Button
          variant="outline"
          size="sm"
          onClick={loadInvoices}
          disabled={loading}
        >
          <RefreshCw className={`h-4 w-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
          Frissítés
        </Button>
      </div>

      {error && (
        <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-md">
          {error}
        </div>
      )}

      {loading && !data ? (
        <div className="text-center py-8">
          <RefreshCw className="h-8 w-8 animate-spin mx-auto text-gray-400" />
          <p className="mt-2 text-gray-500">Adatok betöltése...</p>
        </div>
      ) : (
        <>
          {data?.payments && data.payments.length > 0 ? (
            <Card>
              <CardHeader>
                <CardTitle>Pontfeltöltési fizetések</CardTitle>
                <CardDescription>A pontfeltöltésekhez kapcsolódó fizetések</CardDescription>
              </CardHeader>
              <CardContent>
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Leírás</TableHead>
                      <TableHead>Dátum</TableHead>
                      <TableHead>Összeg</TableHead>
                      <TableHead>Állapot</TableHead>
                      <TableHead className="text-right">Műveletek</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {data.payments.map((payment) => (
                      <TableRow key={payment.id}>
                        <TableCell className="font-medium">
                          <div className="flex items-center">
                            <CreditCard className="h-4 w-4 mr-2 text-gray-400" />
                            {payment.description || 'Pontfeltöltés'}
                          </div>
                        </TableCell>
                        <TableCell>{formatDate(payment.created)}</TableCell>
                        <TableCell>{formatCurrency(payment.amount / 100, payment.currency)}</TableCell>
                        <TableCell>
                          <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusBadgeClass(payment.status)}`}>
                            {getStatusText(payment.status)}
                          </span>
                        </TableCell>
                        <TableCell className="text-right">
                          <div className="flex justify-end space-x-2">
                            {payment.receiptUrl && (
                              <Button
                                variant="ghost"
                                size="sm"
                                onClick={() => window.open(payment.receiptUrl, '_blank')}
                              >
                                <ExternalLink className="h-4 w-4" />
                              </Button>
                            )}
                          </div>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </CardContent>
            </Card>
          ) : (
            <Card>
              <CardContent className="text-center py-8">
                <FileText className="h-12 w-12 mx-auto text-gray-400 mb-4" />
                <h3 className="text-lg font-medium text-gray-900">Nincs még pontfeltöltési számla</h3>
                <p className="mt-1 text-sm text-gray-500">
                  A pontfeltöltésekhez kapcsolódó számlák itt fognak megjelenni.
                </p>
                <div className="mt-4">
                  <Button onClick={() => window.location.href = '/points'} variant="outline">
                    Pontok feltöltése
                  </Button>
                </div>
              </CardContent>
            </Card>
          )}
        </>
      )}
    </div>
  );
};

export default PointInvoicesList;
