import React from 'react';
import { Helmet } from 'react-helmet-async';

interface StructuredDataProps {
  type?: 'website' | 'course' | 'educational' | 'faq' | 'article';
  title?: string;
  description?: string;
  url?: string;
  image?: string;
  author?: string;
  datePublished?: string;
  dateModified?: string;
  courseData?: {
    name: string;
    description: string;
    provider: string;
    educationalLevel: string[];
    inLanguage: string[];
    learningResourceType: string;
    timeRequired?: string;
    skillLevel?: string;
  };
  faqData?: Array<{
    question: string;
    answer: string;
  }>;
}

const StructuredData: React.FC<StructuredDataProps> = ({
  type = 'website',
  title = "Magyar-Német Nyelvtanuló",
  description = "Interaktív német nyelvtanuló alkalmazás",
  url = "https://digitalisnemet.hu",
  image = "https://digitalisnemet.hu/og-image.jpg",
  author = "Magyar-Német Nyelvtanuló",
  datePublished,
  dateModified,
  courseData,
  faqData
}) => {
  
  const getStructuredData = () => {
    const baseData = {
      "@context": "https://schema.org",
      "@type": getSchemaType(),
      "name": title,
      "description": description,
      "url": url,
      "image": {
        "@type": "ImageObject",
        "url": image
      }
    };

    switch (type) {
      case 'website':
        return {
          ...baseData,
          "@type": "WebSite",
          "potentialAction": {
            "@type": "SearchAction",
            "target": {
              "@type": "EntryPoint",
              "urlTemplate": "https://digitalisnemet.hu/search?q={search_term_string}"
            },
            "query-input": "required name=search_term_string"
          },
          "sameAs": [
            "https://digitalisnemet.hu"
          ]
        };

      case 'course':
      case 'educational':
        return {
          ...baseData,
          "@type": "Course",
          "provider": {
            "@type": "Organization",
            "name": courseData?.provider || "Magyar-Német Nyelvtanuló",
            "url": "https://digitalisnemet.hu"
          },
          "educationalLevel": courseData?.educationalLevel || ["Beginner", "Intermediate"],
          "inLanguage": courseData?.inLanguage || ["hu", "de"],
          "learningResourceType": courseData?.learningResourceType || "Interactive Course",
          "timeRequired": courseData?.timeRequired || "P30D",
          "skillLevel": courseData?.skillLevel || "Beginner",
          "courseCode": "DE-HU-001",
          "hasCourseInstance": {
            "@type": "CourseInstance",
            "courseMode": "online",
            "courseWorkload": "PT2H",
            "instructor": {
              "@type": "Person",
              "name": "AI Nyelvtanár"
            }
          },
          "audience": {
            "@type": "EducationalAudience",
            "educationalRole": "student"
          }
        };

      case 'faq':
        return {
          ...baseData,
          "@type": "FAQPage",
          "mainEntity": faqData?.map(item => ({
            "@type": "Question",
            "name": item.question,
            "acceptedAnswer": {
              "@type": "Answer",
              "text": item.answer
            }
          })) || []
        };

      case 'article':
        return {
          ...baseData,
          "@type": "Article",
          "headline": title,
          "author": {
            "@type": "Person",
            "name": author
          },
          "publisher": {
            "@type": "Organization",
            "name": "Magyar-Német Nyelvtanuló",
            "logo": {
              "@type": "ImageObject",
              "url": "https://digitalisnemet.hu/logo.png"
            }
          },
          "datePublished": datePublished,
          "dateModified": dateModified || datePublished,
          "mainEntityOfPage": {
            "@type": "WebPage",
            "@id": url
          }
        };

      default:
        return baseData;
    }
  };

  const getSchemaType = () => {
    switch (type) {
      case 'website': return 'WebSite';
      case 'course':
      case 'educational': return 'Course';
      case 'faq': return 'FAQPage';
      case 'article': return 'Article';
      default: return 'WebPage';
    }
  };

  const structuredData = getStructuredData();

  // Szervezet structured data
  const organizationData = {
    "@context": "https://schema.org",
    "@type": "EducationalOrganization",
    "name": "Magyar-Német Nyelvtanuló",
    "description": "Interaktív német nyelvtanuló platform magyar anyanyelvűek számára",
    "url": "https://digitalisnemet.hu",
    "logo": {
      "@type": "ImageObject",
      "url": "https://digitalisnemet.hu/logo.png"
    },
    "sameAs": [
      "https://digitalisnemet.hu"
    ],
    "address": {
      "@type": "PostalAddress",
      "addressCountry": "HU",
      "addressRegion": "Hungary"
    },
    "contactPoint": {
      "@type": "ContactPoint",
      "contactType": "customer service",
      "email": "<EMAIL>",
      "availableLanguage": ["Hungarian", "German"]
    },
    "hasEducationalUse": "LanguageLearning",
    "educationalCredentialAwarded": "Certificate of Completion",
    "audience": {
      "@type": "EducationalAudience",
      "educationalRole": "student",
      "audienceType": "Hungarian speakers learning German"
    }
  };

  // Breadcrumb structured data
  const breadcrumbData = {
    "@context": "https://schema.org",
    "@type": "BreadcrumbList",
    "itemListElement": [
      {
        "@type": "ListItem",
        "position": 1,
        "name": "Főoldal",
        "item": "https://digitalisnemet.hu/"
      }
    ]
  };

  return (
    <Helmet>
      <script type="application/ld+json">
        {JSON.stringify(structuredData)}
      </script>
      <script type="application/ld+json">
        {JSON.stringify(organizationData)}
      </script>
      <script type="application/ld+json">
        {JSON.stringify(breadcrumbData)}
      </script>
    </Helmet>
  );
};

export default StructuredData;
