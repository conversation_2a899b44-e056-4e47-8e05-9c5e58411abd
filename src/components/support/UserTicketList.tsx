import React from 'react';
import { SupportTicket } from '@/services/supportService';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { formatDistanceToNow } from 'date-fns';
import { hu } from 'date-fns/locale';
import { Loader2, MessageSquare, Clock, CheckCircle, AlertCircle } from 'lucide-react';

interface UserTicketListProps {
  tickets: SupportTicket[];
  isLoading: boolean;
  onTicketClick: (ticketId: string) => void;
}

const UserTicketList: React.FC<UserTicketListProps> = ({ tickets, isLoading, onTicketClick }) => {
  // Státusz badge színének meghatározása
  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'open':
        return (
          <Badge variant="outline" className="bg-blue-50 text-blue-700 border-blue-200 flex items-center gap-1">
            <Clock className="h-3 w-3" />
            <span>Nyitott</span>
          </Badge>
        );
      case 'in_progress':
        return (
          <Badge variant="outline" className="bg-yellow-50 text-yellow-700 border-yellow-200 flex items-center gap-1">
            <AlertCircle className="h-3 w-3" />
            <span>Folyamatban</span>
          </Badge>
        );
      case 'closed':
        return (
          <Badge variant="outline" className="bg-green-50 text-green-700 border-green-200 flex items-center gap-1">
            <CheckCircle className="h-3 w-3" />
            <span>Lezárva</span>
          </Badge>
        );
      default:
        return <Badge>{status}</Badge>;
    }
  };

  // Prioritás badge színének meghatározása
  const getPriorityBadge = (priority: string) => {
    switch (priority) {
      case 'low':
        return <Badge variant="outline" className="bg-gray-50 text-gray-700 border-gray-200">Alacsony</Badge>;
      case 'medium':
        return <Badge variant="outline" className="bg-blue-50 text-blue-700 border-blue-200">Közepes</Badge>;
      case 'high':
        return <Badge variant="outline" className="bg-red-50 text-red-700 border-red-200">Magas</Badge>;
      default:
        return <Badge variant="outline">{priority}</Badge>;
    }
  };

  if (isLoading) {
    return (
      <div className="flex justify-center py-8">
        <Loader2 className="h-8 w-8 animate-spin text-primary" />
      </div>
    );
  }

  if (tickets.length === 0) {
    return (
      <div className="text-center py-8">
        <MessageSquare className="h-12 w-12 mx-auto text-gray-400 mb-4" />
        <h3 className="text-lg font-medium text-gray-900">Nincsenek még ticketjeid</h3>
        <p className="mt-1 text-sm text-gray-500">
          Hozz létre egy új ticketet, ha segítségre van szükséged.
        </p>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      {tickets.map((ticket) => (
        <div
          key={ticket.id}
          className="border rounded-lg p-4 hover:bg-gray-50 transition-colors cursor-pointer"
          onClick={() => onTicketClick(ticket.id)}
        >
          <div className="flex flex-col md:flex-row md:items-center justify-between gap-2 mb-2">
            <h3 className="font-medium text-lg">{ticket.subject}</h3>
            <div className="flex flex-wrap gap-2">
              {getStatusBadge(ticket.status)}
              {getPriorityBadge(ticket.priority)}
            </div>
          </div>
          <div className="flex justify-between items-center text-sm text-gray-500">
            <div className="flex items-center gap-1">
              <MessageSquare className="h-4 w-4" />
              <span>{ticket._count?.messages || 0} üzenet</span>
            </div>
            <span>
              {formatDistanceToNow(new Date(ticket.updatedAt), { addSuffix: true, locale: hu })}
            </span>
          </div>
        </div>
      ))}
    </div>
  );
};

export default UserTicketList;
