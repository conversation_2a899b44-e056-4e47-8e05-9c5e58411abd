import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Label } from '@/components/ui/label';
import { Loader2 } from 'lucide-react';
import { toast } from 'sonner';
import { createTicket } from '@/services/supportService';

interface NewTicketFormProps {
  onTicketCreated: () => void;
}

const NewTicketForm: React.FC<NewTicketFormProps> = ({ onTicketCreated }) => {
  const [subject, setSubject] = useState('');
  const [message, setMessage] = useState('');
  const [priority, setPriority] = useState('medium');
  const [isSubmitting, setIsSubmitting] = useState(false);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!subject.trim()) {
      toast.error('<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, add meg a ticket tárgyát!');
      return;
    }
    
    if (!message.trim()) {
      toast.error('Kérjük, írd le a problémádat!');
      return;
    }
    
    try {
      setIsSubmitting(true);
      await createTicket(subject, message, priority);
      
      // Form reset
      setSubject('');
      setMessage('');
      setPriority('medium');
      
      // Callback
      onTicketCreated();
    } catch (error) {
      console.error('Hiba a ticket létrehozásakor:', error);
      toast.error('Nem sikerült létrehozni a ticketet. Kérjük, próbáld újra később!');
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      <div className="space-y-2">
        <Label htmlFor="subject">Tárgy</Label>
        <Input
          id="subject"
          placeholder="Pl. Problémám van a fizetéssel"
          value={subject}
          onChange={(e) => setSubject(e.target.value)}
          disabled={isSubmitting}
          required
        />
      </div>
      
      <div className="space-y-2">
        <Label htmlFor="priority">Prioritás</Label>
        <Select
          value={priority}
          onValueChange={setPriority}
          disabled={isSubmitting}
        >
          <SelectTrigger>
            <SelectValue placeholder="Válassz prioritást" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="low">Alacsony</SelectItem>
            <SelectItem value="medium">Közepes</SelectItem>
            <SelectItem value="high">Magas</SelectItem>
          </SelectContent>
        </Select>
      </div>
      
      <div className="space-y-2">
        <Label htmlFor="message">Üzenet</Label>
        <Textarea
          id="message"
          placeholder="Írd le részletesen a problémádat..."
          value={message}
          onChange={(e) => setMessage(e.target.value)}
          disabled={isSubmitting}
          required
          rows={6}
        />
      </div>
      
      <Button type="submit" className="w-full" disabled={isSubmitting}>
        {isSubmitting ? (
          <>
            <Loader2 className="mr-2 h-4 w-4 animate-spin" />
            Küldés...
          </>
        ) : (
          'Ticket létrehozása'
        )}
      </Button>
    </form>
  );
};

export default NewTicketForm;
