import React, { useState } from 'react';
import { SupportTicket } from '@/services/supportService';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { formatDistanceToNow } from 'date-fns';
import { hu } from 'date-fns/locale';
import { Loader2, MessageSquare, Clock, CheckCircle, AlertCircle, Search, Filter } from 'lucide-react';

interface AdminTicketListProps {
  tickets: SupportTicket[];
  isLoading: boolean;
  onTicketClick: (ticketId: string) => void;
}

const AdminTicketList: React.FC<AdminTicketListProps> = ({ tickets, isLoading, onTicketClick }) => {
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState<string>('all');
  const [priorityFilter, setPriorityFilter] = useState<string>('all');

  // Státusz badge színének meghatározása
  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'open':
        return (
          <Badge variant="outline" className="bg-blue-50 text-blue-700 border-blue-200 flex items-center gap-1">
            <Clock className="h-3 w-3" />
            <span>Nyitott</span>
          </Badge>
        );
      case 'in_progress':
        return (
          <Badge variant="outline" className="bg-yellow-50 text-yellow-700 border-yellow-200 flex items-center gap-1">
            <AlertCircle className="h-3 w-3" />
            <span>Folyamatban</span>
          </Badge>
        );
      case 'closed':
        return (
          <Badge variant="outline" className="bg-green-50 text-green-700 border-green-200 flex items-center gap-1">
            <CheckCircle className="h-3 w-3" />
            <span>Lezárva</span>
          </Badge>
        );
      default:
        return <Badge>{status}</Badge>;
    }
  };

  // Prioritás badge színének meghatározása
  const getPriorityBadge = (priority: string) => {
    switch (priority) {
      case 'low':
        return <Badge variant="outline" className="bg-gray-50 text-gray-700 border-gray-200">Alacsony</Badge>;
      case 'medium':
        return <Badge variant="outline" className="bg-blue-50 text-blue-700 border-blue-200">Közepes</Badge>;
      case 'high':
        return <Badge variant="outline" className="bg-red-50 text-red-700 border-red-200">Magas</Badge>;
      default:
        return <Badge variant="outline">{priority}</Badge>;
    }
  };

  // Szűrés és keresés
  const filteredTickets = tickets.filter(ticket => {
    const matchesSearch = 
      ticket.subject.toLowerCase().includes(searchTerm.toLowerCase()) ||
      (ticket.creator?.name && ticket.creator.name.toLowerCase().includes(searchTerm.toLowerCase())) ||
      (ticket.creator?.email && ticket.creator.email.toLowerCase().includes(searchTerm.toLowerCase()));
    
    const matchesStatus = statusFilter === 'all' || ticket.status === statusFilter;
    const matchesPriority = priorityFilter === 'all' || ticket.priority === priorityFilter;
    
    return matchesSearch && matchesStatus && matchesPriority;
  });

  if (isLoading) {
    return (
      <div className="flex justify-center py-8">
        <Loader2 className="h-8 w-8 animate-spin text-primary" />
      </div>
    );
  }

  return (
    <div className="space-y-4">
      <div className="flex flex-col md:flex-row gap-4 mb-4">
        <div className="relative flex-1">
          <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="Keresés tárgy, név vagy email alapján..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="pl-8"
          />
        </div>
        <div className="flex gap-2">
          <Select value={statusFilter} onValueChange={setStatusFilter}>
            <SelectTrigger className="w-[140px]">
              <SelectValue placeholder="Státusz" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">Összes státusz</SelectItem>
              <SelectItem value="open">Nyitott</SelectItem>
              <SelectItem value="in_progress">Folyamatban</SelectItem>
              <SelectItem value="closed">Lezárva</SelectItem>
            </SelectContent>
          </Select>
          <Select value={priorityFilter} onValueChange={setPriorityFilter}>
            <SelectTrigger className="w-[140px]">
              <SelectValue placeholder="Prioritás" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">Összes prioritás</SelectItem>
              <SelectItem value="low">Alacsony</SelectItem>
              <SelectItem value="medium">Közepes</SelectItem>
              <SelectItem value="high">Magas</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>

      {filteredTickets.length === 0 ? (
        <div className="text-center py-8">
          <MessageSquare className="h-12 w-12 mx-auto text-gray-400 mb-4" />
          <h3 className="text-lg font-medium text-gray-900">Nincsenek a feltételeknek megfelelő ticketek</h3>
          <p className="mt-1 text-sm text-gray-500">
            Próbáld meg módosítani a keresési feltételeket.
          </p>
        </div>
      ) : (
        <div className="space-y-4">
          {filteredTickets.map((ticket) => (
            <div
              key={ticket.id}
              className="border rounded-lg p-4 hover:bg-gray-50 transition-colors cursor-pointer"
              onClick={() => onTicketClick(ticket.id)}
            >
              <div className="flex flex-col md:flex-row md:items-center justify-between gap-2 mb-2">
                <h3 className="font-medium text-lg">{ticket.subject}</h3>
                <div className="flex flex-wrap gap-2">
                  {getStatusBadge(ticket.status)}
                  {getPriorityBadge(ticket.priority)}
                </div>
              </div>
              <div className="flex flex-col md:flex-row justify-between gap-2">
                <div className="text-sm">
                  <span className="text-gray-500">Felhasználó: </span>
                  <span className="font-medium">{ticket.creator?.name}</span>
                  <span className="text-gray-500 ml-2">({ticket.creator?.email})</span>
                </div>
                <div className="flex items-center gap-4 text-sm text-gray-500">
                  <div className="flex items-center gap-1">
                    <MessageSquare className="h-4 w-4" />
                    <span>{ticket._count?.messages || 0} üzenet</span>
                  </div>
                  <span>
                    {formatDistanceToNow(new Date(ticket.updatedAt), { addSuffix: true, locale: hu })}
                  </span>
                </div>
              </div>
            </div>
          ))}
        </div>
      )}
    </div>
  );
};

export default AdminTicketList;
