import React, { useState, useEffect } from 'react';
import { subscriptionService } from '@/services/apiService';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { FileText, Download, ExternalLink, RefreshCw } from 'lucide-react';
import { toast } from 'sonner';
import { formatCurrency } from '@/lib/utils';
import { useAuth } from '@/contexts/AuthContext';

interface Invoice {
  id: string;
  number: string;
  amount: number;
  currency: string;
  status: string;
  created: string;
  periodStart: string | null;
  periodEnd: string | null;
  pdfUrl: string;
  hostedInvoiceUrl: string;
}

interface Payment {
  id: string;
  amount: number;
  currency: string;
  status: string;
  description: string;
  created: string;
}

interface SubscriptionDetails {
  id: string;
  status: string;
  currentPeriodEnd: string;
  cancelAtPeriodEnd: boolean;
  stripeDetails: {
    id: string;
    status: string;
    currentPeriodStart: string;
    currentPeriodEnd: string;
    cancelAtPeriodEnd: boolean;
    canceledAt: string | null;
    startDate: string;
    trialEnd: string | null;
    paymentMethod: {
      brand: string;
      last4: string;
      expMonth: number;
      expYear: number;
    } | null;
    items: {
      id: string;
      priceId: string;
      productId: string;
      productName: string;
      amount: number;
      currency: string;
      interval: string | null;
      intervalCount: number | null;
    }[];
  } | null;
}

interface InvoicesResponse {
  invoices: Invoice[];
  payments: Payment[];
  subscription: SubscriptionDetails | null;
}

const InvoicesList: React.FC = () => {
  const { user, checkSubscription } = useAuth();
  const [data, setData] = useState<InvoicesResponse | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const loadInvoices = async () => {
    try {
      setLoading(true);
      setError(null);

      // Számlák betöltése
      const response = await subscriptionService.getInvoices();
      setData(response.data);

      // Előfizetés állapotának frissítése - kikommentálva, hogy ne hívja meg túl gyakran
      // if (user) {
      //   console.log('Számlák oldal: Előfizetés frissítése a következő felhasználónak:', user.email);
      //   await checkSubscription(user);
      // }
    } catch (err) {
      console.error('Hiba a számlák betöltésekor:', err);
      setError('Nem sikerült betölteni a számlákat. Kérjük, próbáld újra később!');
      toast.error('Nem sikerült betölteni a számlákat');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    // Csak egyszer töltjük be a számlákat, amikor a komponens betöltődik
    // és a felhasználó be van jelentkezve
    let isMounted = true;

    if (user && isMounted) {
      loadInvoices();
    }

    // Cleanup függvény, amely jelzi, ha a komponens eltávolításra kerül
    return () => {
      isMounted = false;
    };
  }, [user]); // checkSubscription eltávolítva a függőségekből

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('hu-HU', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  const getStatusBadgeClass = (status: string) => {
    switch (status) {
      case 'paid':
      case 'succeeded':
        return 'bg-green-100 text-green-800';
      case 'open':
      case 'processing':
        return 'bg-blue-100 text-blue-800';
      case 'uncollectible':
      case 'failed':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'paid':
        return 'Fizetve';
      case 'open':
        return 'Nyitott';
      case 'uncollectible':
        return 'Behajthatatlan';
      case 'void':
        return 'Érvénytelen';
      case 'draft':
        return 'Piszkozat';
      case 'succeeded':
        return 'Sikeres';
      case 'processing':
        return 'Feldolgozás alatt';
      case 'failed':
        return 'Sikertelen';
      default:
        return status;
    }
  };

  const getCardBrandIcon = (brand: string) => {
    switch (brand) {
      case 'visa':
        return '💳 Visa';
      case 'mastercard':
        return '💳 Mastercard';
      case 'amex':
        return '💳 American Express';
      default:
        return `💳 ${brand}`;
    }
  };

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h2 className="text-2xl font-bold">Előfizetési adatok</h2>
        <Button
          variant="outline"
          size="sm"
          onClick={loadInvoices}
          disabled={loading}
        >
          <RefreshCw className={`h-4 w-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
          Frissítés
        </Button>
      </div>

      {error && (
        <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-md">
          {error}
        </div>
      )}

      {loading && !data ? (
        <div className="text-center py-8">
          <RefreshCw className="h-8 w-8 animate-spin mx-auto text-gray-400" />
          <p className="mt-2 text-gray-500">Adatok betöltése...</p>
        </div>
      ) : (
        <>
          {data?.subscription && (
            <Card>
              <CardHeader>
                <CardTitle>Előfizetési részletek</CardTitle>
                <CardDescription>Az előfizetésed aktuális állapota és részletei</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <h3 className="text-sm font-medium text-gray-500">Állapot</h3>
                      <p className="mt-1">
                        <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                          data.subscription.status === 'active' ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
                        }`}>
                          {data.subscription.status === 'active' ? 'Aktív' : 'Inaktív'}
                        </span>
                        {data.subscription.cancelAtPeriodEnd && (
                          <span className="ml-2 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                            Lemondva
                          </span>
                        )}
                      </p>
                    </div>
                    <div>
                      <h3 className="text-sm font-medium text-gray-500">Következő fizetés</h3>
                      <p className="mt-1">{formatDate(data.subscription.currentPeriodEnd)}</p>
                    </div>

                    {data.subscription.stripeDetails && (
                      <>
                        <div>
                          <h3 className="text-sm font-medium text-gray-500">Előfizetés kezdete</h3>
                          <p className="mt-1">{formatDate(data.subscription.stripeDetails.startDate)}</p>
                        </div>
                        <div>
                          <h3 className="text-sm font-medium text-gray-500">Jelenlegi időszak</h3>
                          <p className="mt-1">
                            {formatDate(data.subscription.stripeDetails.currentPeriodStart)} - {formatDate(data.subscription.stripeDetails.currentPeriodEnd)}
                          </p>
                        </div>

                        {data.subscription.stripeDetails.paymentMethod && (
                          <div>
                            <h3 className="text-sm font-medium text-gray-500">Fizetési mód</h3>
                            <p className="mt-1">
                              {getCardBrandIcon(data.subscription.stripeDetails.paymentMethod.brand)} **** {data.subscription.stripeDetails.paymentMethod.last4}
                              <span className="text-sm text-gray-500 ml-2">
                                (Lejárat: {data.subscription.stripeDetails.paymentMethod.expMonth}/{data.subscription.stripeDetails.paymentMethod.expYear})
                              </span>
                            </p>
                          </div>
                        )}

                        {data.subscription.stripeDetails.items.map(item => (
                          <div key={item.id} className="col-span-1 md:col-span-2">
                            <h3 className="text-sm font-medium text-gray-500">Előfizetett csomag</h3>
                            <div className="mt-1 bg-gray-50 p-3 rounded-md">
                              <div className="flex justify-between">
                                <span className="font-medium">{item.productName}</span>
                                <span>{formatCurrency(item.amount / 100, item.currency)}</span>
                              </div>
                              <div className="text-sm text-gray-500 mt-1">
                                {item.interval && `${item.intervalCount} ${item.interval === 'month' ? 'hónap' : item.interval}`}
                              </div>
                            </div>
                          </div>
                        ))}
                      </>
                    )}
                  </div>
                </div>
              </CardContent>
              <CardFooter className="flex justify-between">
                {data.subscription.cancelAtPeriodEnd ? (
                  <Button
                    variant="outline"
                    onClick={async () => {
                      try {
                        await subscriptionService.reactivateSubscription();
                        toast.success('Előfizetésed sikeresen újraaktiválva!');
                        // Előfizetés állapotának frissítése
                        if (user) {
                          await checkSubscription(user);
                        }
                        loadInvoices();
                      } catch (err) {
                        toast.error('Nem sikerült újraaktiválni az előfizetést');
                      }
                    }}
                  >
                    Előfizetés újraaktiválása
                  </Button>
                ) : (
                  <Button
                    variant="outline"
                    onClick={async () => {
                      try {
                        await subscriptionService.cancelSubscription();
                        toast.success('Előfizetésed sikeresen lemondva!');
                        // Előfizetés állapotának frissítése
                        if (user) {
                          await checkSubscription(user);
                        }
                        loadInvoices();
                      } catch (err) {
                        toast.error('Nem sikerült lemondani az előfizetést');
                      }
                    }}
                  >
                    Előfizetés lemondása
                  </Button>
                )}
                <Button
                  variant="ghost"
                  onClick={() => {
                    loadInvoices();
                  }}
                  disabled={loading}
                >
                  <RefreshCw className={`h-4 w-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
                  Frissítés
                </Button>
              </CardFooter>
            </Card>
          )}

          {data?.invoices && data.invoices.length > 0 && (
            <Card>
              <CardHeader>
                <CardTitle>Számlák</CardTitle>
                <CardDescription>Az előfizetésedhez kapcsolódó számlák</CardDescription>
              </CardHeader>
              <CardContent>
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Számla</TableHead>
                      <TableHead>Dátum</TableHead>
                      <TableHead>Összeg</TableHead>
                      <TableHead>Állapot</TableHead>
                      <TableHead className="text-right">Műveletek</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {data.invoices.map((invoice) => (
                      <TableRow key={invoice.id}>
                        <TableCell className="font-medium">
                          <div className="flex items-center">
                            <FileText className="h-4 w-4 mr-2 text-gray-400" />
                            {invoice.number || 'Számla'}
                          </div>
                        </TableCell>
                        <TableCell>{formatDate(invoice.created)}</TableCell>
                        <TableCell>{formatCurrency(invoice.amount / 100, invoice.currency)}</TableCell>
                        <TableCell>
                          <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusBadgeClass(invoice.status)}`}>
                            {getStatusText(invoice.status)}
                          </span>
                        </TableCell>
                        <TableCell className="text-right">
                          <div className="flex justify-end space-x-2">
                            {invoice.pdfUrl && (
                              <Button
                                variant="ghost"
                                size="sm"
                                onClick={() => window.open(invoice.pdfUrl, '_blank')}
                              >
                                <Download className="h-4 w-4" />
                              </Button>
                            )}
                            {invoice.hostedInvoiceUrl && (
                              <Button
                                variant="ghost"
                                size="sm"
                                onClick={() => window.open(invoice.hostedInvoiceUrl, '_blank')}
                              >
                                <ExternalLink className="h-4 w-4" />
                              </Button>
                            )}
                          </div>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </CardContent>
            </Card>
          )}

          {data?.payments && data.payments.length > 0 && (
            <Card>
              <CardHeader>
                <CardTitle>Fizetési előzmények</CardTitle>
                <CardDescription>Az előfizetésedhez kapcsolódó fizetések</CardDescription>
              </CardHeader>
              <CardContent>
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Leírás</TableHead>
                      <TableHead>Dátum</TableHead>
                      <TableHead>Összeg</TableHead>
                      <TableHead>Állapot</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {data.payments.map((payment) => (
                      <TableRow key={payment.id}>
                        <TableCell className="font-medium">
                          {payment.description || 'Fizetés'}
                        </TableCell>
                        <TableCell>{formatDate(payment.created)}</TableCell>
                        <TableCell>{formatCurrency(payment.amount / 100, payment.currency)}</TableCell>
                        <TableCell>
                          <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusBadgeClass(payment.status)}`}>
                            {getStatusText(payment.status)}
                          </span>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </CardContent>
            </Card>
          )}

          {(!data?.invoices || data.invoices.length === 0) && (!data?.payments || data.payments.length === 0) && (
            <Card>
              <CardContent className="text-center py-8">
                <FileText className="h-12 w-12 mx-auto text-gray-400 mb-4" />
                <h3 className="text-lg font-medium text-gray-900">Nincs még számla vagy fizetés</h3>
                <p className="mt-1 text-sm text-gray-500">
                  Az előfizetésedhez kapcsolódó számlák és fizetések itt fognak megjelenni.
                </p>
              </CardContent>
            </Card>
          )}
        </>
      )}
    </div>
  );
};

export default InvoicesList;
