import React, { useState, useEffect } from 'react';
import { CardElement, useStripe, useElements, Elements } from '@stripe/react-stripe-js';
import { stripePromise } from '@/services/stripeService';
import { Button } from '@/components/ui/button';
import { toast } from 'sonner';
import { Loader2 } from 'lucide-react';

interface StripePaymentFormProps {
  onSuccess: () => void;
  onCancel: () => void;
}

const StripePaymentForm: React.FC<StripePaymentFormProps> = ({ onSuccess, onCancel }) => {
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  
  const stripe = useStripe();
  const elements = useElements();

  const handleSubmit = async (event: React.FormEvent) => {
    event.preventDefault();

    if (!stripe || !elements) {
      // Stripe.js még nem töltődött be
      return;
    }

    setIsLoading(true);
    setError(null);

    try {
      // Kártyaadatok lekérése
      const cardElement = elements.getElement(CardElement);
      
      if (!cardElement) {
        throw new Error('Nem sikerült lekérni a kártyaadatokat');
      }

      // Valós alkalmazásban itt küldenénk el a kártyaadatokat a szervernek
      // Itt most csak szimulálunk egy sikeres fizetést
      
      // Várakozás a "fizetés" feldolgozására
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      // Sikeres fizetés
      onSuccess();
    } catch (error) {
      console.error('Fizetési hiba:', error);
      setError('Hiba történt a fizetés során. Kérjük, próbáld újra!');
      toast.error('Fizetési hiba: ' + (error instanceof Error ? error.message : 'Ismeretlen hiba'));
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-4">
      <div className="p-4 border rounded-md bg-white">
        <CardElement
          options={{
            style: {
              base: {
                fontSize: '16px',
                color: '#424770',
                '::placeholder': {
                  color: '#aab7c4',
                },
              },
              invalid: {
                color: '#9e2146',
              },
            },
          }}
        />
      </div>
      
      {error && (
        <div className="text-red-500 text-sm">{error}</div>
      )}
      
      <div className="flex gap-2">
        <Button
          type="button"
          variant="outline"
          onClick={onCancel}
          disabled={isLoading}
          className="flex-1"
        >
          Mégse
        </Button>
        
        <Button
          type="submit"
          disabled={!stripe || isLoading}
          className="flex-1"
        >
          {isLoading ? (
            <>
              <Loader2 className="h-4 w-4 mr-2 animate-spin" />
              Feldolgozás...
            </>
          ) : (
            'Fizetés'
          )}
        </Button>
      </div>
    </form>
  );
};

interface StripePaymentProps {
  onSuccess: () => void;
  onCancel: () => void;
}

const StripePayment: React.FC<StripePaymentProps> = ({ onSuccess, onCancel }) => {
  return (
    <Elements stripe={stripePromise}>
      <StripePaymentForm onSuccess={onSuccess} onCancel={onCancel} />
    </Elements>
  );
};

export default StripePayment;
