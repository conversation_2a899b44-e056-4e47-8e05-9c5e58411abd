import React from "react";
import { InteractiveHoverButtonDemo } from "@/components/ui/interactive-hover-button-demo";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";

const ButtonDemo = () => {
  return (
    <div className="container mx-auto px-4 py-8">
      <h1 className="text-3xl font-bold mb-8 text-center">UI Komponens Demó</h1>
      
      <Card className="mb-10">
        <CardHeader>
          <CardTitle>InteractiveHoverButton Komponens</CardTitle>
          <CardDescription>
            Interaktív hover effekttel rendelkező gombok, amely<PERSON> anim<PERSON> jelzik a felhasználói interakciót
          </CardDescription>
        </CardHeader>
        <CardContent>
          <InteractiveHoverButtonDemo />
        </CardContent>
      </Card>
      
      <Card className="mb-10">
        <CardHeader>
          <CardTitle>Integráció a Kezdőoldalon</CardTitle>
          <CardDescription>
            Az InteractiveHoverButton komponensek a kezdőoldalon is megtalálhatók, helyettesítve a korábbi gombokat
          </CardDescription>
        </CardHeader>
        <CardContent>
          <p className="mb-4">Az új gombok a következő helyeken lettek integrálva:</p>
          <ul className="list-disc pl-6 mb-6 space-y-2">
            <li>A kezdőoldal hero szekciójában lévő "Kezdj tanulni" és "Asszisztens" gombok</li>
            <li>A carousel szekcióban lévő "Kezdj tanulni most", "Beszélgess az asszisztenssel" és "Előfizetés" gombok</li>
            <li>A közösségi funkcióknál a "Meghívók kezelése" gomb</li>
          </ul>
          
          <div className="flex justify-center">
            <Button className="bg-language-primary hover:bg-language-primary/90 text-white" onClick={() => window.location.href = "/"}>
              Vissza a kezdőoldalra
            </Button>
          </div>
        </CardContent>
      </Card>
      
      <Card>
        <CardHeader>
          <CardTitle>Hogyan használd a komponenst</CardTitle>
          <CardDescription>
            Az InteractiveHoverButton komponens használatához szükséges kódrészlet
          </CardDescription>
        </CardHeader>
        <CardContent>
          <pre className="bg-gray-100 p-4 rounded-md overflow-x-auto">
{`import { InteractiveHoverButton } from "@/components/ui/interactive-hover-button";
import { Book } from "lucide-react";

// Alap használat
<InteractiveHoverButton 
  text="Gomb szövege" 
  icon={<Book className="h-4 w-4" />} 
  colorClass="bg-primary" 
  className="w-40 py-3" 
/>

// Nagyobb gomb egyedi színnel
<InteractiveHoverButton 
  text="Nagy gomb" 
  icon={<Book className="h-5 w-5" />} 
  colorClass="bg-gradient-to-r from-pink-500 to-orange-500" 
  className="w-64 py-6 font-medium hover:text-white" 
/>`}
          </pre>
        </CardContent>
      </Card>
    </div>
  );
};

export default ButtonDemo;
