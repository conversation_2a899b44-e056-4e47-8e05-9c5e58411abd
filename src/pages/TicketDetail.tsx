import React, { useState, useEffect, useRef } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { toast } from 'sonner';
import { Loader2, ArrowLeft, Send, MessageSquare, Clock, CheckCircle, AlertCircle, Sparkles } from 'lucide-react';
import { formatDistanceToNow, format } from 'date-fns';
import { hu } from 'date-fns/locale';
import { useAuth } from '@/contexts/AuthContext';
import { SupportTicket, SupportMessage, getTicketDetails, addMessageToTicket, updateTicketStatus, getAISuggestion } from '@/services/supportService';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Separator } from '@/components/ui/separator';

const TicketDetail = () => {
  const { id } = useParams<{ id: string }>();
  const [ticket, setTicket] = useState<SupportTicket | null>(null);
  const [newMessage, setNewMessage] = useState('');
  const [isLoading, setIsLoading] = useState(true);
  const [isSending, setIsSending] = useState(false);
  const [isUpdatingStatus, setIsUpdatingStatus] = useState(false);
  const [isGeneratingAI, setIsGeneratingAI] = useState(false);

  const messagesEndRef = useRef<HTMLDivElement>(null);
  const { user } = useAuth();
  const navigate = useNavigate();

  // Ticket részleteinek lekérdezése
  const fetchTicketDetails = async () => {
    if (!id) return;

    try {
      setIsLoading(true);
      const data = await getTicketDetails(id);
      setTicket(data);
    } catch (error) {
      console.error('Hiba a ticket részleteinek lekérdezésekor:', error);
      toast.error('Nem sikerült betölteni a ticket részleteit. Kérjük, próbáld újra később!');
      navigate('/support');
    } finally {
      setIsLoading(false);
    }
  };

  // Üzenet küldése
  const handleSendMessage = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!id || !newMessage.trim()) return;

    try {
      setIsSending(true);
      await addMessageToTicket(id, newMessage);
      setNewMessage('');
      await fetchTicketDetails();
      toast.success('Üzenet sikeresen elküldve!');
    } catch (error) {
      console.error('Hiba az üzenet küldésekor:', error);
      toast.error('Nem sikerült elküldeni az üzenetet. Kérjük, próbáld újra később!');
    } finally {
      setIsSending(false);
    }
  };

  // Ticket státuszának módosítása (csak adminoknak)
  const handleStatusChange = async (status: string) => {
    if (!id || !user?.isAdmin) return;

    try {
      setIsUpdatingStatus(true);
      await updateTicketStatus(id, status);
      await fetchTicketDetails();
      toast.success(`Ticket státusza sikeresen módosítva: ${status === 'open' ? 'Nyitott' : status === 'in_progress' ? 'Folyamatban' : 'Lezárva'}`);
    } catch (error) {
      console.error('Hiba a ticket státuszának módosításakor:', error);
      toast.error('Nem sikerült módosítani a ticket státuszát. Kérjük, próbáld újra később!');
    } finally {
      setIsUpdatingStatus(false);
    }
  };

  // Prioritás módosítása (csak adminoknak)
  const handlePriorityChange = async (priority: string) => {
    if (!id || !user?.isAdmin) return;

    try {
      setIsUpdatingStatus(true);
      await updateTicketStatus(id, ticket?.status || 'open', priority);
      await fetchTicketDetails();
      toast.success(`Ticket prioritása sikeresen módosítva: ${priority === 'low' ? 'Alacsony' : priority === 'medium' ? 'Közepes' : 'Magas'}`);
    } catch (error) {
      console.error('Hiba a ticket prioritásának módosításakor:', error);
      toast.error('Nem sikerült módosítani a ticket prioritását. Kérjük, próbáld újra később!');
    } finally {
      setIsUpdatingStatus(false);
    }
  };

  // AI válaszjavaslat kérése (csak adminoknak)
  const handleGetAISuggestion = async () => {
    if (!id || !user?.isAdmin) return;

    try {
      setIsGeneratingAI(true);
      const { suggestion } = await getAISuggestion(id);
      setNewMessage(suggestion);
      toast.success('AI válaszjavaslat sikeresen generálva!');
    } catch (error) {
      console.error('Hiba az AI válaszjavaslat kérésekor:', error);
      toast.error('Nem sikerült generálni AI válaszjavaslatot. Kérjük, próbáld újra később!');
    } finally {
      setIsGeneratingAI(false);
    }
  };

  // Státusz badge színének meghatározása
  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'open':
        return (
          <Badge variant="outline" className="bg-blue-50 text-blue-700 border-blue-200 flex items-center gap-1">
            <Clock className="h-3 w-3" />
            <span>Nyitott</span>
          </Badge>
        );
      case 'in_progress':
        return (
          <Badge variant="outline" className="bg-yellow-50 text-yellow-700 border-yellow-200 flex items-center gap-1">
            <AlertCircle className="h-3 w-3" />
            <span>Folyamatban</span>
          </Badge>
        );
      case 'closed':
        return (
          <Badge variant="outline" className="bg-green-50 text-green-700 border-green-200 flex items-center gap-1">
            <CheckCircle className="h-3 w-3" />
            <span>Lezárva</span>
          </Badge>
        );
      default:
        return <Badge>{status}</Badge>;
    }
  };

  // Prioritás badge színének meghatározása
  const getPriorityBadge = (priority: string) => {
    switch (priority) {
      case 'low':
        return <Badge variant="outline" className="bg-gray-50 text-gray-700 border-gray-200">Alacsony</Badge>;
      case 'medium':
        return <Badge variant="outline" className="bg-blue-50 text-blue-700 border-blue-200">Közepes</Badge>;
      case 'high':
        return <Badge variant="outline" className="bg-red-50 text-red-700 border-red-200">Magas</Badge>;
      default:
        return <Badge variant="outline">{priority}</Badge>;
    }
  };

  // Komponens betöltésekor lekérdezzük a ticket részleteit
  useEffect(() => {
    fetchTicketDetails();
  }, [id]);

  // Üzenetek betöltése után görgessünk az aljára
  useEffect(() => {
    if (messagesEndRef.current) {
      messagesEndRef.current.scrollIntoView({ behavior: 'smooth' });
    }
  }, [ticket?.messages]);

  if (isLoading) {
    return (
      <div className="container mx-auto px-4 py-8 mb-20 md:mb-0 md:pt-24 flex justify-center">
        <Loader2 className="h-8 w-8 animate-spin text-primary" />
      </div>
    );
  }

  if (!ticket) {
    return (
      <div className="container mx-auto px-4 py-8 mb-20 md:mb-0 md:pt-24">
        <div className="text-center">
          <h2 className="text-2xl font-bold">Ticket nem található</h2>
          <p className="mt-2 text-gray-500">A keresett ticket nem található vagy nincs jogosultságod a megtekintéséhez.</p>
          <Button onClick={() => navigate('/support')} className="mt-4">
            <ArrowLeft className="h-4 w-4 mr-2" />
            Vissza a támogatáshoz
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8 mb-20 md:mb-0 md:pt-24">
      <div className="max-w-4xl mx-auto">
        <Button
          variant="ghost"
          className="mb-4"
          onClick={() => navigate('/support')}
        >
          <ArrowLeft className="h-4 w-4 mr-2" />
          Vissza a támogatáshoz
        </Button>

        <Card>
          <CardHeader>
            <div className="flex flex-col md:flex-row md:items-center justify-between gap-4">
              <div>
                <CardTitle className="text-xl">{ticket.subject}</CardTitle>
                <CardDescription className="mt-1">
                  Létrehozva: {format(new Date(ticket.createdAt), 'yyyy. MMMM d. HH:mm', { locale: hu })}
                </CardDescription>
              </div>
              <div className="flex flex-wrap gap-2">
                {getStatusBadge(ticket.status)}
                {getPriorityBadge(ticket.priority)}
              </div>
            </div>

            {user?.id === '11383db6-ab6e-4810-81a9-dc5ac1426d3a' && (
              <div className="flex flex-col sm:flex-row gap-4 mt-4 pt-4 border-t">
                <div className="flex-1">
                  <p className="text-sm text-gray-500 mb-1">Státusz módosítása</p>
                  <Select
                    value={ticket.status}
                    onValueChange={handleStatusChange}
                    disabled={isUpdatingStatus}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Státusz" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="open">Nyitott</SelectItem>
                      <SelectItem value="in_progress">Folyamatban</SelectItem>
                      <SelectItem value="closed">Lezárva</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div className="flex-1">
                  <p className="text-sm text-gray-500 mb-1">Prioritás módosítása</p>
                  <Select
                    value={ticket.priority}
                    onValueChange={handlePriorityChange}
                    disabled={isUpdatingStatus}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Prioritás" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="low">Alacsony</SelectItem>
                      <SelectItem value="medium">Közepes</SelectItem>
                      <SelectItem value="high">Magas</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>
            )}
          </CardHeader>

          <CardContent>
            <div className="space-y-6">
              <div className="flex items-center gap-2">
                <Avatar>
                  <AvatarFallback>{ticket.creator?.name?.charAt(0) || 'U'}</AvatarFallback>
                </Avatar>
                <div>
                  <p className="font-medium">{ticket.creator?.name || 'Felhasználó'}</p>
                  <p className="text-sm text-gray-500">{ticket.creator?.email}</p>
                </div>
              </div>

              <Separator />

              <div className="space-y-6 max-h-[400px] overflow-y-auto p-2">
                {ticket.messages?.map((message) => (
                  <div
                    key={message.id}
                    className={`flex gap-4 ${message.isFromAdmin ? 'justify-start' : 'justify-start'}`}
                  >
                    <Avatar>
                      <AvatarFallback>
                        {message.isFromAdmin ? 'A' : message.user?.name?.charAt(0) || 'U'}
                      </AvatarFallback>
                    </Avatar>
                    <div className="space-y-2 max-w-[80%]">
                      <div className="flex items-center gap-2">
                        <p className="font-medium">
                          {message.isFromAdmin ? 'Ügyfélszolgálat' : message.user?.name || 'Felhasználó'}
                        </p>
                        <span className="text-xs text-gray-500">
                          {format(new Date(message.createdAt), 'yyyy. MM. dd. HH:mm', { locale: hu })}
                        </span>
                      </div>
                      <div className={`p-3 rounded-lg ${message.isFromAdmin ? 'bg-primary/10' : 'bg-gray-100'}`}>
                        <p className="whitespace-pre-wrap">{message.content}</p>
                      </div>
                    </div>
                  </div>
                ))}
                <div ref={messagesEndRef} />
              </div>
            </div>
          </CardContent>

          <CardFooter>
            {ticket.status !== 'closed' ? (
              <form onSubmit={handleSendMessage} className="w-full space-y-4">
                <div className="flex flex-col space-y-2">
                  <div className="flex justify-between items-center">
                    <label htmlFor="message" className="text-sm font-medium">
                      Új üzenet
                    </label>
                    {user?.id === '11383db6-ab6e-4810-81a9-dc5ac1426d3a' && (
                      <Button
                        type="button"
                        variant="outline"
                        size="sm"
                        onClick={handleGetAISuggestion}
                        disabled={isGeneratingAI}
                        className="flex items-center gap-1"
                      >
                        {isGeneratingAI ? (
                          <Loader2 className="h-3 w-3 animate-spin" />
                        ) : (
                          <Sparkles className="h-3 w-3" />
                        )}
                        <span>AI javaslat</span>
                      </Button>
                    )}
                  </div>
                  <Textarea
                    id="message"
                    placeholder="Írd be az üzenetedet..."
                    value={newMessage}
                    onChange={(e) => setNewMessage(e.target.value)}
                    disabled={isSending}
                    rows={4}
                    className="resize-none"
                  />
                </div>
                <Button
                  type="submit"
                  className="w-full"
                  disabled={isSending || !newMessage.trim()}
                >
                  {isSending ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      Küldés...
                    </>
                  ) : (
                    <>
                      <Send className="mr-2 h-4 w-4" />
                      Üzenet küldése
                    </>
                  )}
                </Button>
              </form>
            ) : (
              <div className="w-full p-4 bg-gray-50 rounded-lg text-center">
                <CheckCircle className="h-6 w-6 mx-auto text-green-500 mb-2" />
                <p className="font-medium">Ez a ticket le van zárva</p>
                <p className="text-sm text-gray-500 mt-1">
                  Nem küldhetsz több üzenetet ehhez a tickethez.
                </p>
                {user?.id === '11383db6-ab6e-4810-81a9-dc5ac1426d3a' && (
                  <Button
                    onClick={() => handleStatusChange('open')}
                    variant="outline"
                    className="mt-4"
                    disabled={isUpdatingStatus}
                  >
                    Ticket újranyitása
                  </Button>
                )}
              </div>
            )}
          </CardFooter>
        </Card>
      </div>
    </div>
  );
};

export default TicketDetail;
