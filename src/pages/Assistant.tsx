import React, { useState, useRef, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Textarea } from "@/components/ui/textarea";
import { Input } from "@/components/ui/input";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { SendHorizontal, Loader2, BookOpen, GraduationCap, Languages, MessageSquare, CheckCircle2 } from "lucide-react";
import { AssistantMessage, sendMessageToAssistant, getGrammarExplanation, getTranslation, getGrammarCheck, getExampleSentences, startConversationPractice } from "@/services/assistantService";
import MarkdownRenderer from "@/components/MarkdownRenderer";

const Assistant = () => {
  // <PERSON><PERSON>potok
  const [activeTab, setActiveTab] = useState("chat");
  const [messages, setMessages] = useState<AssistantMessage[]>([]);
  const [inputMessage, setInputMessage] = useState("");
  const [isLoading, setIsLoading] = useState(false);
  const [grammarTopic, setGrammarTopic] = useState("");
  const [grammarResult, setGrammarResult] = useState("");
  const [translationText, setTranslationText] = useState("");
  const [translationDirection, setTranslationDirection] = useState<"hu" | "de">("de");
  const [translationResult, setTranslationResult] = useState("");
  const [grammarCheckText, setGrammarCheckText] = useState("");
  const [grammarCheckResult, setGrammarCheckResult] = useState("");
  const [exampleWord, setExampleWord] = useState("");
  const [exampleCount, setExampleCount] = useState("3");
  const [exampleResult, setExampleResult] = useState("");
  const [conversationTopic, setConversationTopic] = useState("");
  const [conversationLevel, setConversationLevel] = useState<"beginner" | "intermediate" | "advanced">("beginner");
  const [conversationResult, setConversationResult] = useState("");

  const messagesEndRef = useRef<HTMLDivElement>(null);

  // Automatikus görgetés a chat alján
  useEffect(() => {
    if (messagesEndRef.current) {
      messagesEndRef.current.scrollIntoView({ behavior: "smooth" });
    }
  }, [messages]);

  // Üzenet küldése a chatben
  const handleSendMessage = async () => {
    if (!inputMessage.trim()) return;

    // Felhasználói üzenet hozzáadása
    const userMessage: AssistantMessage = {
      role: "user",
      content: inputMessage,
    };
    setMessages((prev) => [...prev, userMessage]);
    setInputMessage("");
    setIsLoading(true);

    try {
      // Asszisztens válaszának lekérése
      const response = await sendMessageToAssistant([...messages, userMessage]);

      // Asszisztens válaszának hozzáadása
      const assistantMessage: AssistantMessage = {
        role: "assistant",
        content: response,
      };
      setMessages((prev) => [...prev, assistantMessage]);
    } catch (error) {
      console.error("Hiba az asszisztens válaszának lekérésekor:", error);
      // Hibaüzenet hozzáadása
      const errorMessage: AssistantMessage = {
        role: "assistant",
        content: "Sajnos hiba történt a válasz generálásakor. Kérlek, próbáld újra később.",
      };
      setMessages((prev) => [...prev, errorMessage]);
    } finally {
      setIsLoading(false);
    }
  };

  // Nyelvtani magyarázat lekérése
  const handleGrammarExplanation = async () => {
    if (!grammarTopic.trim()) return;
    setIsLoading(true);
    setGrammarResult("");

    try {
      const result = await getGrammarExplanation(grammarTopic);
      setGrammarResult(result);
    } catch (error) {
      console.error("Hiba a nyelvtani magyarázat lekérésekor:", error);
      setGrammarResult("Sajnos hiba történt a nyelvtani magyarázat generálásakor. Kérlek, próbáld újra később.");
    } finally {
      setIsLoading(false);
    }
  };

  // Fordítás lekérése
  const handleTranslation = async () => {
    if (!translationText.trim()) return;
    setIsLoading(true);
    setTranslationResult("");

    try {
      const result = await getTranslation(translationText, translationDirection);
      setTranslationResult(result);
    } catch (error) {
      console.error("Hiba a fordítás lekérésekor:", error);
      setTranslationResult("Sajnos hiba történt a fordítás generálásakor. Kérlek, próbáld újra később.");
    } finally {
      setIsLoading(false);
    }
  };

  // Nyelvtani ellenőrzés lekérése
  const handleGrammarCheck = async () => {
    if (!grammarCheckText.trim()) return;
    setIsLoading(true);
    setGrammarCheckResult("");

    try {
      const result = await getGrammarCheck(grammarCheckText);
      setGrammarCheckResult(result);
    } catch (error) {
      console.error("Hiba a nyelvtani ellenőrzés lekérésekor:", error);
      setGrammarCheckResult("Sajnos hiba történt a nyelvtani ellenőrzés generálásakor. Kérlek, próbáld újra később.");
    } finally {
      setIsLoading(false);
    }
  };

  // Példamondatok lekérése
  const handleExampleSentences = async () => {
    if (!exampleWord.trim()) return;
    setIsLoading(true);
    setExampleResult("");

    try {
      const result = await getExampleSentences(exampleWord, parseInt(exampleCount));
      setExampleResult(result);
    } catch (error) {
      console.error("Hiba a példamondatok lekérésekor:", error);
      setExampleResult("Sajnos hiba történt a példamondatok generálásakor. Kérlek, próbáld újra később.");
    } finally {
      setIsLoading(false);
    }
  };

  // Beszélgetési gyakorlat indítása
  const handleConversationPractice = async () => {
    if (!conversationTopic.trim()) return;
    setIsLoading(true);
    setConversationResult("");

    try {
      const result = await startConversationPractice(conversationTopic, conversationLevel);
      setConversationResult(result);
    } catch (error) {
      console.error("Hiba a beszélgetési gyakorlat indításakor:", error);
      setConversationResult("Sajnos hiba történt a beszélgetési gyakorlat indításakor. Kérlek, próbáld újra később.");
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="container mx-auto px-4 py-8 mb-20 md:mb-0 md:pt-24">
      <h1 className="text-2xl font-bold text-center mb-6">Nyelvi Asszisztens</h1>

      <Tabs defaultValue="chat" value={activeTab} onValueChange={setActiveTab} className="max-w-4xl mx-auto">
        <TabsList className="grid grid-cols-3 md:grid-cols-6 mb-6">
          <TabsTrigger value="chat" className="flex items-center gap-2">
            <MessageSquare className="h-4 w-4" />
            <span className="hidden md:inline">Chat</span>
          </TabsTrigger>
          <TabsTrigger value="grammar" className="flex items-center gap-2">
            <BookOpen className="h-4 w-4" />
            <span className="hidden md:inline">Nyelvtan</span>
          </TabsTrigger>
          <TabsTrigger value="translation" className="flex items-center gap-2">
            <Languages className="h-4 w-4" />
            <span className="hidden md:inline">Fordítás</span>
          </TabsTrigger>
          <TabsTrigger value="check" className="flex items-center gap-2">
            <CheckCircle2 className="h-4 w-4" />
            <span className="hidden md:inline">Ellenőrzés</span>
          </TabsTrigger>
          <TabsTrigger value="examples" className="flex items-center gap-2">
            <BookOpen className="h-4 w-4" />
            <span className="hidden md:inline">Példák</span>
          </TabsTrigger>
          <TabsTrigger value="conversation" className="flex items-center gap-2">
            <GraduationCap className="h-4 w-4" />
            <span className="hidden md:inline">Beszélgetés</span>
          </TabsTrigger>
        </TabsList>

        {/* Chat fül */}
        <TabsContent value="chat">
          <Card>
            <CardHeader>
              <CardTitle>Beszélgess az asszisztenssel</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="flex flex-col h-[500px]">
                <div className="flex-1 overflow-y-auto mb-4 space-y-4 p-4 border rounded-md">
                  {messages.length === 0 ? (
                    <div className="text-center text-gray-500 mt-10">
                      <p>Üdvözöllek! Én vagyok a német nyelvi asszisztensed.</p>
                      <p className="mt-2">Kérdezz bármit a német nyelvvel kapcsolatban!</p>
                    </div>
                  ) : (
                    messages.map((msg, index) => (
                      <div
                        key={index}
                        className={`flex ${
                          msg.role === "user" ? "justify-end" : "justify-start"
                        }`}
                      >
                        <div
                          className={`max-w-[80%] rounded-lg px-4 py-2 ${
                            msg.role === "user"
                              ? "bg-language-primary text-white"
                              : "bg-gray-100"
                          }`}
                        >
                          {msg.role === "assistant" ? (
                            <MarkdownRenderer content={msg.content} />
                          ) : (
                            <div className="whitespace-pre-wrap">{msg.content}</div>
                          )}
                        </div>
                      </div>
                    ))
                  )}
                  <div ref={messagesEndRef} />
                </div>
                <div className="flex gap-2">
                  <Textarea
                    placeholder="Írj egy üzenetet..."
                    value={inputMessage}
                    onChange={(e) => setInputMessage(e.target.value)}
                    className="flex-1"
                    onKeyDown={(e) => {
                      if (e.key === "Enter" && !e.shiftKey) {
                        e.preventDefault();
                        handleSendMessage();
                      }
                    }}
                    disabled={isLoading}
                  />
                  <Button
                    onClick={handleSendMessage}
                    disabled={isLoading || !inputMessage.trim()}
                  >
                    {isLoading ? (
                      <Loader2 className="h-4 w-4 animate-spin" />
                    ) : (
                      <SendHorizontal className="h-4 w-4" />
                    )}
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Nyelvtan fül */}
        <TabsContent value="grammar">
          <Card>
            <CardHeader>
              <CardTitle>Nyelvtani magyarázatok</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="flex gap-2">
                  <Input
                    placeholder="Milyen nyelvtani témáról szeretnél tanulni? (pl. névelők, igeidők, elöljárószók...)"
                    value={grammarTopic}
                    onChange={(e) => setGrammarTopic(e.target.value)}
                    className="flex-1"
                    disabled={isLoading}
                  />
                  <Button
                    onClick={handleGrammarExplanation}
                    disabled={isLoading || !grammarTopic.trim()}
                  >
                    {isLoading ? (
                      <Loader2 className="h-4 w-4 animate-spin" />
                    ) : (
                      "Kérdezz"
                    )}
                  </Button>
                </div>

                {grammarResult && (
                  <div className="mt-4 p-4 border rounded-md bg-gray-50">
                    <MarkdownRenderer content={grammarResult} />
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Fordítás fül */}
        <TabsContent value="translation">
          <Card>
            <CardHeader>
              <CardTitle>Fordítás</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="flex gap-2 items-start">
                  <div className="flex-1">
                    <Textarea
                      placeholder="Írd be a fordítandó szöveget..."
                      value={translationText}
                      onChange={(e) => setTranslationText(e.target.value)}
                      className="w-full"
                      rows={4}
                      disabled={isLoading}
                    />
                  </div>
                  <div className="flex flex-col gap-2">
                    <Select
                      value={translationDirection}
                      onValueChange={(value) => setTranslationDirection(value as "hu" | "de")}
                      disabled={isLoading}
                    >
                      <SelectTrigger className="w-[180px]">
                        <SelectValue placeholder="Fordítás iránya" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="de">Magyarról németre</SelectItem>
                        <SelectItem value="hu">Németről magyarra</SelectItem>
                      </SelectContent>
                    </Select>
                    <Button
                      onClick={handleTranslation}
                      disabled={isLoading || !translationText.trim()}
                      className="w-full"
                    >
                      {isLoading ? (
                        <Loader2 className="h-4 w-4 animate-spin" />
                      ) : (
                        "Fordítás"
                      )}
                    </Button>
                  </div>
                </div>

                {translationResult && (
                  <div className="mt-4 p-4 border rounded-md bg-gray-50">
                    <MarkdownRenderer content={translationResult} />
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Ellenőrzés fül */}
        <TabsContent value="check">
          <Card>
            <CardHeader>
              <CardTitle>Nyelvtani ellenőrzés</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="flex gap-2 items-start">
                  <Textarea
                    placeholder="Írd be a német szöveget, amit ellenőrizni szeretnél..."
                    value={grammarCheckText}
                    onChange={(e) => setGrammarCheckText(e.target.value)}
                    className="flex-1"
                    rows={4}
                    disabled={isLoading}
                  />
                  <Button
                    onClick={handleGrammarCheck}
                    disabled={isLoading || !grammarCheckText.trim()}
                  >
                    {isLoading ? (
                      <Loader2 className="h-4 w-4 animate-spin" />
                    ) : (
                      "Ellenőrzés"
                    )}
                  </Button>
                </div>

                {grammarCheckResult && (
                  <div className="mt-4 p-4 border rounded-md bg-gray-50">
                    <MarkdownRenderer content={grammarCheckResult} />
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Példák fül */}
        <TabsContent value="examples">
          <Card>
            <CardHeader>
              <CardTitle>Példamondatok</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="flex gap-2 items-start">
                  <Input
                    placeholder="Írd be a német szót, amihez példamondatokat szeretnél..."
                    value={exampleWord}
                    onChange={(e) => setExampleWord(e.target.value)}
                    className="flex-1"
                    disabled={isLoading}
                  />
                  <Select
                    value={exampleCount}
                    onValueChange={setExampleCount}
                    disabled={isLoading}
                  >
                    <SelectTrigger className="w-[100px]">
                      <SelectValue placeholder="Darab" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="3">3 db</SelectItem>
                      <SelectItem value="5">5 db</SelectItem>
                      <SelectItem value="10">10 db</SelectItem>
                    </SelectContent>
                  </Select>
                  <Button
                    onClick={handleExampleSentences}
                    disabled={isLoading || !exampleWord.trim()}
                  >
                    {isLoading ? (
                      <Loader2 className="h-4 w-4 animate-spin" />
                    ) : (
                      "Kérés"
                    )}
                  </Button>
                </div>

                {exampleResult && (
                  <div className="mt-4 p-4 border rounded-md bg-gray-50">
                    <MarkdownRenderer content={exampleResult} />
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Beszélgetés fül */}
        <TabsContent value="conversation">
          <Card>
            <CardHeader>
              <CardTitle>Beszélgetési gyakorlat</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="flex gap-2 items-start">
                  <Input
                    placeholder="Milyen témában szeretnél beszélgetni? (pl. hobbi, utazás, időjárás...)"
                    value={conversationTopic}
                    onChange={(e) => setConversationTopic(e.target.value)}
                    className="flex-1"
                    disabled={isLoading}
                  />
                  <Select
                    value={conversationLevel}
                    onValueChange={(value) => setConversationLevel(value as "beginner" | "intermediate" | "advanced")}
                    disabled={isLoading}
                  >
                    <SelectTrigger className="w-[180px]">
                      <SelectValue placeholder="Nyelvi szint" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="beginner">Kezdő</SelectItem>
                      <SelectItem value="intermediate">Középhaladó</SelectItem>
                      <SelectItem value="advanced">Haladó</SelectItem>
                    </SelectContent>
                  </Select>
                  <Button
                    onClick={handleConversationPractice}
                    disabled={isLoading || !conversationTopic.trim()}
                  >
                    {isLoading ? (
                      <Loader2 className="h-4 w-4 animate-spin" />
                    ) : (
                      "Indítás"
                    )}
                  </Button>
                </div>

                {conversationResult && (
                  <div className="mt-4">
                    <div className="p-4 border rounded-md bg-gray-50 mb-4">
                      <MarkdownRenderer content={conversationResult} />
                    </div>

                    <div className="flex gap-2">
                      <Textarea
                        placeholder="Írd be a válaszodat..."
                        className="flex-1"
                        rows={3}
                        disabled={isLoading}
                      />
                      <Button disabled={isLoading}>
                        {isLoading ? (
                          <Loader2 className="h-4 w-4 animate-spin" />
                        ) : (
                          <SendHorizontal className="h-4 w-4" />
                        )}
                      </Button>
                    </div>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default Assistant;
