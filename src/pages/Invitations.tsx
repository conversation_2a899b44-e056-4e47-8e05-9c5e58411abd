import React from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { useNavigate } from 'react-router-dom';
import { motion } from 'framer-motion';
import InvitationForm from '@/components/invitation/InvitationForm';
import InvitationsList from '@/components/invitation/InvitationsList';

const Invitations: React.FC = () => {
  const { user } = useAuth();
  const navigate = useNavigate();

  // Ha nincs bejelentke<PERSON>tt fel<PERSON>, átirányítjuk a bejelentkezési oldalra
  React.useEffect(() => {
    if (!user) {
      navigate('/login');
    }
  }, [user, navigate]);

  if (!user) {
    return null;
  }

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      exit={{ opacity: 0, y: 20 }}
      transition={{ duration: 0.3 }}
      className="container mx-auto py-8 px-4"
    >
      <h1 className="text-3xl font-bold mb-8">Barátok meghívása</h1>
      
      <div className="grid grid-cols-1 gap-8">
        <InvitationForm onSuccess={() => {
          // Sikeres meghívó küldés után frissítjük a listát
          // Ezt a komponens újrarenderelésével érjük el
          const invitationsList = document.getElementById('invitations-list');
          if (invitationsList) {
            invitationsList.classList.add('refresh');
            setTimeout(() => {
              invitationsList.classList.remove('refresh');
            }, 100);
          }
        }} />
        
        <div id="invitations-list">
          <InvitationsList />
        </div>
      </div>
    </motion.div>
  );
};

export default Invitations;
