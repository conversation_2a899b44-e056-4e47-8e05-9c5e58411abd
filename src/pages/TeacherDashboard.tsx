import React, { useState, useEffect } from "react";
import { Card, CardContent, CardDescription, Card<PERSON>ooter, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { useAuth } from "@/contexts/AuthContext";
import { toast } from "sonner";
import { getStudents, inviteStudent, TeacherStudentRelation } from "@/services/teacherService";
import { Users, UserPlus, RefreshCw, Mail, Calendar, Coins } from "lucide-react";
import { formatDistanceToNow } from "date-fns";
import { hu } from "date-fns/locale";

const TeacherDashboard = () => {
  const [students, setStudents] = useState<TeacherStudentRelation[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isInviting, setIsInviting] = useState(false);
  const [email, setEmail] = useState("");
  const { user, isTeacher } = useAuth();

  // Diákok lekérése
  const fetchStudents = async () => {
    try {
      setIsLoading(true);
      const data = await getStudents();
      setStudents(data);
    } catch (error) {
      console.error("Hiba a diákok lekérésekor:", error);
      toast.error("Nem sikerült lekérni a diákokat. Kérjük, próbáld újra később!");
    } finally {
      setIsLoading(false);
    }
  };

  // Diák meghívása
  const handleInviteStudent = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!email) {
      toast.error("Kérjük, add meg a diák email címét!");
      return;
    }
    
    try {
      setIsInviting(true);
      await inviteStudent(email);
      toast.success("Meghívó sikeresen elküldve!");
      setEmail("");
      // Frissítsük a diákok listáját
      await fetchStudents();
    } catch (error) {
      console.error("Hiba a diák meghívása során:", error);
      toast.error("Nem sikerült elküldeni a meghívót. Kérjük, próbáld újra később!");
    } finally {
      setIsInviting(false);
    }
  };

  // Komponens betöltésekor lekérjük a diákokat
  useEffect(() => {
    if (isTeacher) {
      fetchStudents();
    }
  }, [isTeacher]);

  // Ha a felhasználó nem tanár, átirányítjuk a tanári előfizetés oldalra
  if (!isTeacher) {
    return (
      <div className="container mx-auto px-4 py-8 mb-20 md:mb-0 md:pt-24">
        <Card className="max-w-md mx-auto">
          <CardHeader>
            <CardTitle>Tanári funkciók</CardTitle>
            <CardDescription>
              A tanári funkciók használatához tanári előfizetés szükséges.
            </CardDescription>
          </CardHeader>
          <CardContent>
            <p className="text-center mb-4">
              Előfizetve a tanári csomagra hozzáférhetsz a tanári funkciókhoz, például diákok meghívásához, haladáskövetéshez és csoportos feladatok létrehozásához.
            </p>
          </CardContent>
          <CardFooter>
            <Button
              onClick={() => window.location.href = "/teacher/subscribe"}
              className="w-full"
            >
              Tanári előfizetés
            </Button>
          </CardFooter>
        </Card>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8 mb-20 md:mb-0 md:pt-24">
      <h1 className="text-2xl font-bold text-center mb-6">Tanári vezérlőpult</h1>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
        {/* Diákok száma */}
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-lg flex items-center">
              <Users className="h-5 w-5 mr-2 text-primary" />
              Diákok
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-3xl font-bold">{students.length}</div>
            <p className="text-sm text-muted-foreground">
              {user?.maxStudents ? `Maximum ${user.maxStudents} diák` : 'Diákok száma'}
            </p>
          </CardContent>
        </Card>

        {/* Pontok */}
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-lg flex items-center">
              <Coins className="h-5 w-5 mr-2 text-primary" />
              Pontok
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-3xl font-bold">{user?.points || 0}</div>
            <p className="text-sm text-muted-foreground">
              {user?.monthlyPoints ? `${user.monthlyPoints} pont havonta` : 'Elérhető pontok'}
            </p>
          </CardContent>
        </Card>

        {/* Előfizetés */}
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-lg flex items-center">
              <Calendar className="h-5 w-5 mr-2 text-primary" />
              Előfizetés
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-xl font-bold">Aktív</div>
            <p className="text-sm text-muted-foreground">
              Tanári csomag
            </p>
          </CardContent>
        </Card>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        {/* Diák meghívása */}
        <Card className="md:col-span-1">
          <CardHeader>
            <CardTitle className="flex items-center">
              <UserPlus className="h-5 w-5 mr-2" />
              Diák meghívása
            </CardTitle>
            <CardDescription>
              Hívj meg diákokat a tanári csoportodba
            </CardDescription>
          </CardHeader>
          <CardContent>
            <form onSubmit={handleInviteStudent}>
              <div className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="email">Email cím</Label>
                  <Input
                    id="email"
                    type="email"
                    placeholder="<EMAIL>"
                    value={email}
                    onChange={(e) => setEmail(e.target.value)}
                    required
                  />
                </div>
                <Button
                  type="submit"
                  className="w-full"
                  disabled={isInviting}
                >
                  {isInviting ? (
                    <>
                      <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                      Meghívás...
                    </>
                  ) : (
                    <>
                      <Mail className="h-4 w-4 mr-2" />
                      Meghívó küldése
                    </>
                  )}
                </Button>
              </div>
            </form>
          </CardContent>
        </Card>

        {/* Diákok listája */}
        <Card className="md:col-span-2">
          <CardHeader>
            <div className="flex justify-between items-center">
              <CardTitle className="flex items-center">
                <Users className="h-5 w-5 mr-2" />
                Diákok
              </CardTitle>
              <Button
                variant="outline"
                size="sm"
                onClick={fetchStudents}
                disabled={isLoading}
              >
                <RefreshCw className={`h-4 w-4 ${isLoading ? 'animate-spin' : ''}`} />
              </Button>
            </div>
            <CardDescription>
              A tanári csoportodba meghívott diákok
            </CardDescription>
          </CardHeader>
          <CardContent>
            {isLoading ? (
              <div className="flex justify-center py-8">
                <RefreshCw className="h-8 w-8 animate-spin text-primary" />
              </div>
            ) : students.length === 0 ? (
              <div className="text-center py-8 text-muted-foreground">
                <p>Még nincsenek diákok a csoportodban</p>
                <p className="text-sm mt-2">Használd a meghívó funkciót diákok hozzáadásához</p>
              </div>
            ) : (
              <div className="space-y-4">
                {students.map((relation) => (
                  <div
                    key={relation.id}
                    className="flex items-center justify-between p-3 border rounded-lg hover:bg-gray-50"
                  >
                    <div>
                      <div className="font-medium">{relation.student.name}</div>
                      <div className="text-sm text-muted-foreground">{relation.student.email}</div>
                    </div>
                    <div className="text-sm text-muted-foreground">
                      {formatDistanceToNow(new Date(relation.createdAt), { addSuffix: true, locale: hu })}
                    </div>
                  </div>
                ))}
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default TeacherDashboard;
