
import React, { useState, useEffect, useCallback } from "react";
import { Link } from "react-router-dom";
import { Book, MessageSquare, GraduationCap, Gamepad2, ArrowRight, Bot, Sparkles, Globe, Headphones, Brain, Target, ChevronRight, CheckCircle, Clock, Lightbulb, Rocket, Zap, Award, CreditCard, Coins, VolumeIcon, Users, Crown, BadgeCheck, ChevronLeft, ChevronRight as ChevronRightIcon, Puzzle, Mic, BookOpen } from "lucide-react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle, CardFooter } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { vocabularyData, phrasesData } from "@/data/languageData";
import { motion, AnimatePresence } from "framer-motion";
import { Progress } from "@/components/ui/progress";
import HomeBackground from "@/components/HomeBackground";
import { InteractiveHoverButton } from "@/components/ui/interactive-hover-button";
import HomeFooter from "@/components/HomeFooter";
import WelcomeMessage from "@/components/WelcomeMessage";
import SEOHead from "@/components/SEOHead";

const Home = () => {
  // State for animations and interactive elements
  const [activeTab, setActiveTab] = useState("vocabulary");
  const [progress, setProgress] = useState(0);
  const [isLoaded, setIsLoaded] = useState(false);
  const [statCount, setStatCount] = useState({ words: 0, phrases: 0, quizzes: 0 });
  const [carouselIndex, setCarouselIndex] = useState(0);
  const carouselItems = ["action", "community", "games", "study"];

  // Get current date for greeting
  const getCurrentGreeting = () => {
    const hour = new Date().getHours();
    if (hour < 12) return "Jó reggelt!";
    if (hour < 18) return "Jó napot!";
    return "Jó estét!";
  };

  // Carousel navigation functions
  const nextCarouselItem = useCallback(() => {
    setCarouselIndex((prev) => (prev + 1) % carouselItems.length);
  }, [carouselItems.length]);

  const prevCarouselItem = useCallback(() => {
    setCarouselIndex((prev) => (prev - 1 + carouselItems.length) % carouselItems.length);
  }, [carouselItems.length]);

  // Auto-rotate carousel
  useEffect(() => {
    const interval = setInterval(() => {
      nextCarouselItem();
    }, 10000); // Rotate every 10 seconds

    return () => clearInterval(interval);
  }, [nextCarouselItem]);

  // Animation for counting up statistics
  useEffect(() => {
    setIsLoaded(true);

    const timer = setTimeout(() => {
      const countInterval = setInterval(() => {
        setStatCount(prev => {
          const newWords = Math.min(prev.words + 5, vocabularyData.length);
          const newPhrases = Math.min(prev.phrases + 3, phrasesData.length);
          const newQuizzes = Math.min(prev.quizzes + 1, 48);

          if (newWords === vocabularyData.length &&
              newPhrases === phrasesData.length &&
              newQuizzes === 48) {
            clearInterval(countInterval);
          }

          return {
            words: newWords,
            phrases: newPhrases,
            quizzes: newQuizzes
          };
        });
      }, 50);

      return () => clearInterval(countInterval);
    }, 500);

    // Simulate progress loading
    const progressInterval = setInterval(() => {
      setProgress(prev => {
        if (prev >= 100) {
          clearInterval(progressInterval);
          return 100;
        }
        return prev + 1;
      });
    }, 30);

    return () => {
      clearTimeout(timer);
      clearInterval(progressInterval);
    };
  }, []);

  return (
    <>
      <SEOHead
        title="Magyar-Német Nyelvtanuló - Interaktív Német Nyelvtanulás"
        description="Tanulj németül interaktív módszerekkel! Szókincs, mondatok, kvízek, játékok és kiejtésgyakorlás. Kezdő és középhaladó szintű német nyelvtanulás magyarul."
        keywords="német nyelvtanulás, német szókincs, német mondatok, német kvíz, német játékok, német kiejtés, nyelvtanulás, német nyelv, A1 német, B1 német, interaktív nyelvtanulás"
        url="https://digitalisnemet.hu/"
      />
      <div className="relative container mx-auto px-4 py-8 mb-20 md:mb-0 md:pt-16">
        <HomeBackground />
      <WelcomeMessage />


      {/* Hero Section */}
      <motion.div
        className="mb-16 max-w-5xl mx-auto"
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
      >
        <div className="bg-gradient-to-br from-white via-white to-language-primary/5 rounded-3xl shadow-xl border border-language-border/10 p-8 md:p-12 relative overflow-hidden">
          {/* Enhanced background decoration with more visual elements */}
          <div className="absolute -top-24 -right-24 w-64 h-64 bg-language-primary/10 rounded-full blur-3xl"></div>
          <div className="absolute -bottom-32 -left-32 w-80 h-80 bg-language-accent/10 rounded-full blur-3xl"></div>
          <div className="absolute top-1/2 right-1/4 w-40 h-40 bg-language-secondary/10 rounded-full blur-2xl"></div>
          <div className="absolute bottom-1/4 left-1/2 w-32 h-32 bg-language-quaternary/10 rounded-full blur-xl"></div>


          <div className="relative flex flex-col md:flex-row items-center justify-between gap-8">
            <div className="md:max-w-xl">
              <motion.div
                initial={{ opacity: 0, y: 10 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.2, duration: 0.4 }}
              >
                <Badge variant="plain" className="bg-language-primary/10 text-language-primary border-language-primary/20 mb-3 py-1.5 px-3">
                  <Sparkles className="h-3.5 w-3.5 mr-1.5" /> {getCurrentGreeting()}
                </Badge>
              </motion.div>

              <motion.div
                className="flex items-center mb-4"
                initial={{ opacity: 0, y: 10 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.3, duration: 0.4 }}
              >
                <div className="relative">
                  <motion.div
                    className="absolute -inset-1 rounded-full bg-gradient-to-r from-language-primary to-language-accent opacity-70 blur-sm"
                    animate={{
                      scale: [1, 1.05, 1],
                      opacity: [0.7, 0.8, 0.7]
                    }}
                    transition={{
                      duration: 3,
                      repeat: Infinity,
                      repeatType: "reverse"
                    }}
                  />
                  <div className="relative bg-white rounded-full p-1">
                    <BookOpen className="w-6 h-6 text-language-primary" />
                  </div>
                </div>
                <div className="flex flex-col ml-3">
                  <span className="bg-gradient-to-r from-language-primary via-language-secondary to-language-accent bg-clip-text text-transparent leading-tight font-bold text-xl">Magyar-Német</span>
                  <span className="text-xs text-gray-500 font-normal">Nyelvtanuló</span>
                </div>
                <span className="ml-2 text-xs bg-language-accent text-white px-1.5 py-0.5 rounded-md rotate-12 font-normal">Neu!</span>
              </motion.div>

              <motion.h1
                className="text-4xl md:text-5xl font-bold mb-4 leading-relaxed"
                initial={{ opacity: 0, y: 10 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.3, duration: 0.4 }}
              >
                <span className="inline-block pb-3 bg-gradient-to-r from-language-primary via-language-secondary to-language-accent bg-clip-text text-transparent">Nyelvtanuló Alkalmazás</span>
              </motion.h1>

              <motion.p
                className="text-xl text-gray-600 mb-6"
                initial={{ opacity: 0, y: 10 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.4, duration: 0.4 }}
              >
                Tanulj németül B1 szintig, interaktív és játékos módon, bárhol és bármikor.
                <span className="font-medium text-language-accent"> Most új kiejtésgyakorló és fordító funkciókkal!</span>
              </motion.p>

              {/* Enhanced statistics badges with animations */}
              <motion.div
                className="flex flex-wrap gap-3 mb-6"
                initial={{ opacity: 0, y: 10 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.5, duration: 0.4 }}
              >
                <motion.div
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                >
                  <Badge variant="plain" className="bg-language-primary/10 text-language-primary text-xs px-3 py-1 border border-language-primary/10 hover:border-language-primary/20 transition-colors">
                    <Book className="h-3.5 w-3.5 mr-1.5" /> {statCount.words} szó
                  </Badge>
                </motion.div>
                <motion.div
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                >
                  <Badge variant="plain" className="bg-language-secondary/10 text-language-secondary text-xs px-3 py-1 border border-language-secondary/10 hover:border-language-secondary/20 transition-colors">
                    <MessageSquare className="h-3.5 w-3.5 mr-1.5" /> {statCount.phrases} mondat
                  </Badge>
                </motion.div>
                <motion.div
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                >
                  <Badge variant="plain" className="bg-language-accent/10 text-language-accent text-xs px-3 py-1 border border-language-accent/10 hover:border-language-accent/20 transition-colors">
                    <GraduationCap className="h-3.5 w-3.5 mr-1.5" /> {statCount.quizzes} kvízkérdés
                  </Badge>
                </motion.div>
                <motion.div
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                >
                  <Badge variant="plain" className="bg-language-quaternary/10 text-language-quaternary text-xs px-3 py-1 border border-language-quaternary/10 hover:border-language-quaternary/20 transition-colors">
                    <Gamepad2 className="h-3.5 w-3.5 mr-1.5" /> 8+ játék
                  </Badge>
                </motion.div>
              </motion.div>

              {/* Enhanced features list with better icons and descriptions */}
              <motion.div
                className="mb-6 space-y-3"
                initial={{ opacity: 0, y: 10 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.55, duration: 0.4 }}
              >
                <div className="flex items-start gap-2">
                  <div className="mt-0.5 bg-green-100 rounded-full p-0.5">
                    <CheckCircle className="h-4 w-4 text-green-500" />
                  </div>
                  <div>
                    <span className="text-gray-800 font-medium">Kezdőtől középhaladó szintig (A1-B1)</span>
                    <p className="text-xs text-gray-500">Teljes tananyag kezdőtől a középhaladó szintig</p>
                  </div>
                </div>
                <div className="flex items-start gap-2">
                  <div className="mt-0.5 bg-blue-100 rounded-full p-0.5">
                    <Bot className="h-4 w-4 text-blue-500" />
                  </div>
                  <div>
                    <span className="text-gray-800 font-medium">AI nyelvi asszisztens</span>
                    <p className="text-xs text-gray-500">Beszédkészség fejlesztése és azonnali válaszok kérdéseidre</p>
                  </div>
                </div>
                <div className="flex items-start gap-2">
                  <div className="mt-0.5 bg-purple-100 rounded-full p-0.5">
                    <Gamepad2 className="h-4 w-4 text-purple-500" />
                  </div>
                  <div>
                    <span className="text-gray-800 font-medium">Játékos tanulási módszerek</span>
                    <p className="text-xs text-gray-500">Interaktív feladatok, játékok és kihívások</p>
                  </div>
                </div>
                <div className="flex items-start gap-2">
                  <div className="mt-0.5 bg-amber-100 rounded-full p-0.5">
                    <Mic className="h-4 w-4 text-amber-500" />
                  </div>
                  <div>
                    <span className="text-gray-800 font-medium">Beszédfelismerés és kiejtésgyakorlás</span>
                    <p className="text-xs text-gray-500">Fejleszd kiejtésed valós idejű visszajelzésekkel</p>
                  </div>
                </div>
                <div className="flex items-start gap-2">
                  <div className="mt-0.5 bg-teal-100 rounded-full p-0.5">
                    <Globe className="h-4 w-4 text-teal-500" />
                  </div>
                  <div>
                    <span className="text-gray-800 font-medium">Beépített fordító</span>
                    <p className="text-xs text-gray-500">Szöveg, hang és kamera alapú valós idejű fordítás</p>
                  </div>
                </div>
              </motion.div>

            </div>

            {/* Enhanced visual elements for the hero image */}
            <motion.div
              className="flex-shrink-0 relative"
              initial={{ opacity: 0, scale: 0.9 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ delay: 0.4, duration: 0.5 }}
            >
              <div className="absolute inset-0 bg-gradient-to-br from-language-primary/20 to-language-accent/20 rounded-full blur-xl"></div>
              <div className="relative bg-white p-6 rounded-full shadow-lg border border-language-border/50">
                <div className="relative w-48 h-48 md:w-56 md:h-56 flex items-center justify-center">
                  <img src="/german-flag.svg" alt="Német zászló" className="w-32 h-32 md:w-40 md:h-40 object-contain drop-shadow-lg" />
                  <div className="absolute inset-0 border-4 border-dashed border-language-primary/20 rounded-full animate-spin-slow"></div>

                  {/* More floating badges with enhanced styling */}
                  <div className="absolute -top-4 -right-2 bg-language-primary text-white px-2 py-1 rounded-lg text-xs shadow-lg transform rotate-12 flex items-center gap-1 animate-pulse">
                    <VolumeIcon className="h-3 w-3" />
                    <span>Kiejtés</span>
                  </div>
                  <div className="absolute -bottom-2 -left-6 bg-language-secondary text-white px-2 py-1 rounded-lg text-xs shadow-lg transform -rotate-6 flex items-center gap-1 animate-pulse" style={{ animationDelay: "1s" }}>
                    <MessageSquare className="h-3 w-3" />
                    <span>Beszédgyakorlat</span>
                  </div>
                  <div className="absolute top-1/4 right-0 bg-amber-500 text-white px-2 py-1 rounded-lg text-xs shadow-lg transform rotate-3 flex items-center gap-1 animate-pulse" style={{ animationDelay: "1.5s" }}>
                    <Mic className="h-3 w-3" />
                    <span>Beszédfelismerés</span>
                  </div>
                  <div className="absolute bottom-1/4 -right-6 bg-teal-500 text-white px-2 py-1 rounded-lg text-xs shadow-lg transform -rotate-12 flex items-center gap-1 animate-pulse" style={{ animationDelay: "2s" }}>
                    <Globe className="h-3 w-3" />
                    <span>Fordító</span>
                  </div>
                </div>
              </div>

              {/* Language level indicator */}
              <div className="absolute -bottom-8 right-0 bg-white/80 backdrop-blur-sm px-3 py-1.5 rounded-full shadow-md text-xs flex items-center gap-1.5 border border-language-border/30">
                <div className="flex gap-1">
                  <span className="inline-block px-1.5 py-0.5 rounded bg-green-100 text-green-700 font-medium">A1</span>
                  <span className="inline-block px-1.5 py-0.5 rounded bg-blue-100 text-blue-700 font-medium">A2</span>
                  <span className="inline-block px-1.5 py-0.5 rounded bg-purple-100 text-purple-700 font-medium">B1</span>
                </div>
                <span className="text-gray-500">nyelvtudás</span>
              </div>

              {/* Rating badge */}
              <div className="absolute -top-2 -left-2 bg-white shadow-md rounded-full py-1 px-3 flex items-center gap-1 border border-language-border/30">
                <div className="flex text-amber-400">
                  <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" className="w-4 h-4">
                    <path fillRule="evenodd" d="M10.788 3.21c.448-1.077 1.976-1.077 2.424 0l2.082 5.007 5.404.433c1.164.093 1.636 1.545.749 2.305l-4.117 3.527 1.257 5.273c.271 1.136-.964 2.033-1.96 1.425L12 18.354 7.373 21.18c-.996.608-2.231-.29-1.96-1.425l1.257-5.273-4.117-3.527c-.887-.76-.415-2.212.749-2.305l5.404-.433 2.082-5.006z" clipRule="evenodd" />
                  </svg>
                  <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" className="w-4 h-4">
                    <path fillRule="evenodd" d="M10.788 3.21c.448-1.077 1.976-1.077 2.424 0l2.082 5.007 5.404.433c1.164.093 1.636 1.545.749 2.305l-4.117 3.527 1.257 5.273c.271 1.136-.964 2.033-1.96 1.425L12 18.354 7.373 21.18c-.996.608-2.231-.29-1.96-1.425l1.257-5.273-4.117-3.527c-.887-.76-.415-2.212.749-2.305l5.404-.433 2.082-5.006z" clipRule="evenodd" />
                  </svg>
                  <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" className="w-4 h-4">
                    <path fillRule="evenodd" d="M10.788 3.21c.448-1.077 1.976-1.077 2.424 0l2.082 5.007 5.404.433c1.164.093 1.636 1.545.749 2.305l-4.117 3.527 1.257 5.273c.271 1.136-.964 2.033-1.96 1.425L12 18.354 7.373 21.18c-.996.608-2.231-.29-1.96-1.425l1.257-5.273-4.117-3.527c-.887-.76-.415-2.212.749-2.305l5.404-.433 2.082-5.006z" clipRule="evenodd" />
                  </svg>
                  <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" className="w-4 h-4">
                    <path fillRule="evenodd" d="M10.788 3.21c.448-1.077 1.976-1.077 2.424 0l2.082 5.007 5.404.433c1.164.093 1.636 1.545.749 2.305l-4.117 3.527 1.257 5.273c.271 1.136-.964 2.033-1.96 1.425L12 18.354 7.373 21.18c-.996.608-2.231-.29-1.96-1.425l1.257-5.273-4.117-3.527c-.887-.76-.415-2.212.749-2.305l5.404-.433 2.082-5.006z" clipRule="evenodd" />
                  </svg>
                  <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" className="w-4 h-4">
                    <path fillRule="evenodd" d="M10.788 3.21c.448-1.077 1.976-1.077 2.424 0l2.082 5.007 5.404.433c1.164.093 1.636 1.545.749 2.305l-4.117 3.527 1.257 5.273c.271 1.136-.964 2.033-1.96 1.425L12 18.354 7.373 21.18c-.996.608-2.231-.29-1.96-1.425l1.257-5.273-4.117-3.527c-.887-.76-.415-2.212.749-2.305l5.404-.433 2.082-5.006z" clipRule="evenodd" />
                  </svg>
                </div>
                <span className="text-gray-700 font-medium">4.9</span>
              </div>
            </motion.div>
          </div>

          {/* Additional decoration elements removed */}
        </div>
      </motion.div>

      {/* Features Section */}
      <motion.div
        className="mb-16"
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ delay: 0.3, duration: 0.5 }}
      >
        <div className="text-center mb-8">
          <Badge variant="plain" className="bg-white text-language-secondary border-language-secondary/20 mb-3 py-1.5 px-3 mx-auto shadow-sm">
            <Target className="h-3.5 w-3.5 mr-1.5" /> Tanulási lehetőségek
          </Badge>
          <h2 className="text-3xl font-bold mb-3 bg-gradient-to-r from-language-primary via-language-secondary to-language-accent bg-clip-text text-transparent">Válassz tanulási módszert</h2>
          <p className="text-gray-600 max-w-2xl mx-auto bg-white/70 backdrop-blur-sm py-2 px-4 rounded-full inline-block">Különböző módszerekkel tanulhatsz, hogy megtaláld a számodra leghatékonyabbat</p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 max-w-5xl mx-auto">
          {/* Vocabulary Card */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.4, duration: 0.5 }}
          >
            <Link to="/vocabulary" className="block h-full">
              <Card className="h-full hover:shadow-xl transition-all duration-300 border border-language-border/30 group overflow-hidden bg-white/90 backdrop-blur-sm">
                <div className="absolute inset-x-0 h-1 bg-gradient-to-r from-language-primary to-language-primary/70 transform origin-left transition-all duration-300 group-hover:h-1.5"></div>
                <CardHeader className="pb-2">
                  <div className="flex items-center justify-between">
                    <CardTitle className="text-xl font-bold group-hover:text-language-primary transition-colors duration-300">Szókincs</CardTitle>
                    <div className="p-2 rounded-full bg-language-primary/10 text-language-primary group-hover:bg-language-primary group-hover:text-white transition-all duration-300">
                      <Book className="h-5 w-5" />
                    </div>
                  </div>
                </CardHeader>
                <CardContent>
                  <CardDescription className="mb-4 text-gray-600 group-hover:text-gray-700 transition-colors duration-300">
                    Tanulj új szavakat és kifejezéseket flashcardok segítségével
                  </CardDescription>
                  <div className="flex items-center text-xs text-gray-500">
                    <CheckCircle className="h-3.5 w-3.5 mr-1.5 text-language-primary/70" />
                    <span>240+ szó és kifejezés</span>
                  </div>
                </CardContent>
                <CardFooter className="pt-0">
                  <Button variant="ghost" size="sm" className="text-language-primary ml-auto group/btn">
                    <span className="mr-2 group-hover/btn:mr-3 transition-all">Tovább</span>
                    <ChevronRight className="h-4 w-4 group-hover/btn:translate-x-1 transition-transform" />
                  </Button>
                </CardFooter>
              </Card>
            </Link>
          </motion.div>

          {/* Phrases Card */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.5, duration: 0.5 }}
          >
            <Link to="/phrases" className="block h-full">
              <Card className="h-full hover:shadow-xl transition-all duration-300 border border-language-border/30 group overflow-hidden bg-white/90 backdrop-blur-sm">
                <div className="absolute inset-x-0 h-1 bg-gradient-to-r from-language-secondary to-language-secondary/70 transform origin-left transition-all duration-300 group-hover:h-1.5"></div>
                <CardHeader className="pb-2">
                  <div className="flex items-center justify-between">
                    <CardTitle className="text-xl font-bold group-hover:text-language-secondary transition-colors duration-300">Mondatok</CardTitle>
                    <div className="p-2 rounded-full bg-language-secondary/10 text-language-secondary group-hover:bg-language-secondary group-hover:text-white transition-all duration-300">
                      <MessageSquare className="h-5 w-5" />
                    </div>
                  </div>
                </CardHeader>
                <CardContent>
                  <CardDescription className="mb-4 text-gray-600 group-hover:text-gray-700 transition-colors duration-300">
                    Gyakorolj hasznos mondatokat és kifejezéseket, hallgasd meg a kiejtést
                  </CardDescription>
                  <div className="flex items-center text-xs text-gray-500">
                    <Headphones className="h-3.5 w-3.5 mr-1.5 text-language-secondary/70" />
                    <span>Kiejtés gyakorlás</span>
                  </div>
                </CardContent>
                <CardFooter className="pt-0">
                  <Button variant="ghost" size="sm" className="text-language-secondary ml-auto group/btn">
                    <span className="mr-2 group-hover/btn:mr-3 transition-all">Tovább</span>
                    <ChevronRight className="h-4 w-4 group-hover/btn:translate-x-1 transition-transform" />
                  </Button>
                </CardFooter>
              </Card>
            </Link>
          </motion.div>

          {/* Quiz Card */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.6, duration: 0.5 }}
          >
            <Link to="/quiz" className="block h-full">
              <Card className="h-full hover:shadow-xl transition-all duration-300 border border-language-border/30 group overflow-hidden bg-white/90 backdrop-blur-sm">
                <div className="absolute inset-x-0 h-1 bg-gradient-to-r from-language-accent to-language-accent/70 transform origin-left transition-all duration-300 group-hover:h-1.5"></div>
                <CardHeader className="pb-2">
                  <div className="flex items-center justify-between">
                    <CardTitle className="text-xl font-bold group-hover:text-language-accent transition-colors duration-300">Kvíz</CardTitle>
                    <div className="p-2 rounded-full bg-language-accent/10 text-language-accent group-hover:bg-language-accent group-hover:text-white transition-all duration-300">
                      <GraduationCap className="h-5 w-5" />
                    </div>
                  </div>
                </CardHeader>
                <CardContent>
                  <CardDescription className="mb-4 text-gray-600 group-hover:text-gray-700 transition-colors duration-300">
                    Teszteld a tudásodat interaktív kérdésekkel és feladatokkal
                  </CardDescription>
                  <div className="flex items-center text-xs text-gray-500">
                    <Brain className="h-3.5 w-3.5 mr-1.5 text-language-accent/70" />
                    <span>Tudás ellenőrzés</span>
                  </div>
                </CardContent>
                <CardFooter className="pt-0">
                  <Button variant="ghost" size="sm" className="text-language-accent ml-auto group/btn">
                    <span className="mr-2 group-hover/btn:mr-3 transition-all">Tovább</span>
                    <ChevronRight className="h-4 w-4 group-hover/btn:translate-x-1 transition-transform" />
                  </Button>
                </CardFooter>
              </Card>
            </Link>
          </motion.div>

          {/* Games Card */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.7, duration: 0.5 }}
          >
            <Link to="/games" className="block h-full">
              <Card className="h-full hover:shadow-xl transition-all duration-300 border border-language-border/30 group overflow-hidden bg-white/90 backdrop-blur-sm">
                <div className="absolute inset-x-0 h-1 bg-gradient-to-r from-language-quaternary to-language-quaternary/70 transform origin-left transition-all duration-300 group-hover:h-1.5"></div>
                <CardHeader className="pb-2">
                  <div className="flex items-center justify-between">
                    <CardTitle className="text-xl font-bold group-hover:text-language-quaternary transition-colors duration-300">Játékok</CardTitle>
                    <div className="p-2 rounded-full bg-language-quaternary/10 text-language-quaternary group-hover:bg-language-quaternary group-hover:text-white transition-all duration-300">
                      <Gamepad2 className="h-5 w-5" />
                    </div>
                  </div>
                </CardHeader>
                <CardContent>
                  <CardDescription className="mb-4 text-gray-600 group-hover:text-gray-700 transition-colors duration-300">
                    Játékos feladatokkal gyakorold a hallásértést és írást
                  </CardDescription>
                  <div className="flex items-center text-xs text-gray-500">
                    <Sparkles className="h-3.5 w-3.5 mr-1.5 text-language-quaternary/70" />
                    <span>Szórakoztato tanulás</span>
                  </div>
                </CardContent>
                <CardFooter className="pt-0">
                  <Button variant="ghost" size="sm" className="text-language-quaternary ml-auto group/btn">
                    <span className="mr-2 group-hover/btn:mr-3 transition-all">Tovább</span>
                    <ChevronRight className="h-4 w-4 group-hover/btn:translate-x-1 transition-transform" />
                  </Button>
                </CardFooter>
              </Card>
            </Link>
          </motion.div>
        </div>
      </motion.div>

      {/* Új funkciók szekció */}
      <motion.div
        className="mb-16"
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ delay: 0.5, duration: 0.5 }}
      >
        <div className="text-center mb-8">
          <Badge variant="plain" className="bg-white text-language-primary border-language-primary/20 mb-3 py-1.5 px-3 mx-auto shadow-sm">
            <Sparkles className="h-3.5 w-3.5 mr-1.5" /> Új funkciók
          </Badge>
          <h2 className="text-3xl font-bold mb-3 bg-gradient-to-r from-language-primary via-language-secondary to-language-accent bg-clip-text text-transparent">Új funkciók és fejlesztések</h2>
          <p className="text-gray-600 max-w-2xl mx-auto bg-white/70 backdrop-blur-sm py-2 px-4 rounded-full inline-block">Fedezd fel a legfrissebb fejlesztéseket, amelyek még hatékonyabbá teszik a nyelvtanulást</p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 max-w-5xl mx-auto">
          {/* Pontrendszer kártya */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.6, duration: 0.5 }}
          >
            <Link to="/points" className="block h-full">
              <Card className="h-full hover:shadow-xl transition-all duration-300 border border-language-border/30 group overflow-hidden bg-white/90 backdrop-blur-sm">
                <div className="absolute inset-x-0 h-1 bg-gradient-to-r from-amber-500 to-amber-300 transform origin-left transition-all duration-300 group-hover:h-1.5"></div>
                <CardHeader className="pb-2">
                  <div className="flex items-center justify-between">
                    <CardTitle className="text-xl font-bold group-hover:text-amber-500 transition-colors duration-300">Pontrendszer</CardTitle>
                    <div className="p-2 rounded-full bg-amber-100 text-amber-500 group-hover:bg-amber-500 group-hover:text-white transition-all duration-300">
                      <Coins className="h-5 w-5" />
                    </div>
                  </div>
                </CardHeader>
                <CardContent>
                  <CardDescription className="mb-4 text-gray-600 group-hover:text-gray-700 transition-colors duration-300">
                    Vásárolj pontokat és használd az AI asszisztenst korlátlanól
                  </CardDescription>
                  <div className="flex items-center text-xs text-gray-500">
                    <BadgeCheck className="h-3.5 w-3.5 mr-1.5 text-amber-500" />
                    <span>Rugalmas pontcsomagok</span>
                  </div>
                </CardContent>
                <CardFooter className="pt-0">
                  <Button variant="ghost" size="sm" className="text-amber-500 ml-auto group/btn">
                    <span className="mr-2 group-hover/btn:mr-3 transition-all">Tovább</span>
                    <ChevronRight className="h-4 w-4 group-hover/btn:translate-x-1 transition-transform" />
                  </Button>
                </CardFooter>
              </Card>
            </Link>
          </motion.div>

          {/* Ingyenes próba csomag kártya */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.6, duration: 0.5 }}
          >
            <Link to="/subscription" className="block h-full">
              <Card className="h-full hover:shadow-xl transition-all duration-300 border border-language-border/30 group overflow-hidden bg-white/90 backdrop-blur-sm">
                <div className="absolute inset-x-0 h-1 bg-gradient-to-r from-green-500 to-green-300 transform origin-left transition-all duration-300 group-hover:h-1.5"></div>
                <CardHeader className="pb-2">
                  <div className="flex items-center justify-between">
                    <CardTitle className="text-xl font-bold group-hover:text-green-500 transition-colors duration-300">Ingyenes próba</CardTitle>
                    <div className="p-2 rounded-full bg-green-100 text-green-500 group-hover:bg-green-500 group-hover:text-white transition-all duration-300">
                      <Coins className="h-5 w-5" />
                    </div>
                  </div>
                </CardHeader>
                <CardContent>
                  <CardDescription className="mb-4 text-gray-600 group-hover:text-gray-700 transition-colors duration-300">
                    Ismerkedj meg az alkalmazással 15 ingyenes ponttal
                  </CardDescription>
                  <div className="flex items-center text-xs text-gray-500">
                    <BadgeCheck className="h-3.5 w-3.5 mr-1.5 text-green-500" />
                    <span>Hozzáférés minden funkcióhoz</span>
                  </div>
                </CardContent>
                <CardFooter className="pt-0">
                  <Button variant="ghost" size="sm" className="text-green-500 ml-auto group/btn">
                    <span className="mr-2 group-hover/btn:mr-3 transition-all">Tovább</span>
                    <ChevronRight className="h-4 w-4 group-hover/btn:translate-x-1 transition-transform" />
                  </Button>
                </CardFooter>
              </Card>
            </Link>
          </motion.div>

          {/* Előfizetés kártya */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.7, duration: 0.5 }}
          >
            <Link to="/subscription" className="block h-full">
              <Card className="h-full hover:shadow-xl transition-all duration-300 border border-language-border/30 group overflow-hidden bg-white/90 backdrop-blur-sm">
                <div className="absolute inset-x-0 h-1 bg-gradient-to-r from-purple-500 to-purple-300 transform origin-left transition-all duration-300 group-hover:h-1.5"></div>
                <CardHeader className="pb-2">
                  <div className="flex items-center justify-between">
                    <CardTitle className="text-xl font-bold group-hover:text-purple-500 transition-colors duration-300">Havi előfizetés</CardTitle>
                    <div className="p-2 rounded-full bg-purple-100 text-purple-500 group-hover:bg-purple-500 group-hover:text-white transition-all duration-300">
                      <Crown className="h-5 w-5" />
                    </div>
                  </div>
                </CardHeader>
                <CardContent>
                  <CardDescription className="mb-4 text-gray-600 group-hover:text-gray-700 transition-colors duration-300">
                    500 pont havonta az AI funkciók használatához (2990 Ft/hó)
                  </CardDescription>
                  <div className="flex items-center text-xs text-gray-500">
                    <BadgeCheck className="h-3.5 w-3.5 mr-1.5 text-purple-500" />
                    <span>Bármikor lemondható</span>
                  </div>
                </CardContent>
                <CardFooter className="pt-0">
                  <Button variant="ghost" size="sm" className="text-purple-500 ml-auto group/btn">
                    <span className="mr-2 group-hover/btn:mr-3 transition-all">Tovább</span>
                    <ChevronRight className="h-4 w-4 group-hover/btn:translate-x-1 transition-transform" />
                  </Button>
                </CardFooter>
              </Card>
            </Link>
          </motion.div>

          {/* Tanári csomag kártya */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.8, duration: 0.5 }}
          >
            <Link to="/teacher/subscribe" className="block h-full">
              <Card className="h-full hover:shadow-xl transition-all duration-300 border border-language-border/30 group overflow-hidden bg-white/90 backdrop-blur-sm">
                <div className="absolute inset-x-0 h-1 bg-gradient-to-r from-blue-500 to-blue-300 transform origin-left transition-all duration-300 group-hover:h-1.5"></div>
                <CardHeader className="pb-2">
                  <div className="flex items-center justify-between">
                    <CardTitle className="text-xl font-bold group-hover:text-blue-500 transition-colors duration-300">Tanári csomag</CardTitle>
                    <div className="p-2 rounded-full bg-blue-100 text-blue-500 group-hover:bg-blue-500 group-hover:text-white transition-all duration-300">
                      <Users className="h-5 w-5" />
                    </div>
                  </div>
                  <div className="mt-1">
                    <Badge className="bg-blue-100 text-blue-700">Új</Badge>
                  </div>
                </CardHeader>
                <CardContent>
                  <CardDescription className="mb-4 text-gray-600 group-hover:text-gray-700 transition-colors duration-300">
                    Tanítsd diákjaidat és kövesd haladásukat egyszerűen
                  </CardDescription>
                  <div className="flex items-center text-xs text-gray-500">
                    <BadgeCheck className="h-3.5 w-3.5 mr-1.5 text-blue-500" />
                    <span>Max. 20 diák kezelése</span>
                  </div>
                </CardContent>
                <CardFooter className="pt-0">
                  <Button variant="ghost" size="sm" className="text-blue-500 ml-auto group/btn">
                    <span className="mr-2 group-hover/btn:mr-3 transition-all">Tovább</span>
                    <ChevronRight className="h-4 w-4 group-hover/btn:translate-x-1 transition-transform" />
                  </Button>
                </CardFooter>
              </Card>
            </Link>
          </motion.div>

          {/* Kiejtésgyakorló kártya */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.8, duration: 0.5 }}
          >
            <Link to="/pronunciation" className="block h-full">
              <Card className="h-full hover:shadow-xl transition-all duration-300 border border-language-border/30 group overflow-hidden bg-white/90 backdrop-blur-sm">
                <div className="absolute inset-x-0 h-1 bg-gradient-to-r from-teal-500 to-teal-300 transform origin-left transition-all duration-300 group-hover:h-1.5"></div>
                <CardHeader className="pb-2">
                  <div className="flex items-center justify-between">
                    <CardTitle className="text-xl font-bold group-hover:text-teal-500 transition-colors duration-300">Kiejtésgyakorló</CardTitle>
                    <div className="p-2 rounded-full bg-teal-100 text-teal-500 group-hover:bg-teal-500 group-hover:text-white transition-all duration-300">
                      <Mic className="h-5 w-5" />
                    </div>
                  </div>
                  <div className="mt-1">
                    <Badge className="bg-teal-100 text-teal-700">Új</Badge>
                  </div>
                </CardHeader>
                <CardContent>
                  <CardDescription className="mb-4 text-gray-600 group-hover:text-gray-700 transition-colors duration-300">
                    Gyakorold a kiejtést hangfelismeréssel és azonnali visszajelzéssel
                  </CardDescription>
                  <div className="flex items-center text-xs text-gray-500">
                    <BadgeCheck className="h-3.5 w-3.5 mr-1.5 text-teal-500" />
                    <span>AI-alapú kiejtésértékelés</span>
                  </div>
                </CardContent>
                <CardFooter className="pt-0">
                  <Button variant="ghost" size="sm" className="text-teal-500 ml-auto group/btn">
                    <span className="mr-2 group-hover/btn:mr-3 transition-all">Tovább</span>
                    <ChevronRight className="h-4 w-4 group-hover/btn:translate-x-1 transition-transform" />
                  </Button>
                </CardFooter>
              </Card>
            </Link>
          </motion.div>

          {/* Valós idejű fordítás kártya */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.9, duration: 0.5 }}
          >
            <Link to="/translator" className="block h-full">
              <Card className="h-full hover:shadow-xl transition-all duration-300 border border-language-border/30 group overflow-hidden bg-white/90 backdrop-blur-sm">
                <div className="absolute inset-x-0 h-1 bg-gradient-to-r from-indigo-500 to-indigo-300 transform origin-left transition-all duration-300 group-hover:h-1.5"></div>
                <CardHeader className="pb-2">
                  <div className="flex items-center justify-between">
                    <CardTitle className="text-xl font-bold group-hover:text-indigo-500 transition-colors duration-300">Fordító</CardTitle>
                    <div className="p-2 rounded-full bg-indigo-100 text-indigo-500 group-hover:bg-indigo-500 group-hover:text-white transition-all duration-300">
                      <Globe className="h-5 w-5" />
                    </div>
                  </div>
                  <div className="mt-1">
                    <Badge className="bg-indigo-100 text-indigo-700">Új</Badge>
                  </div>
                </CardHeader>
                <CardContent>
                  <CardDescription className="mb-4 text-gray-600 group-hover:text-gray-700 transition-colors duration-300">
                    Valós idejű fordítás magyar és német nyelv között
                  </CardDescription>
                  <div className="flex items-center text-xs text-gray-500">
                    <BadgeCheck className="h-3.5 w-3.5 mr-1.5 text-indigo-500" />
                    <span>Szöveg, hang és kamera alapú fordítás</span>
                  </div>
                </CardContent>
                <CardFooter className="pt-0">
                  <Button variant="ghost" size="sm" className="text-indigo-500 ml-auto group/btn">
                    <span className="mr-2 group-hover/btn:mr-3 transition-all">Tovább</span>
                    <ChevronRight className="h-4 w-4 group-hover/btn:translate-x-1 transition-transform" />
                  </Button>
                </CardFooter>
              </Card>
            </Link>
          </motion.div>

          {/* Szakmai szókincs kártya */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 1.0, duration: 0.5 }}
          >
            <Link to="/professional" className="block h-full">
              <Card className="h-full hover:shadow-xl transition-all duration-300 border border-language-border/30 group overflow-hidden bg-white/90 backdrop-blur-sm">
                <div className="absolute inset-x-0 h-1 bg-gradient-to-r from-orange-500 to-orange-300 transform origin-left transition-all duration-300 group-hover:h-1.5"></div>
                <CardHeader className="pb-2">
                  <div className="flex items-center justify-between">
                    <CardTitle className="text-xl font-bold group-hover:text-orange-500 transition-colors duration-300">Szakmai szókincs</CardTitle>
                    <div className="p-2 rounded-full bg-orange-100 text-orange-500 group-hover:bg-orange-500 group-hover:text-white transition-all duration-300">
                      <GraduationCap className="h-5 w-5" />
                    </div>
                  </div>
                </CardHeader>
                <CardContent>
                  <CardDescription className="mb-4 text-gray-600 group-hover:text-gray-700 transition-colors duration-300">
                    Szakma-specifikus szókincs különböző területekhez
                  </CardDescription>
                  <div className="flex items-center text-xs text-gray-500">
                    <BadgeCheck className="h-3.5 w-3.5 mr-1.5 text-orange-500" />
                    <span>Egészségügy, vendéglátás, kereskedelem</span>
                  </div>
                </CardContent>
                <CardFooter className="pt-0">
                  <Button variant="ghost" size="sm" className="text-orange-500 ml-auto group/btn">
                    <span className="mr-2 group-hover/btn:mr-3 transition-all">Tovább</span>
                    <ChevronRight className="h-4 w-4 group-hover/btn:translate-x-1 transition-transform" />
                  </Button>
                </CardFooter>
              </Card>
            </Link>
          </motion.div>
        </div>
      </motion.div>

      {/* Tips Section */}
      <motion.div
        className="mb-16"
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ delay: 0.5, duration: 0.5 }}
      >
        <div className="text-center mb-8">
          <Badge variant="plain" className="bg-white text-language-quaternary border-language-quaternary/20 mb-3 py-1.5 px-3 mx-auto shadow-sm">
            <Lightbulb className="h-3.5 w-3.5 mr-1.5" /> Tippek és trükkök
          </Badge>
          <h2 className="text-3xl font-bold mb-3 bg-gradient-to-r from-language-quaternary via-language-tertiary to-language-secondary bg-clip-text text-transparent">Nyelvtanulási stratégiák</h2>
          <p className="text-gray-600 max-w-2xl mx-auto bg-white/70 backdrop-blur-sm py-2 px-4 rounded-full inline-block">Ezekkel a módszerekkel hatékonyabbá teheted a tanulási folyamatot</p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 max-w-5xl mx-auto">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.6, duration: 0.4 }}
          >
            <Card className="bg-white/80 backdrop-blur-sm border border-language-border/20 hover:shadow-lg transition-all duration-300 hover:bg-gradient-to-br hover:from-white hover:to-language-primary/5 h-64">
              <CardContent className="pt-6 flex flex-col h-full">
                <div className="flex justify-center mb-4">
                  <div className="bg-white p-3 rounded-full shadow-md">
                    <Clock className="h-6 w-6 text-language-primary" />
                  </div>
                </div>
                <h3 className="text-lg font-medium text-center mb-2">Rendszeresség</h3>
                <p className="text-gray-600 text-center flex-grow flex items-center justify-center">
                  Napi 15-20 perc gyakorlás hatékonyabb, mint heti egyszer több órát tanulni.
                </p>
              </CardContent>
            </Card>
          </motion.div>

          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.7, duration: 0.4 }}
          >
            <Card className="bg-white/80 backdrop-blur-sm border border-language-border/20 hover:shadow-lg transition-all duration-300 hover:bg-gradient-to-br hover:from-white hover:to-language-secondary/5 h-64">
              <CardContent className="pt-6 flex flex-col h-full">
                <div className="flex justify-center mb-4">
                  <div className="bg-white p-3 rounded-full shadow-md">
                    <MessageSquare className="h-6 w-6 text-language-secondary" />
                  </div>
                </div>
                <h3 className="text-lg font-medium text-center mb-2">Beszélgetés</h3>
                <p className="text-gray-600 text-center flex-grow flex items-center justify-center">
                  Használd az asszisztenst beszélgetési gyakorlatokhoz és kérdésekhez.
                </p>
              </CardContent>
            </Card>
          </motion.div>

          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.8, duration: 0.4 }}
          >
            <Card className="bg-white/80 backdrop-blur-sm border border-language-border/20 hover:shadow-lg transition-all duration-300 hover:bg-gradient-to-br hover:from-white hover:to-language-accent/5 h-64">
              <CardContent className="pt-6 flex flex-col h-full">
                <div className="flex justify-center mb-4">
                  <div className="bg-white p-3 rounded-full shadow-md">
                    <Headphones className="h-6 w-6 text-language-accent" />
                  </div>
                </div>
                <h3 className="text-lg font-medium text-center mb-2">Hallgatás</h3>
                <p className="text-gray-600 text-center flex-grow flex items-center justify-center">
                  Hallgasd meg a kiejtést és próbáld utánozni a helyes hangsúlyozást.
                </p>
              </CardContent>
            </Card>
          </motion.div>

          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.9, duration: 0.4 }}
          >
            <Card className="bg-white/80 backdrop-blur-sm border border-language-border/20 hover:shadow-lg transition-all duration-300 hover:bg-gradient-to-br hover:from-white hover:to-language-quaternary/5 h-64">
              <CardContent className="pt-6 flex flex-col h-full">
                <div className="flex justify-center mb-4">
                  <div className="bg-white p-3 rounded-full shadow-md">
                    <Rocket className="h-6 w-6 text-language-quaternary" />
                  </div>
                </div>
                <h3 className="text-lg font-medium text-center mb-2">Játékos tanulás</h3>
                <p className="text-gray-600 text-center flex-grow flex items-center justify-center">
                  A játékos tanulás növeli a motivációt és segít hosszabb ideig koncentrálni.
                </p>
              </CardContent>
            </Card>
          </motion.div>
        </div>
      </motion.div>

      {/* Carousel Section - Kezdd el most és Közösségi funkciók */}
      <motion.div
        className="mb-16"
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ delay: 0.6, duration: 0.5 }}
      >
        <div className="max-w-4xl mx-auto bg-white/80 backdrop-blur-sm rounded-2xl p-8 md:p-10 shadow-xl border border-language-border/30 relative overflow-hidden">
          <div className="absolute -top-24 -right-24 w-64 h-64 bg-language-primary/10 rounded-full blur-3xl"></div>
          <div className="absolute -bottom-32 -left-32 w-80 h-80 bg-language-accent/10 rounded-full blur-3xl"></div>
          <div className="absolute inset-0 bg-gradient-to-br from-language-primary/5 via-language-secondary/5 to-language-accent/5 opacity-70"></div>

          {/* Carousel Navigation */}
          <div className="absolute top-4 right-4 flex space-x-2 z-10">
            <button
              onClick={prevCarouselItem}
              className="p-1.5 rounded-full bg-white/80 hover:bg-white text-gray-500 hover:text-gray-700 transition-colors shadow-sm"
              aria-label="Előző"
            >
              <ChevronLeft className="h-4 w-4" />
            </button>
            <button
              onClick={nextCarouselItem}
              className="p-1.5 rounded-full bg-white/80 hover:bg-white text-gray-500 hover:text-gray-700 transition-colors shadow-sm"
              aria-label="Következő"
            >
              <ChevronRightIcon className="h-4 w-4" />
            </button>
          </div>

          {/* Carousel Indicators */}
          <div className="absolute bottom-4 left-1/2 transform -translate-x-1/2 flex space-x-1.5 z-10">
            {carouselItems.map((_, index) => (
              <button
                key={index}
                onClick={() => setCarouselIndex(index)}
                className={`h-2 rounded-full transition-all ${index === carouselIndex ? 'bg-language-primary w-8' : 'bg-gray-300 w-2 hover:bg-gray-400'}`}
                aria-label={`Slide ${index + 1}`}
              />
            ))}
          </div>

          <AnimatePresence mode="wait">
            {carouselIndex === 0 && (
              <motion.div
                key="action"
                className="relative"
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                exit={{ opacity: 0 }}
                transition={{ duration: 0.5 }}
              >
                <div className="text-center">
                  <Badge variant="plain" className="bg-white text-language-primary border-language-primary/20 mb-4 py-1.5 px-3 mx-auto">
                    <Zap className="h-3.5 w-3.5 mr-1.5" /> Kezdd el most
                  </Badge>

                  <h2 className="text-3xl md:text-4xl font-bold mb-4">Készen állsz a tanulásra?</h2>

                  <p className="text-lg text-gray-600 mb-8 max-w-2xl mx-auto">
                    Válassz egy tanulási módszert, fedezd fel az új funkciókat, és kezdd el fejleszteni a német nyelvtudásod már ma!
                  </p>

                  <div className="flex flex-wrap gap-4 justify-center">
                    <Link to="/vocabulary">
                      <Button className="bg-language-primary hover:bg-language-primary/95 text-white rounded-full px-6 py-6">
                        <Book className="mr-2 h-5 w-5" />
                        Kezdj tanulni most
                      </Button>
                    </Link>

                    <Link to="/assistant">
                      <Button variant="outline" className="rounded-full px-6 py-6 border-language-border bg-white/80">
                        <Bot className="mr-2 h-5 w-5" />
                        Beszélgess az asszisztenssel
                      </Button>
                    </Link>

                    <Link to="/subscription">
                      <Button variant="outline" className="rounded-full px-6 py-6 border-language-border bg-white/80">
                        <Crown className="mr-2 h-5 w-5 text-purple-500" />
                        Előfizetés
                      </Button>
                    </Link>
                  </div>

                  <div className="mt-8 flex items-center justify-center text-sm text-gray-500">
                    <Award className="h-4 w-4 mr-2 text-language-accent" />
                    <span>Már több mint 1000 felhasználó tanul sikeresen a platformunkon</span>
                  </div>
                </div>
              </motion.div>
            )}

            {carouselIndex === 1 && (
              <motion.div
                key="community"
                className="relative"
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                exit={{ opacity: 0 }}
                transition={{ duration: 0.5 }}
              >
                <div className="flex flex-col md:flex-row gap-8 items-center">
                  <div className="md:w-1/2">
                    <Badge variant="plain" className="bg-white text-blue-500 border-blue-200 mb-3 py-1.5 px-3 shadow-sm">
                      <Users className="h-3.5 w-3.5 mr-1.5" /> Közösségi funkciók
                    </Badge>
                    <h2 className="text-2xl md:text-3xl font-bold mb-4">Hívd meg barátaidat és tanulj együtt!</h2>
                    <p className="text-gray-600 mb-6">
                      Az új meghívó rendszerünkkel meghívhatod barátaidat, hogy csatlakozzanak a nyelvtanuló közösségünkhöz. Küldj meghívókat e-mailben, és kövesd nyomon, kik fogadták el a meghívásodat.
                    </p>
                    <div className="flex flex-wrap gap-3">
                      <Link to="/invitations">
                        <Button className="bg-blue-500 hover:bg-blue-600 text-white rounded-full px-6">
                          <Users className="mr-2 h-4 w-4" />
                          Meghívók kezelése
                        </Button>
                      </Link>
                    </div>
                  </div>
                  <div className="md:w-1/2 flex justify-center">
                    <div className="relative bg-white p-6 rounded-xl shadow-lg border border-language-border/50 max-w-xs w-full">
                      <div className="space-y-4">
                        <div className="flex items-center gap-3 border-b pb-3">
                          <div className="bg-blue-100 p-2 rounded-full">
                            <Users className="h-5 w-5 text-blue-500" />
                          </div>
                          <div>
                            <h3 className="font-medium">Meghívó rendszer</h3>
                            <p className="text-sm text-gray-500">Küldj meghívókat barátaidnak</p>
                          </div>
                        </div>
                        <div className="space-y-2">
                          <div className="flex items-center gap-2">
                            <CheckCircle className="h-4 w-4 text-green-500" />
                            <span className="text-sm">E-mail meghívók küldése</span>
                          </div>
                          <div className="flex items-center gap-2">
                            <CheckCircle className="h-4 w-4 text-green-500" />
                            <span className="text-sm">Meghívók nyomon követése</span>
                          </div>
                          <div className="flex items-center gap-2">
                            <CheckCircle className="h-4 w-4 text-green-500" />
                            <span className="text-sm">Közösségépítés</span>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </motion.div>
            )}

            {carouselIndex === 2 && (
              <motion.div
                key="games"
                className="relative"
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                exit={{ opacity: 0 }}
                transition={{ duration: 0.5 }}
              >
                <div className="flex flex-col md:flex-row gap-8 items-center">
                  <div className="md:w-1/2">
                    <Badge variant="plain" className="bg-white text-green-500 border-green-200 mb-3 py-1.5 px-3 shadow-sm">
                      <Gamepad2 className="h-3.5 w-3.5 mr-1.5" /> Játékos tanulás
                    </Badge>
                    <h2 className="text-2xl md:text-3xl font-bold mb-4">Fedezd fel játékainkat!</h2>
                    <p className="text-gray-600 mb-6">
                      A játékos tanulás segít hatékonyan és szórakoztatóan elsajátítani a nyelvet. Próbáld ki változatos játékainkat: szópárosítás, mondatrendezés, kvízek, anagrammák és még sok más!
                    </p>
                    <div className="flex flex-wrap gap-3">
                      <Link to="/games">
                        <Button className="bg-green-500 hover:bg-green-600 text-white rounded-full px-6">
                          <Gamepad2 className="mr-2 h-4 w-4" />
                          Játékok kipróbálása
                        </Button>
                      </Link>
                    </div>
                  </div>
                  <div className="md:w-1/2 flex justify-center">
                    <div className="grid grid-cols-2 gap-3 max-w-xs w-full">
                      <div className="bg-white p-4 rounded-xl shadow-md border border-language-border/30 flex flex-col items-center hover:shadow-lg transition-shadow">
                        <div className="p-2 rounded-full bg-language-primary/10 text-language-primary mb-2">
                          <Book className="h-5 w-5" />
                        </div>
                        <p className="text-sm font-medium">Szópárosítás</p>
                      </div>
                      <div className="bg-white p-4 rounded-xl shadow-md border border-language-border/30 flex flex-col items-center hover:shadow-lg transition-shadow">
                        <div className="p-2 rounded-full bg-language-secondary/10 text-language-secondary mb-2">
                          <MessageSquare className="h-5 w-5" />
                        </div>
                        <p className="text-sm font-medium">Mondatrendezés</p>
                      </div>
                      <div className="bg-white p-4 rounded-xl shadow-md border border-language-border/30 flex flex-col items-center hover:shadow-lg transition-shadow">
                        <div className="p-2 rounded-full bg-language-accent/10 text-language-accent mb-2">
                          <Headphones className="h-5 w-5" />
                        </div>
                        <p className="text-sm font-medium">Hallásértés</p>
                      </div>
                      <div className="bg-white p-4 rounded-xl shadow-md border border-language-border/30 flex flex-col items-center hover:shadow-lg transition-shadow">
                        <div className="p-2 rounded-full bg-language-quaternary/10 text-language-quaternary mb-2">
                          <Puzzle className="h-5 w-5" />
                        </div>
                        <p className="text-sm font-medium">Anagramma</p>
                      </div>
                    </div>
                  </div>
                </div>
              </motion.div>
            )}

            {carouselIndex === 3 && (
              <motion.div
                key="study"
                className="relative"
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                exit={{ opacity: 0 }}
                transition={{ duration: 0.5 }}
              >
                <div className="flex flex-col md:flex-row gap-8 items-center">
                  <div className="md:w-1/2">
                    <Badge variant="plain" className="bg-white text-purple-500 border-purple-200 mb-3 py-1.5 px-3 shadow-sm">
                      <Brain className="h-3.5 w-3.5 mr-1.5" /> Nyelvtani gyakorlatok
                    </Badge>
                    <h2 className="text-2xl md:text-3xl font-bold mb-4">Alapozd meg a nyelvtudásodat!</h2>
                    <p className="text-gray-600 mb-6">
                      A kiejtés és a helyes nyelvtani szerkezetek elengedhetetlenek a folyékony nyelvtudáshoz. Gyakorolj AI asszisztensünkkel és használd az új kiejtésgyakorló modult a tökéletes kiejtésért.
                    </p>
                    <div className="flex flex-wrap gap-3">
                      <Link to="/pronunciation">
                        <Button className="bg-purple-500 hover:bg-purple-600 text-white rounded-full px-6">
                          <Mic className="mr-2 h-4 w-4" />
                          Kiejtésgyakorló
                        </Button>
                      </Link>
                      <Link to="/phrases">
                        <Button variant="outline" className="border-purple-200 hover:bg-purple-50 text-purple-500 rounded-full px-6">
                          <VolumeIcon className="mr-2 h-4 w-4" />
                          Mondatok
                        </Button>
                      </Link>
                      <Link to="/grammar/a1">
                        <Button variant="outline" className="border-purple-200 hover:bg-purple-50 text-purple-500 rounded-full px-6">
                          <GraduationCap className="mr-2 h-4 w-4" />
                          A1 nyelvtan
                        </Button>
                      </Link>
                    </div>
                  </div>
                  <div className="md:w-1/2 flex justify-center">
                    <div className="bg-white p-6 rounded-xl shadow-lg border border-language-border/50 max-w-xs w-full">
                      <div className="space-y-5">
                        <h3 className="font-medium text-center mb-2 text-gray-800">Fejlődési út</h3>
                        <div className="space-y-2">
                          <div className="flex justify-between items-center">
                            <div className="flex items-center">
                              <div className="w-7 h-7 rounded-full bg-green-100 flex items-center justify-center text-green-600 border border-green-200 mr-3">
                                <CheckCircle className="h-4 w-4" />
                              </div>
                              <span className="text-gray-700">Alapszavak (A1)</span>
                            </div>
                            <Badge className="bg-green-100 text-green-700">Befejezve</Badge>
                          </div>
                          <div className="flex justify-between items-center">
                            <div className="flex items-center">
                              <div className="w-7 h-7 rounded-full bg-blue-100 flex items-center justify-center text-blue-600 border border-blue-200 mr-3">
                                <MessageSquare className="h-4 w-4" />
                              </div>
                              <span className="text-gray-700">Mondatszerkezetek</span>
                            </div>
                            <Badge className="bg-blue-100 text-blue-700">75%</Badge>
                          </div>
                          <div className="flex justify-between items-center">
                            <div className="flex items-center">
                              <div className="w-7 h-7 rounded-full bg-teal-100 flex items-center justify-center text-teal-600 border border-teal-200 mr-3">
                                <Mic className="h-4 w-4" />
                              </div>
                              <span className="text-gray-700">Kiejtésgyakorló</span>
                            </div>
                            <Badge variant="teal">Új</Badge>
                          </div>
                          <div className="flex justify-between items-center">
                            <div className="flex items-center">
                              <div className="w-7 h-7 rounded-full bg-gray-100 flex items-center justify-center text-gray-600 border border-gray-200 mr-3">
                                <GraduationCap className="h-4 w-4" />
                              </div>
                              <span className="text-gray-700">Nyelvtani szabályok</span>
                            </div>
                            <Badge className="bg-gray-100 text-gray-700">25%</Badge>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </motion.div>
            )}
          </AnimatePresence>
        </div>
      </motion.div>

      {/* Footer */}
        <HomeFooter />
      </div>
    </>
  );
};

export default Home;
