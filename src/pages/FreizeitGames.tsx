import React, { useState } from 'react';
import { Button } from "../components/ui/button";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "../components/ui/card";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "../components/ui/tabs";

// Importáljuk a játék komponenseket
import TimeExercise from "../components/games/TimeExercise";
import DialogPractice from "../components/games/DialogPractice";
import SeparableVerbGame from "../components/games/SeparableVerbGame";
import WeeklyPlanner from "../components/games/WeeklyPlanner";
import TimeDomino from "../components/games/TimeDomino";

const FreizeitGames: React.FC = () => {
  const [activeTab, setActiveTab] = useState<string>('intro');

  const games = [
    {
      id: 'time',
      title: 'Időpont gyakorló',
      description: '<PERSON>ya<PERSON>old a német időkifejezéseket interaktív óra segítségével.',
      component: <TimeExercise />
    },
    {
      id: 'dialog',
      title: 'Beszélgetés gyakorlás',
      description: 'Gyakorold a német beszélgetéseket különböző szituációkban.',
      component: <DialogPractice />
    },
    {
      id: 'separable',
      title: 'Elváló igekötős igék',
      description: 'Játékos gyakorlatok az elváló igekötős igék elsajátításához.',
      component: <SeparableVerbGame />
    },
    {
      id: 'planner',
      title: 'Heti tervező',
      description: 'Készíts heti tervet németül és gyakorold az időpontokkal kapcsolatos kifejezéseket.',
      component: <WeeklyPlanner />
    },
    {
      id: 'domino',
      title: 'Idő-dominó',
      description: 'Párosítsd az időpontokat a megfelelő tevékenységekkel dominó játék formájában.',
      component: <TimeDomino />
    }
  ];

  return (
    <div className="container mx-auto py-8">
      <h1 className="text-3xl font-bold mb-6">Szabadidő és napi rutin - Játékos gyakorlatok</h1>
      
      <Tabs defaultValue="intro" value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="grid grid-cols-6">
          <TabsTrigger value="intro">Bevezető</TabsTrigger>
          {games.map(game => (
            <TabsTrigger key={game.id} value={game.id}>
              {game.title}
            </TabsTrigger>
          ))}
        </TabsList>
        
        <TabsContent value="intro" className="mt-6">
          <Card>
            <CardHeader>
              <CardTitle>Interaktív gyakorlatok a "Freizeit" témakörhöz</CardTitle>
              <CardDescription>
                Ezek a játékos gyakorlatok segítenek neked elsajátítani a német nyelvben használt időkifejezéseket, napi rutint leíró szófordulatokat és az ezekhez kapcsolódó nyelvtani szerkezeteket.
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {games.map(game => (
                  <Card key={game.id} className="flex flex-col">
                    <CardHeader>
                      <CardTitle>{game.title}</CardTitle>
                      <CardDescription>{game.description}</CardDescription>
                    </CardHeader>
                    <CardFooter className="mt-auto">
                      <Button onClick={() => setActiveTab(game.id)}>
                        Játék indítása
                      </Button>
                    </CardFooter>
                  </Card>
                ))}
              </div>
            </CardContent>
            <CardFooter className="flex justify-between">
              <p className="text-sm text-gray-500">
                Válassz egy játékot a fentiek közül a gyakorláshoz!
              </p>
            </CardFooter>
          </Card>
        </TabsContent>
        
        {games.map(game => (
          <TabsContent key={game.id} value={game.id} className="mt-6">
            {game.component}
          </TabsContent>
        ))}
      </Tabs>
    </div>
  );
};

export default FreizeitGames;