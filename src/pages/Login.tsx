import React, { useState } from 'react';
import { useNavigate, Link, useLocation } from 'react-router-dom';
import { useAuth } from '@/contexts/AuthContext';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Label } from '@/components/ui/label';
import { toast } from 'sonner';
import { motion } from 'framer-motion';
import { Mail, Lock } from 'lucide-react';

const Login: React.FC = () => {
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [isLoading, setIsLoading] = useState(false);

  const { login } = useAuth();
  const navigate = useNavigate();
  const location = useLocation();

  // <PERSON>nan jöttünk
  const from = location.state?.from?.pathname || '/';

  // Bejelentkezés kezelése
  const handleLogin = async (e: React.FormEvent) => {
    e.preventDefault();

    // Validáció
    if (!email || !password) {
      toast.error('Kérjük, töltsd ki az összes mezőt!');
      return;
    }

    try {
      setIsLoading(true);

      // Bejelentkezés
      await login(email, password);

      // Átirányítás
      navigate(from, { replace: true });
    } catch (error) {
      console.error('Bejelentkezési hiba:', error);
      toast.error('Hibás email cím vagy jelszó. Kérjük, próbáld újra!');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="container mx-auto px-4 py-8 mb-20 md:mb-0 md:pt-24">
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
        className="max-w-md mx-auto"
      >
        <Card>
          <CardHeader>
            <CardTitle className="text-2xl font-bold text-center">Bejelentkezés</CardTitle>
            <CardDescription className="text-center">
              Jelentkezz be a Magyar-German Nyelvtanuló fiókodba
            </CardDescription>
          </CardHeader>
          <CardContent>
            <form onSubmit={handleLogin} className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="email">Email cím</Label>
                <div className="relative">
                  <Mail className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                  <Input
                    id="email"
                    type="email"
                    placeholder="email@példa.hu"
                    value={email}
                    onChange={(e) => setEmail(e.target.value)}
                    className="pl-10"
                    required
                  />
                </div>
              </div>

              <div className="space-y-2">
                <div className="flex items-center justify-between">
                  <Label htmlFor="password">Jelszó</Label>
                  <Link to="/forgot-password" className="text-sm text-language-primary hover:underline">
                    Elfelejtetted?
                  </Link>
                </div>
                <div className="relative">
                  <Lock className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                  <Input
                    id="password"
                    type="password"
                    placeholder="********"
                    value={password}
                    onChange={(e) => setPassword(e.target.value)}
                    className="pl-10"
                    required
                  />
                </div>
              </div>

              <Button type="submit" className="w-full" disabled={isLoading}>
                {isLoading ? 'Bejelentkezés...' : 'Bejelentkezés'}
              </Button>
            </form>
          </CardContent>
          <CardFooter className="flex flex-col space-y-2">
            <div className="text-sm text-center text-gray-500">
              Még nincs fiókod?{' '}
              <Link to="/register" className="text-language-primary hover:underline">
                Regisztráció
              </Link>
            </div>
            <div className="text-sm text-center text-gray-500 mt-2">
              <Link to="/forgot-password" className="text-language-primary hover:underline">
                Elfelejtetted a jelszavad?
              </Link>
            </div>
          </CardFooter>
        </Card>
      </motion.div>
    </div>
  );
};

export default Login;
