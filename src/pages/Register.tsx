import React, { useState, useEffect } from 'react';
import { useNavigate, Link, useSearchParams } from 'react-router-dom';
import { useAuth } from '@/contexts/AuthContext';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Label } from '@/components/ui/label';
import { toast } from 'sonner';
import { motion } from 'framer-motion';
import { User, Lock, Mail, CreditCard, CheckCircle, AlertCircle } from 'lucide-react';
import { invitationService } from '@/services/apiService';

const Register: React.FC = () => {
  const [name, setName] = useState('');
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [step, setStep] = useState(1); // 1: Regisztráció, 2: <PERSON>zetés, 3: Sikeres
  const [invitationToken, setInvitationToken] = useState<string | null>(null);
  const [invitationData, setInvitationData] = useState<{ email: string; inviter: string } | null>(null);
  const [isVerifyingInvitation, setIsVerifyingInvitation] = useState(false);
  const [invitationError, setInvitationError] = useState<string | null>(null);

  const { register, createSubscription } = useAuth();
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();

  // Meghívó token ellenőrzése
  useEffect(() => {
    const token = searchParams.get('token');
    if (token) {
      setInvitationToken(token);
      verifyInvitationToken(token);
    }
  }, [searchParams]);

  // Meghívó token ellenőrzése
  const verifyInvitationToken = async (token: string) => {
    try {
      setIsVerifyingInvitation(true);
      setInvitationError(null);

      const response = await invitationService.verifyInvitation(token);
      setInvitationData(response.data);

      // Ha van email a meghívóban, beállítjuk
      if (response.data.email) {
        setEmail(response.data.email);
      }
    } catch (error: any) {
      console.error('Hiba a meghívó ellenőrzésekor:', error);
      setInvitationError(error.response?.data?.message || 'A meghívó érvénytelen vagy lejárt');
      setInvitationToken(null);
    } finally {
      setIsVerifyingInvitation(false);
    }
  };

  // Regisztráció kezelése
  const handleRegister = async (e: React.FormEvent) => {
    e.preventDefault();

    // Validáció
    if (!name || !email || !password || !confirmPassword) {
      toast.error('Kérjük, töltsd ki az összes mezőt!');
      return;
    }

    if (password !== confirmPassword) {
      toast.error('A jelszavak nem egyeznek!');
      return;
    }

    // Ha van meghívó és az email nem egyezik a meghívóban szereplővel
    if (invitationData && invitationData.email && invitationData.email !== email) {
      toast.error(`A meghívó a következő email címre szól: ${invitationData.email}`);
      return;
    }

    try {
      setIsLoading(true);

      // Regisztráció a meghívó tokennel együtt
      await register(email, password, name, invitationToken || undefined);

      // Sikeres regisztráció után a főoldalra irányítunk
      toast.success('Sikeres regisztráció!');
      navigate('/');
    } catch (error) {
      console.error('Regisztrációs hiba:', error);
      toast.error('Hiba történt a regisztráció során. Kérjük, próbáld újra!');
    } finally {
      setIsLoading(false);
    }
  };

  // Előfizetés kezelése
  const handleSubscribe = async () => {
    try {
      setIsLoading(true);

      // Előfizetés létrehozása - ez most már a valódi API-t használja
      const checkoutUrl = await createSubscription();

      if (checkoutUrl) {
        // Stripe Checkout oldal megnyitása ugyanabban az ablakban
        window.location.href = checkoutUrl;
        // Ne állítsuk be a step-et 3-ra, mert az átirányítás előtt végrehajtódna
        // A sikeres fizetés után a backend visszairányít a sikeres oldalra
      } else {
        throw new Error('Nem sikerült létrehozni a fizetési munkamenetet');
      }
    } catch (error) {
      console.error('Előfizetési hiba:', error);
      toast.error('Hiba történt az előfizetés során. Kérjük, próbáld újra!');
      setIsLoading(false);
    }
  };

  return (
    <div className="container mx-auto px-4 py-8 mb-20 md:mb-0 md:pt-24">
      <div className="max-w-md mx-auto">
        {step === 1 && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
          >
            <Card>
              <CardHeader>
                <CardTitle className="text-2xl font-bold text-center">Regisztráció</CardTitle>
                <CardDescription className="text-center">
                  {invitationData ? (
                    <>
                      <span className="font-medium">{invitationData.inviter}</span> meghívott téged a Magyar-German Nyelvtanuló alkalmazásba
                    </>
                  ) : (
                    <>Hozz létre egy fiókot a Magyar-German Nyelvtanuló használatához</>
                  )}
                </CardDescription>
              </CardHeader>
              <CardContent>
                {isVerifyingInvitation ? (
                  <div className="flex justify-center items-center py-8">
                    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-language-primary"></div>
                    <span className="ml-2">Meghívó ellenőrzése...</span>
                  </div>
                ) : invitationError ? (
                  <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-md mb-4 flex items-start">
                    <AlertCircle className="h-5 w-5 mr-2 mt-0.5" />
                    <div>
                      <p className="font-medium">Hiba a meghívóval</p>
                      <p className="text-sm">{invitationError}</p>
                    </div>
                  </div>
                ) : invitationData ? (
                  <div className="bg-green-50 border border-green-200 text-green-700 px-4 py-3 rounded-md mb-4 flex items-start">
                    <CheckCircle className="h-5 w-5 mr-2 mt-0.5" />
                    <div>
                      <p className="font-medium">Érvényes meghívó</p>
                      <p className="text-sm">A meghívó a következő email címre szól: <strong>{invitationData.email}</strong></p>
                    </div>
                  </div>
                ) : null}

                <form onSubmit={handleRegister} className="space-y-4">
                  <div className="space-y-2">
                    <Label htmlFor="name">Név</Label>
                    <div className="relative">
                      <User className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                      <Input
                        id="name"
                        type="text"
                        placeholder="Teljes neved"
                        value={name}
                        onChange={(e) => setName(e.target.value)}
                        className="pl-10"
                        required
                      />
                    </div>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="email">Email cím</Label>
                    <div className="relative">
                      <Mail className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                      <Input
                        id="email"
                        type="email"
                        placeholder="email@példa.hu"
                        value={email}
                        onChange={(e) => setEmail(e.target.value)}
                        className="pl-10"
                        required
                        disabled={!!invitationData && !!invitationData.email}
                      />
                    </div>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="password">Jelszó</Label>
                    <div className="relative">
                      <Lock className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                      <Input
                        id="password"
                        type="password"
                        placeholder="********"
                        value={password}
                        onChange={(e) => setPassword(e.target.value)}
                        className="pl-10"
                        required
                      />
                    </div>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="confirmPassword">Jelszó megerősítése</Label>
                    <div className="relative">
                      <Lock className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                      <Input
                        id="confirmPassword"
                        type="password"
                        placeholder="********"
                        value={confirmPassword}
                        onChange={(e) => setConfirmPassword(e.target.value)}
                        className="pl-10"
                        required
                      />
                    </div>
                  </div>

                  <Button type="submit" className="w-full" disabled={isLoading}>
                    {isLoading ? 'Regisztráció...' : invitationToken ? 'Regisztráció meghívóval' : 'Regisztráció és előfizetés'}
                  </Button>
                </form>
              </CardContent>
              <CardFooter className="flex flex-col space-y-2">
                <div className="text-sm text-center text-gray-500">
                  Már van fiókod?{' '}
                  <Link to="/login" className="text-language-primary hover:underline">
                    Bejelentkezés
                  </Link>
                </div>
              </CardFooter>
            </Card>
          </motion.div>
        )}

        {step === 2 && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
          >
            <Card>
              <CardHeader>
                <CardTitle className="text-2xl font-bold text-center">Előfizetés</CardTitle>
                <CardDescription className="text-center">
                  Fizess elő a Magyar-German Nyelvtanuló teljes hozzáféréséhez
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="bg-gray-50 p-4 rounded-lg border">
                  <h3 className="font-medium text-lg mb-2">Havi előfizetés</h3>
                  <p className="text-gray-600 mb-2">Teljes hozzáférés minden funkcióhoz</p>
                  <div className="text-2xl font-bold mb-2">2990 Ft / hó</div>
                  <ul className="space-y-2 mb-4">
                    <li className="flex items-center">
                      <CheckCircle className="h-4 w-4 text-green-500 mr-2" />
                      <span>Korlátlan hozzáférés az összes szóhoz és kifejezéshez</span>
                    </li>
                    <li className="flex items-center">
                      <CheckCircle className="h-4 w-4 text-green-500 mr-2" />
                      <span>Minden játék és kvíz használata</span>
                    </li>
                    <li className="flex items-center">
                      <CheckCircle className="h-4 w-4 text-green-500 mr-2" />
                      <span>Korlátlan AI asszisztens használat</span>
                    </li>
                    <li className="flex items-center">
                      <CheckCircle className="h-4 w-4 text-green-500 mr-2" />
                      <span>Bármikor lemondható</span>
                    </li>
                  </ul>
                </div>

                <Button
                  onClick={handleSubscribe}
                  className="w-full bg-language-primary hover:bg-language-primary/90"
                  disabled={isLoading}
                >
                  <CreditCard className="h-4 w-4 mr-2" />
                  {isLoading ? 'Feldolgozás...' : 'Előfizetés most'}
                </Button>
              </CardContent>
              <CardFooter className="flex justify-center">
                <p className="text-xs text-gray-500 text-center">
                  Az előfizetéssel elfogadod a szolgáltatási feltételeinket és adatvédelmi irányelveinket.
                </p>
              </CardFooter>
            </Card>
          </motion.div>
        )}

        {step === 3 && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
          >
            <Card>
              <CardHeader>
                <div className="flex justify-center mb-4">
                  <div className="rounded-full bg-green-100 p-3">
                    <CheckCircle className="h-8 w-8 text-green-600" />
                  </div>
                </div>
                <CardTitle className="text-2xl font-bold text-center">Sikeres előfizetés!</CardTitle>
                <CardDescription className="text-center">
                  Köszönjük, hogy előfizettél a Magyar-German Nyelvtanulóra
                </CardDescription>
              </CardHeader>
              <CardContent className="text-center">
                <p className="mb-4">
                  Most már hozzáférsz az összes prémium funkcióhoz. Jó tanulást kívánunk!
                </p>
                <p className="text-sm text-gray-500">
                  Átirányítunk a főoldalra...
                </p>
              </CardContent>
            </Card>
          </motion.div>
        )}
      </div>
    </div>
  );
};

export default Register;
