import React, { useState } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from '../components/ui/tabs';
import BestimmterArtikelGame from '../components/games/BestimmterArtikelGame';
import DeutschPluralGame from '../components/games/DeutschPluralGame';
import ArtikelCaseExercise from '../components/games/ArtikelAkkusativDativGame';
import PersonalPronomenGame from '../components/games/PersonalPronomenGame';
import PersonalPronomenMixedGame from '../components/games/PersonalPronomenMixedGame';
import PossessivArtikelGame from '../components/games/PossessivArtikelGame';
import Navigation from '../components/Navigation';
import { cn } from '../lib/utils';

// Főkategóriák típus definíciója
type MainCategory = 'articles' | 'pronouns' | 'nouns' | 'verbs' | 'adjectives';

// Interface a gyakorlat definíciójához
interface Exercise {
  id: string;
  name: string;
  component: React.ComponentType<any>;
  description?: string;
}

// Interface a főkategória definíciójához
interface CategoryGroup {
  label: string;
  description?: string;
  icon?: React.ReactNode;
  exercises: Exercise[];
}

// Összes kategória és gyakorlat definíciója
const categoryGroups: Record<MainCategory, CategoryGroup> = {
  articles: {
    label: 'Névelők',
    description: 'Határozott és határozatlan névelők, esetek',
    exercises: [
      { id: 'bestimmter-artikel', name: 'Határozott névelő', component: BestimmterArtikelGame },
      { id: 'artikel-kasus', name: 'Névelők esetei', component: ArtikelCaseExercise },
    ],
  },
  pronouns: {
    label: 'Névmások',
    description: 'Személyes, birtokos és egyéb névmások',
    exercises: [
      { id: 'personal-pronomen', name: 'Személyes névmások', component: PersonalPronomenGame },
      { id: 'personal-pronomen-mixed', name: 'Névmások vegyes', component: PersonalPronomenMixedGame },
      { id: 'possessiv-artikel', name: 'Birtokos névmások', component: PossessivArtikelGame },
    ],
  },
  nouns: {
    label: 'Főnevek',
    description: 'Főnevek többesszáma és egyéb tulajdonságai',
    exercises: [
      { id: 'deutsch-plural', name: 'Főnevek többesszáma', component: DeutschPluralGame },
    ],
  },
  verbs: {
    label: 'Igék',
    description: 'Igeragozás és igeidők',
    exercises: [],
  },
  adjectives: {
    label: 'Melléknevek',
    description: 'Melléknévragozás és fokozás',
    exercises: [],
  },
};

// Csak azokat a kategóriákat mutatjuk, ahol van legalább egy gyakorlat
const availableCategories = Object.entries(categoryGroups)
  .filter(([_, { exercises }]) => exercises.length > 0)
  .map(([key]) => key as MainCategory);

// Induláskor az első elérhető kategória első gyakorlata az aktív
const initialMainCategory = availableCategories[0];
const initialExerciseId = categoryGroups[initialMainCategory].exercises[0].id;

const GrammarExercisesA1: React.FC = () => {
  // A főkategória és a gyakorlat aktív állapotának tárolása
  const [mainCategory, setMainCategory] = useState<MainCategory>(initialMainCategory);
  const [activeExerciseId, setActiveExerciseId] = useState(initialExerciseId);

  // Főkategória váltása
  const handleMainCategoryChange = (category: MainCategory) => {
    setMainCategory(category);
    
    // Ha van gyakorlat az új kategóriában, akkor az első lesz aktív
    if (categoryGroups[category].exercises.length > 0) {
      setActiveExerciseId(categoryGroups[category].exercises[0].id);
    } else {
      setActiveExerciseId('');
    }
  };

  // Gyakorlat váltása
  const handleExerciseChange = (exerciseId: string) => {
    setActiveExerciseId(exerciseId);
  };

  // Az aktív gyakorlat komponensének meghatározása
  const activeExercise = Object.values(categoryGroups)
    .flatMap(group => group.exercises)
    .find(exercise => exercise.id === activeExerciseId);

  const ActiveExerciseComponent = activeExercise?.component || null;

  return (
    <div className="min-h-screen bg-gray-50">
      <Navigation />
      
      <div className="container mx-auto py-8 px-4 max-w-4xl">
        <h1 className="text-3xl font-bold mb-8 text-center">Német nyelvtani gyakorlatok (A1 szint)</h1>
        
        {/* Főkategória tabok - a képen látott stílusban */}
        <div className="bg-gray-100 p-1 rounded-md mb-8 max-w-xl mx-auto">
          <div className="flex">
            {availableCategories.map((key) => (
              <button
                key={key}
                onClick={() => handleMainCategoryChange(key)}
                className={cn(
                  "flex-1 py-2.5 px-4 text-center text-sm font-medium rounded-md transition-all",
                  mainCategory === key
                    ? "bg-white text-black border border-gray-200 shadow-sm" 
                    : "bg-transparent text-gray-600 hover:bg-gray-200/50"
                )}
              >
                {categoryGroups[key].label}
              </button>
            ))}
          </div>
        </div>
        
        {/* Alkategória/gyakorlat tabok */}
        <div className="bg-white p-6 rounded-lg shadow-sm mb-8">
          {/* Alkategóriák listája */}
          {categoryGroups[mainCategory].exercises.length > 0 && (
            <div className="flex flex-wrap gap-2 mb-8 justify-center">
              {categoryGroups[mainCategory].exercises.map((exercise) => (
                <button
                  key={exercise.id}
                  onClick={() => handleExerciseChange(exercise.id)}
                  className={cn(
                    "px-4 py-2 text-sm font-medium transition-all rounded",
                    activeExerciseId === exercise.id
                      ? "bg-blue-50 text-blue-700 border-b-2 border-blue-500"
                      : "text-gray-700 hover:bg-gray-100"
                  )}
                >
                  {exercise.name}
                </button>
              ))}
            </div>
          )}
          
          {/* Aktív gyakorlat megjelenítése */}
          {ActiveExerciseComponent && (
            <div className="pt-2">
              <ActiveExerciseComponent />
            </div>
          )}
          
          {/* Ha nincs gyakorlat a kategóriában */}
          {categoryGroups[mainCategory].exercises.length === 0 && (
            <div className="text-center py-12">
              <p className="text-gray-500">Ebben a kategóriában még nincsenek elérhető gyakorlatok.</p>
              <p className="text-sm text-gray-400 mt-2">Hamarosan érkeznek új tartalmak.</p>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default GrammarExercisesA1;