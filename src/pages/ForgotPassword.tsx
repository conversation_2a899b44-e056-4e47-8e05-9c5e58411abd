import React, { useState } from 'react';
import { <PERSON> } from 'react-router-dom';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Label } from '@/components/ui/label';
import { toast } from 'sonner';
import { motion } from 'framer-motion';
import { Mail, ArrowLeft } from 'lucide-react';
import apiService from '@/services/apiService';

const ForgotPassword: React.FC = () => {
  const [email, setEmail] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [isSubmitted, setIsSubmitted] = useState(false);

  // Je<PERSON><PERSON><PERSON> vissza<PERSON>llítási kérelem kezelése
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    // Validáció
    if (!email) {
      toast.error('<PERSON><PERSON><PERSON>j<PERSON><PERSON>, add meg az email címed!');
      return;
    }
    
    try {
      setIsLoading(true);
      
      // Je<PERSON><PERSON><PERSON> v<PERSON>llítási kérelem küldése
      const response = await apiService.post('/auth/forgot-password', { email });
      
      if (response.data.success) {
        setIsSubmitted(true);
        toast.success('Ha az email cím regisztrálva van, akkor elküldtük a jelszó visszaállítási linket.');
      } else {
        toast.error(response.data.message || 'Hiba történt a kérelem feldolgozása során.');
      }
    } catch (error) {
      console.error('Jelszó visszaállítási hiba:', error);
      toast.error('Hiba történt a kérelem feldolgozása során. Kérjük, próbáld újra később.');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="container mx-auto px-4 py-8 mb-20 md:mb-0 md:pt-24">
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
        className="max-w-md mx-auto"
      >
        <Card>
          <CardHeader>
            <CardTitle className="text-2xl font-bold text-center">Elfelejtett jelszó</CardTitle>
            <CardDescription className="text-center">
              Add meg az email címed, és küldünk egy jelszó visszaállítási linket
            </CardDescription>
          </CardHeader>
          <CardContent>
            {!isSubmitted ? (
              <form onSubmit={handleSubmit} className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="email">Email cím</Label>
                  <div className="relative">
                    <Mail className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                    <Input
                      id="email"
                      type="email"
                      placeholder="email@példa.hu"
                      value={email}
                      onChange={(e) => setEmail(e.target.value)}
                      className="pl-10"
                      required
                    />
                  </div>
                </div>
                
                <Button type="submit" className="w-full" disabled={isLoading}>
                  {isLoading ? 'Feldolgozás...' : 'Jelszó visszaállítása'}
                </Button>
              </form>
            ) : (
              <div className="text-center space-y-4">
                <div className="bg-green-50 text-green-700 p-4 rounded-md">
                  <p className="font-medium">Email elküldve!</p>
                  <p className="text-sm mt-1">
                    Ha az email cím regisztrálva van a rendszerünkben, akkor elküldtünk egy jelszó visszaállítási linket.
                    Kérjük, ellenőrizd az email fiókod, beleértve a spam mappát is.
                  </p>
                </div>
                <Button
                  variant="outline"
                  className="mt-4"
                  onClick={() => {
                    setIsSubmitted(false);
                    setEmail('');
                  }}
                >
                  Új kérelem küldése
                </Button>
              </div>
            )}
          </CardContent>
          <CardFooter className="flex flex-col space-y-2">
            <div className="text-sm text-center text-gray-500">
              <Link to="/login" className="text-language-primary hover:underline inline-flex items-center">
                <ArrowLeft className="h-4 w-4 mr-1" />
                Vissza a bejelentkezéshez
              </Link>
            </div>
          </CardFooter>
        </Card>
      </motion.div>
    </div>
  );
};

export default ForgotPassword;
