import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from '@/components/ui/tabs';
import { Textarea } from '@/components/ui/textarea';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import { useToast } from '@/components/ui/use-toast';
import { useAuth } from '@/contexts/AuthContext';
import { ArrowLeftRight, Globe, Mic, Camera, VolumeUp, Copy, Check, AlertTriangle } from 'lucide-react';
import { usePointsTransaction } from '@/hooks/usePointsTransaction';

const Translator: React.FC = () => {
  const [inputText, setInputText] = useState('');
  const [translatedText, setTranslatedText] = useState('');
  const [isTranslating, setIsTranslating] = useState(false);
  const [sourceLanguage, setSourceLanguage] = useState('hu');
  const [targetLanguage, setTargetLanguage] = useState('de');
  const [isCopied, setIsCopied] = useState(false);
  const [activeTab, setActiveTab] = useState('text');
  const { toast } = useToast();
  const { user } = useAuth();
  const { deductPoints, checkIfEnoughPoints } = usePointsTransaction();

  // Fordítás végrehajtása
  const handleTranslate = async () => {
    if (!inputText.trim()) {
      toast({
        title: 'Hiba',
        description: 'Kérlek, adj meg szöveget a fordításhoz!',
        variant: 'destructive',
      });
      return;
    }

    // Ellenőrizzük, hogy van-e elég pont
    const hasEnoughPoints = await checkIfEnoughPoints(2);
    if (!hasEnoughPoints) {
      toast({
        title: 'Nincs elég pont',
        description: 'A fordításhoz legalább 2 pont szükséges. Kérlek, vásárolj pontokat vagy fizess elő!',
        variant: 'destructive',
      });
      return;
    }

    try {
      setIsTranslating(true);
      
      // Itt lenne a valódi API hívás a fordításhoz
      // Példa: const response = await translateText(inputText, sourceLanguage, targetLanguage);
      
      // Szimulált fordítás (később valódi API-val helyettesítendő)
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // Egyszerű demo fordítás
      let result = '';
      if (sourceLanguage === 'hu' && targetLanguage === 'de') {
        // Magyar -> Német
        const demoTranslations: Record<string, string> = {
          'Jó napot': 'Guten Tag',
          'Szia': 'Hallo',
          'Köszönöm': 'Danke',
          'Hogy vagy?': 'Wie geht es dir?',
          'Beszélsz németül?': 'Sprichst du Deutsch?',
          'Igen': 'Ja',
          'Nem': 'Nein',
          'Viszlát': 'Auf Wiedersehen'
        };
        
        // Keresünk egyezést a demo fordításokban
        const match = Object.keys(demoTranslations).find(key => 
          inputText.toLowerCase().includes(key.toLowerCase())
        );
        
        if (match) {
          result = inputText.replace(new RegExp(match, 'i'), demoTranslations[match]);
        } else {
          // Ha nincs egyezés, egyszerű placeholder fordítást adunk
          result = `[DE] ${inputText}`;
        }
      } else {
        // Német -> Magyar
        const demoTranslations: Record<string, string> = {
          'Guten Tag': 'Jó napot',
          'Hallo': 'Szia',
          'Danke': 'Köszönöm',
          'Wie geht es dir?': 'Hogy vagy?',
          'Sprichst du Deutsch?': 'Beszélsz németül?',
          'Ja': 'Igen',
          'Nein': 'Nem',
          'Auf Wiedersehen': 'Viszlát'
        };
        
        const match = Object.keys(demoTranslations).find(key => 
          inputText.toLowerCase().includes(key.toLowerCase())
        );
        
        if (match) {
          result = inputText.replace(new RegExp(match, 'i'), demoTranslations[match]);
        } else {
          // Ha nincs egyezés, egyszerű placeholder fordítást adunk
          result = `[HU] ${inputText}`;
        }
      }
      
      setTranslatedText(result);
      
      // Pontok levonása
      await deductPoints(2, 'translation', 'Fordítás használata');
      
    } catch (error) {
      console.error('Fordítási hiba:', error);
      toast({
        title: 'Fordítási hiba',
        description: 'Nem sikerült lefordítani a szöveget. Kérlek, próbáld újra később!',
        variant: 'destructive',
      });
    } finally {
      setIsTranslating(false);
    }
  };

  // Nyelvek felcserélése
  const handleSwapLanguages = () => {
    setSourceLanguage(targetLanguage);
    setTargetLanguage(sourceLanguage);
    setInputText(translatedText);
    setTranslatedText(inputText);
  };

  // Szöveg másolása a vágólapra
  const handleCopyText = () => {
    if (translatedText) {
      navigator.clipboard.writeText(translatedText);
      setIsCopied(true);
      setTimeout(() => setIsCopied(false), 2000);
      toast({
        title: 'Másolva',
        description: 'A fordított szöveg a vágólapra másolva!',
      });
    }
  };

  // Mikrofon használata (placeholder)
  const handleMicrophoneInput = () => {
    toast({
      title: 'Mikrofon',
      description: 'A mikrofon funkció hamarosan elérhető lesz!',
    });
  };

  // Kamera használata (placeholder)
  const handleCameraInput = () => {
    toast({
      title: 'Kamera',
      description: 'A kamera alapú fordítás hamarosan elérhető lesz!',
    });
  };

  return (
    <div className="container mx-auto px-4 py-8 mb-20 md:mb-0 md:pt-24">
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
        className="max-w-4xl mx-auto"
      >
        <div className="text-center mb-8">
          <h1 className="text-3xl font-bold mb-2">Fordító</h1>
          <p className="text-gray-600">Fordíts szöveget magyar és német nyelv között</p>
        </div>

        <Card>
          <CardHeader>
            <div className="flex items-center justify-between">
              <CardTitle>Fordító</CardTitle>
              <Badge variant="outline" className="bg-indigo-50 text-indigo-700 border-indigo-200">
                <Globe className="h-3.5 w-3.5 mr-1.5" /> 2 pont / fordítás
              </Badge>
            </div>
            <CardDescription>
              Válaszd ki a fordítás módját és a nyelveket
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
              <TabsList className="grid grid-cols-3 mb-6">
                <TabsTrigger value="text">Szöveg</TabsTrigger>
                <TabsTrigger value="voice">Hang</TabsTrigger>
                <TabsTrigger value="camera">Kamera</TabsTrigger>
              </TabsList>
              
              <TabsContent value="text" className="space-y-6">
                <div className="flex items-center justify-between mb-4">
                  <div className="flex-1">
                    <Label htmlFor="sourceLanguage">Forrás nyelv</Label>
                    <select
                      id="sourceLanguage"
                      value={sourceLanguage}
                      onChange={(e) => setSourceLanguage(e.target.value)}
                      className="w-full p-2 border rounded-md"
                    >
                      <option value="hu">Magyar</option>
                      <option value="de">Német</option>
                    </select>
                  </div>
                  
                  <Button 
                    variant="ghost" 
                    size="icon" 
                    onClick={handleSwapLanguages}
                    className="mx-4"
                  >
                    <ArrowLeftRight className="h-5 w-5" />
                  </Button>
                  
                  <div className="flex-1">
                    <Label htmlFor="targetLanguage">Cél nyelv</Label>
                    <select
                      id="targetLanguage"
                      value={targetLanguage}
                      onChange={(e) => setTargetLanguage(e.target.value)}
                      className="w-full p-2 border rounded-md"
                    >
                      <option value="de">Német</option>
                      <option value="hu">Magyar</option>
                    </select>
                  </div>
                </div>
                
                <div className="space-y-4">
                  <div>
                    <Label htmlFor="inputText">Szöveg fordításra</Label>
                    <Textarea
                      id="inputText"
                      placeholder="Írd be a fordítandó szöveget..."
                      value={inputText}
                      onChange={(e) => setInputText(e.target.value)}
                      className="min-h-[100px]"
                    />
                  </div>
                  
                  <div>
                    <div className="flex justify-between items-center mb-2">
                      <Label htmlFor="translatedText">Fordítás eredménye</Label>
                      {translatedText && (
                        <Button 
                          variant="ghost" 
                          size="sm" 
                          onClick={handleCopyText}
                          className="h-8 px-2"
                        >
                          {isCopied ? <Check className="h-4 w-4 mr-1" /> : <Copy className="h-4 w-4 mr-1" />}
                          {isCopied ? 'Másolva' : 'Másolás'}
                        </Button>
                      )}
                    </div>
                    <Textarea
                      id="translatedText"
                      placeholder="Itt jelenik meg a fordítás..."
                      value={translatedText}
                      readOnly
                      className="min-h-[100px] bg-gray-50"
                    />
                  </div>
                </div>
              </TabsContent>
              
              <TabsContent value="voice" className="space-y-6">
                <div className="text-center py-12 border-2 border-dashed rounded-lg">
                  <Mic className="h-12 w-12 mx-auto text-gray-400 mb-4" />
                  <h3 className="text-lg font-medium mb-2">Hangalapú fordítás</h3>
                  <p className="text-gray-500 mb-4">Beszélj a mikrofonba, és a rendszer lefordítja a beszédedet</p>
                  <Button onClick={handleMicrophoneInput}>
                    <Mic className="h-4 w-4 mr-2" />
                    Mikrofon használata
                  </Button>
                </div>
              </TabsContent>
              
              <TabsContent value="camera" className="space-y-6">
                <div className="text-center py-12 border-2 border-dashed rounded-lg">
                  <Camera className="h-12 w-12 mx-auto text-gray-400 mb-4" />
                  <h3 className="text-lg font-medium mb-2">Kamera alapú fordítás</h3>
                  <p className="text-gray-500 mb-4">Készíts képet a szövegről, és a rendszer felismeri és lefordítja</p>
                  <Button onClick={handleCameraInput}>
                    <Camera className="h-4 w-4 mr-2" />
                    Kamera használata
                  </Button>
                </div>
              </TabsContent>
            </Tabs>
          </CardContent>
          <CardFooter className="flex justify-between">
            <div className="text-sm text-gray-500 flex items-center">
              <AlertTriangle className="h-4 w-4 mr-1.5 text-amber-500" />
              <span>A fordítás 2 pontba kerül</span>
            </div>
            <Button 
              onClick={handleTranslate} 
              disabled={isTranslating || !inputText.trim() || activeTab !== 'text'}
              className="bg-indigo-600 hover:bg-indigo-700"
            >
              {isTranslating ? 'Fordítás...' : 'Fordítás'}
            </Button>
          </CardFooter>
        </Card>
      </motion.div>
    </div>
  );
};

export default Translator;
