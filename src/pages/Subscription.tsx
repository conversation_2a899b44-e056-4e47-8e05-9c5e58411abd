import React, { useState, useEffect } from 'react';
import { useNavigate, Link } from 'react-router-dom';
import { useAuth } from '@/contexts/AuthContext';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { toast } from 'sonner';
import { createCheckoutSession as createStripeCheckout } from '@/services/stripeService';
import { motion } from 'framer-motion';
import { CreditCard, CheckCircle, AlertTriangle, Calendar, Clock, RefreshCw, Coins, Users, Crown, Book, Bot, GraduationCap, Plus } from 'lucide-react';
import api from '@/services/apiService';
import { FREE_TRIAL_PACKAGE, TEACHER_PACKAGE } from '@/services/teacherService';

const Subscription: React.FC = () => {
  const [isLoading, setIsLoading] = useState(false);
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [isReactivating, setIsReactivating] = useState(false);
  const [isAddingPoints, setIsAddingPoints] = useState(false);
  const { user, updateUser, hasSubscription, isTeacher, cancelSubscription, reactivateSubscription, checkSubscription, subscriptionStatus, createTeacherSubscription } = useAuth();
  const navigate = useNavigate();

  // Előfizetés létrehozása
  const handleSubscribe = async () => {
    try {
      setIsLoading(true);

      if (!user) {
        toast.error('Nincs bejelentkezett felhasználó!');
        return;
      }

      // Előfizetés létrehozása - a frissített API-t használjuk
      const checkoutUrl = await createStripeCheckout();

      if (checkoutUrl) {
        // Stripe Checkout oldal megnyitása
        window.location.href = checkoutUrl;
      } else {
        throw new Error('Nem sikerült létrehozni a fizetési munkamenetet');
      }
    } catch (error) {
      console.error('Előfizetési hiba:', error);
      toast.error('Hiba történt az előfizetés során. Kérjük, próbáld újra!');
      setIsLoading(false);
    }
  };

  // Előfizetés lemondása
  const handleCancelSubscription = async () => {
    try {
      setIsLoading(true);

      if (!user) {
        toast.error('Nincs bejelentkezett felhasználó!');
        return;
      }

      // Megerősítés kérése
      if (!window.confirm('Biztosan le szeretnéd mondani az előfizetésed? Ezzel elveszíted a hozzáférést a prémium tartalmakhoz.')) {
        setIsLoading(false);
        return;
      }

      // Az AuthContext cancelSubscription metódusát használjuk
      await cancelSubscription();

      toast.success('Előfizetésed sikeresen lemondva!');
    } catch (error) {
      console.error('Lemondási hiba:', error);
      toast.error('Hiba történt az előfizetés lemondása során. Kérjük, próbáld újra!');
    } finally {
      setIsLoading(false);
    }
  };

  // Előfizetés újraaktiválása
  const handleReactivateSubscription = async () => {
    try {
      setIsReactivating(true);

      if (!user) {
        toast.error('Nincs bejelentkezett felhasználó!');
        return;
      }

      // Az AuthContext reactivateSubscription metódusát használjuk
      await reactivateSubscription();

      toast.success('Előfizetésed sikeresen újraaktiválva!');
    } catch (error) {
      console.error('Újraaktiválási hiba:', error);
      toast.error('Hiba történt az előfizetés újraaktiválása során. Kérjük, próbáld újra!');
    } finally {
      setIsReactivating(false);
    }
  };

  // Előfizetés adatok frissítése
  const refreshSubscriptionData = async () => {
    try {
      setIsRefreshing(true);
      await checkSubscription();
      toast.success('Előfizetési adatok frissítve!');
    } catch (error) {
      console.error('Hiba az előfizetési adatok frissítésekor:', error);
      toast.error('Nem sikerült frissíteni az előfizetési adatokat');
    } finally {
      setIsRefreshing(false);
    }
  };

  // Manuális pontfeltöltés
  const handleAddPoints = async () => {
    try {
      setIsAddingPoints(true);

      if (!user) {
        toast.error('Nincs bejelentkezett felhasználó!');
        return;
      }

      // Megerősítés kérése
      if (!window.confirm('Biztosan hozzá szeretnéd adni a havi előfizetéshez járó pontokat? Ez csak akkor működik, ha van aktív előfizetésed.')) {
        setIsAddingPoints(false);
        return;
      }

      // Pontok hozzáadása
      const response = await api.post('/subscriptions/add-points');

      if (response.data.success) {
        toast.success('Pontok sikeresen hozzáadva!');
        // Frissítjük a felhasználó adatait
        updateUser();
      } else {
        toast.error(response.data.message || 'Hiba történt a pontok hozzáadása során');
      }
    } catch (error: any) {
      console.error('Hiba a pontok hozzáadásakor:', error);
      toast.error(error.response?.data?.message || 'Hiba történt a pontok hozzáadása során. Kérjük, próbáld újra!');
    } finally {
      setIsAddingPoints(false);
    }
  };

  // Komponens betöltésekor frissítjük az előfizetési adatokat
  useEffect(() => {
    if (user) {
      checkSubscription();
    }
  }, [user]);

  // Előfizetés lejárati dátumának formázása
  const formatExpiryDate = (date: Date | null) => {
    if (!date) return 'Nincs megadva';

    return date.toLocaleDateString('hu-HU', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  // Tanári előfizetés létrehozása
  const handleTeacherSubscribe = async () => {
    try {
      setIsLoading(true);

      if (!user) {
        toast.error('Nincs bejelentkezett felhasználó!');
        return;
      }

      // Előfizetés létrehozása
      const checkoutUrl = await createTeacherSubscription();

      if (checkoutUrl) {
        // Stripe Checkout oldal megnyitása
        window.location.href = checkoutUrl;
      } else {
        throw new Error('Nem sikerült létrehozni a fizetési munkamenetet');
      }
    } catch (error) {
      console.error('Előfizetési hiba:', error);
      toast.error('Hiba történt az előfizetés során. Kérjük, próbáld újra!');
      setIsLoading(false);
    }
  };

  // Aktív csomag meghatározása
  const getActivePackage = () => {
    if (isTeacher) return 'teacher';
    if (hasSubscription) return 'subscription';
    return 'free';
  };

  const activePackage = getActivePackage();

  return (
    <div className="container mx-auto px-4 py-8 mb-20 md:mb-0 md:pt-24">
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
        className="max-w-4xl mx-auto"
      >
        <div className="text-center mb-8">
          <h1 className="text-2xl font-bold mb-2">Előfizetési csomagok</h1>
          <p className="text-gray-600">Válaszd ki a számodra legmegfelelőbb csomagot</p>
        </div>

        {/* Előfizetési adatok panel */}
        {user && (hasSubscription || isTeacher) && (
          <Card className="mb-8">
            <CardHeader>
              <div className="flex justify-between items-center">
                <CardTitle>Előfizetési adatok</CardTitle>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={refreshSubscriptionData}
                  disabled={isRefreshing}
                  className="h-8 w-8 p-0"
                >
                  <RefreshCw className={`h-4 w-4 ${isRefreshing ? 'animate-spin' : ''}`} />
                </Button>
              </div>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div className="flex items-start">
                  <div className="bg-gray-100 p-2 rounded-full mr-3">
                    <CreditCard className="h-5 w-5 text-gray-600" />
                  </div>
                  <div>
                    <p className="text-sm font-medium">Előfizetés típusa</p>
                    <p className="text-sm text-gray-600">
                      {isTeacher ? 'Tanári csomag' : 'Havi előfizetés'}
                    </p>
                  </div>
                </div>

                <div className="flex items-start">
                  <div className="bg-gray-100 p-2 rounded-full mr-3">
                    <Calendar className="h-5 w-5 text-gray-600" />
                  </div>
                  <div>
                    <p className="text-sm font-medium">Előfizetés állapota</p>
                    <p className="text-sm text-gray-600">
                      {hasSubscription || isTeacher ? (
                        subscriptionStatus.cancelAtPeriodEnd ? (
                          <span className="text-orange-600 flex items-center">
                            <AlertTriangle className="h-4 w-4 mr-1" /> Lemondva (még aktív)
                          </span>
                        ) : (
                          <span className="text-green-600 flex items-center">
                            <CheckCircle className="h-4 w-4 mr-1" /> Aktív
                          </span>
                        )
                      ) : (
                        <span className="text-red-600 flex items-center">
                          <AlertTriangle className="h-4 w-4 mr-1" /> Inaktív
                        </span>
                      )}
                    </p>
                  </div>
                </div>

                <div className="flex items-start">
                  <div className="bg-gray-100 p-2 rounded-full mr-3">
                    <Clock className="h-5 w-5 text-gray-600" />
                  </div>
                  <div>
                    <p className="text-sm font-medium">Érvényesség</p>
                    <p className="text-sm text-gray-600">
                      {subscriptionStatus.expiryDate ? (
                        <>
                          {formatExpiryDate(subscriptionStatus.expiryDate)}
                          {subscriptionStatus.cancelAtPeriodEnd && (
                            <span className="text-orange-600 block mt-1">
                              (Lemondva, eddig érvényes)
                            </span>
                          )}
                        </>
                      ) : (
                        'Nincs megadva'
                      )}
                    </p>
                  </div>
                </div>
              </div>

              {hasSubscription && subscriptionStatus.cancelAtPeriodEnd && (
                <div className="mt-4 p-3 bg-orange-50 border border-orange-200 rounded-md">
                  <p className="text-sm text-orange-700 flex items-start">
                    <AlertTriangle className="h-4 w-4 mr-2 flex-shrink-0 mt-0.5" />
                    <span>
                      Az előfizetésed lemondva, de a jelenlegi időszak végéig még aktív marad. Az előfizetésed
                      {subscriptionStatus.expiryDate && (
                        <strong className="mx-1">{formatExpiryDate(subscriptionStatus.expiryDate)}</strong>
                      )}
                      napján jár le.
                    </span>
                  </p>
                  <div className="mt-3">
                    <Button
                      size="sm"
                      onClick={handleReactivateSubscription}
                      disabled={isReactivating}
                      className="w-full"
                    >
                      {isReactivating ? (
                        <>
                          <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                          Feldolgozás...
                        </>
                      ) : (
                        'Előfizetés újraaktiválása'
                      )}
                    </Button>
                  </div>
                </div>
              )}

              {(hasSubscription || isTeacher) && !subscriptionStatus.cancelAtPeriodEnd && (
                <div className="flex justify-between">
                  {/* Manuális pontfeltöltés gomb */}
                  <Button
                    variant="outline"
                    onClick={handleAddPoints}
                    disabled={isAddingPoints}
                    className="flex items-center"
                  >
                    {isAddingPoints ? (
                      <>
                        <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                        Feldolgozás...
                      </>
                    ) : (
                      <>
                        <Plus className="h-4 w-4 mr-2" />
                        Pontok hozzáadása
                      </>
                    )}
                  </Button>

                  {/* Előfizetés lemondása gomb */}
                  <Button
                    variant="outline"
                    onClick={handleCancelSubscription}
                    disabled={isLoading}
                  >
                    {isLoading ? 'Feldolgozás...' : 'Előfizetés lemondása'}
                  </Button>
                </div>
              )}
            </CardContent>
          </Card>
        )}

        {/* Csomagok */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          {/* Közös funkciók panel */}
          <div className="col-span-1 md:col-span-3 mb-6">
            <Card className="bg-gradient-to-r from-gray-50 to-gray-100">
              <CardHeader>
                <CardTitle className="text-xl flex items-center">
                  <CheckCircle className="h-5 w-5 text-green-500 mr-2" />
                  Minden csomagban elérhető funkciók
                </CardTitle>
                <CardDescription>
                  Minden felhasználó hozzáfér az összes funkcióhoz, a különbség a használható pontok mennyiségében van
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div className="bg-white p-4 rounded-lg shadow-sm">
                    <h3 className="font-medium text-lg mb-2 flex items-center">
                      <Book className="h-4 w-4 mr-2 text-language-primary" />
                      Tanulási funkciók
                    </h3>
                    <ul className="space-y-1 text-sm">
                      <li className="flex items-center">
                        <CheckCircle className="h-3 w-3 text-green-500 mr-2" />
                        <span>Szókincs és kifejezések</span>
                      </li>
                      <li className="flex items-center">
                        <CheckCircle className="h-3 w-3 text-green-500 mr-2" />
                        <span>Kvízek és játékok</span>
                      </li>
                      <li className="flex items-center">
                        <CheckCircle className="h-3 w-3 text-green-500 mr-2" />
                        <span>Nyelvtani gyakorlatok</span>
                      </li>
                    </ul>
                  </div>

                  <div className="bg-white p-4 rounded-lg shadow-sm">
                    <h3 className="font-medium text-lg mb-2 flex items-center">
                      <Bot className="h-4 w-4 mr-2 text-language-secondary" />
                      AI eszközök
                    </h3>
                    <ul className="space-y-1 text-sm">
                      <li className="flex items-center">
                        <CheckCircle className="h-3 w-3 text-green-500 mr-2" />
                        <span>AI asszisztens</span>
                      </li>
                      <li className="flex items-center">
                        <CheckCircle className="h-3 w-3 text-green-500 mr-2" />
                        <span>Fordító eszköz</span>
                      </li>
                      <li className="flex items-center">
                        <CheckCircle className="h-3 w-3 text-green-500 mr-2" />
                        <span>Kiejtésgyakorló</span>
                      </li>
                    </ul>
                  </div>

                  <div className="bg-white p-4 rounded-lg shadow-sm">
                    <h3 className="font-medium text-lg mb-2 flex items-center">
                      <GraduationCap className="h-4 w-4 mr-2 text-language-quaternary" />
                      Szakmai tartalmak
                    </h3>
                    <ul className="space-y-1 text-sm">
                      <li className="flex items-center">
                        <CheckCircle className="h-3 w-3 text-green-500 mr-2" />
                        <span>Szakmai szókincs</span>
                      </li>
                      <li className="flex items-center">
                        <CheckCircle className="h-3 w-3 text-green-500 mr-2" />
                        <span>Szakma-specifikus kifejezések</span>
                      </li>
                      <li className="flex items-center">
                        <CheckCircle className="h-3 w-3 text-green-500 mr-2" />
                        <span>Gyakorlati párbeszédek</span>
                      </li>
                    </ul>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Ingyenes próba csomag */}
          <Card className={activePackage === 'free' ? 'border-2 border-blue-500' : ''}>
            <CardHeader>
              <CardTitle className="text-xl">Ingyenes próba csomag</CardTitle>
              <CardDescription>
                Ismerkedj meg az alkalmazással
              </CardDescription>
              {activePackage === 'free' && (
                <Badge className="bg-blue-100 text-blue-700 mt-1">Aktív csomag</Badge>
              )}
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="text-2xl font-bold text-center mb-4">{FREE_TRIAL_PACKAGE.price} Ft</div>
              <ul className="space-y-2">
                <li className="flex items-center">
                  <CheckCircle className="h-4 w-4 text-green-500 mr-2" />
                  <span>Hozzáférés minden funkcióhoz</span>
                </li>
                <li className="flex items-center">
                  <CheckCircle className="h-4 w-4 text-green-500 mr-2" />
                  <span>{FREE_TRIAL_PACKAGE.points} pont az AI funkciók használatához</span>
                </li>
                <li className="flex items-center">
                  <CheckCircle className="h-4 w-4 text-green-500 mr-2" />
                  <span>Bármikor vásárolhatsz további pontcsomagokat</span>
                </li>
                <li className="flex items-center">
                  <CheckCircle className="h-4 w-4 text-green-500 mr-2" />
                  <span>Korlátlan ideig használható</span>
                </li>
              </ul>
            </CardContent>
            <CardFooter>
              {activePackage === 'free' ? (
                <Button
                  variant="outline"
                  className="w-full"
                  disabled
                >
                  Jelenleg aktív
                </Button>
              ) : (
                <Button
                  variant="outline"
                  className="w-full"
                  disabled
                >
                  Alapértelmezett csomag
                </Button>
              )}
            </CardFooter>
          </Card>

          {/* Havi előfizetés */}
          <Card className={activePackage === 'subscription' ? 'border-2 border-purple-500' : ''}>
            <CardHeader>
              <CardTitle className="text-xl">Havi előfizetés</CardTitle>
              <CardDescription>
                Több pont az AI funkciók használatához
              </CardDescription>
              {activePackage === 'subscription' && (
                <Badge className="bg-purple-100 text-purple-700 mt-1">Aktív csomag</Badge>
              )}
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="text-2xl font-bold text-center mb-4">2990 Ft / hó</div>
              <ul className="space-y-2">
                <li className="flex items-center">
                  <CheckCircle className="h-4 w-4 text-green-500 mr-2" />
                  <span>Hozzáférés minden funkcióhoz</span>
                </li>
                <li className="flex items-center">
                  <CheckCircle className="h-4 w-4 text-green-500 mr-2" />
                  <span>500 pont havonta az AI funkciók használatához</span>
                </li>
                <li className="flex items-center">
                  <CheckCircle className="h-4 w-4 text-green-500 mr-2" />
                  <span>Fel nem használt pontok átvihetők a következő hónapra</span>
                </li>
                <li className="flex items-center">
                  <CheckCircle className="h-4 w-4 text-green-500 mr-2" />
                  <span>Bármikor lemondható</span>
                </li>
              </ul>
            </CardContent>
            <CardFooter>
              {activePackage === 'subscription' ? (
                <Button
                  variant="outline"
                  className="w-full"
                  disabled
                >
                  Jelenleg aktív
                </Button>
              ) : (
                <Button
                  onClick={handleSubscribe}
                  className="w-full bg-purple-600 hover:bg-purple-700 text-white"
                  disabled={isLoading}
                >
                  <CreditCard className="h-4 w-4 mr-2" />
                  {isLoading ? 'Feldolgozás...' : 'Előfizetés most'}
                </Button>
              )}
            </CardFooter>
          </Card>

          {/* Tanári csomag */}
          <Card className={`${activePackage === 'teacher' ? 'border-2 border-blue-500' : ''} border-blue-200`}>
            <CardHeader className={activePackage === 'teacher' ? '' : 'bg-blue-50'}>
              <div className="bg-blue-500 text-white text-xs font-bold px-3 py-1 rounded-full w-fit mb-2">
                AJÁNLOTT
              </div>
              <CardTitle className="text-xl">Tanári csomag</CardTitle>
              <CardDescription>
                Ideális tanároknak, akik diákjaikkal szeretnének dolgozni
              </CardDescription>
              {activePackage === 'teacher' && (
                <Badge className="bg-blue-100 text-blue-700 mt-1">Aktív csomag</Badge>
              )}
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="text-2xl font-bold text-center mb-4">{TEACHER_PACKAGE.price} Ft / hó</div>
              <ul className="space-y-2">
                <li className="flex items-center">
                  <CheckCircle className="h-4 w-4 text-green-500 mr-2" />
                  <span>Hozzáférés minden funkcióhoz</span>
                </li>
                <li className="flex items-center">
                  <Coins className="h-4 w-4 text-blue-500 mr-2" />
                  <span>{TEACHER_PACKAGE.monthlyPoints} pont havonta az AI funkciók használatához</span>
                </li>
                <li className="flex items-center">
                  <Users className="h-4 w-4 text-blue-500 mr-2" />
                  <span>Maximum {TEACHER_PACKAGE.maxStudents} diák meghívása</span>
                </li>
                <li className="flex items-center">
                  <CheckCircle className="h-4 w-4 text-green-500 mr-2" />
                  <span>{TEACHER_PACKAGE.studentGiftPoints} pont ajándék minden meghívott diáknak</span>
                </li>
                <li className="flex items-center">
                  <CheckCircle className="h-4 w-4 text-green-500 mr-2" />
                  <span>Haladáskövetés diákonként</span>
                </li>
              </ul>
            </CardContent>
            <CardFooter>
              {activePackage === 'teacher' ? (
                <Button
                  variant="outline"
                  className="w-full"
                  disabled
                >
                  Jelenleg aktív
                </Button>
              ) : (
                <Button
                  onClick={handleTeacherSubscribe}
                  className="w-full bg-blue-600 hover:bg-blue-700 text-white"
                  disabled={isLoading}
                >
                  <Crown className="h-4 w-4 mr-2" />
                  {isLoading ? 'Feldolgozás...' : 'Előfizetés most'}
                </Button>
              )}
            </CardFooter>
          </Card>
        </div>

        <div className="mt-8 text-center">
          <Button
            variant="ghost"
            onClick={() => navigate('/')}
          >
            Vissza a főoldalra
          </Button>
        </div>

        <div className="mt-8 text-center text-sm text-gray-500">
          <p>Az előfizetéssel elfogadod a <Link to="/terms" className="text-blue-500 hover:underline">szolgáltatási feltételeinket</Link> és <Link to="/privacy" className="text-blue-500 hover:underline">adatvédelmi irányelveinket</Link>.</p>
        </div>
      </motion.div>
    </div>
  );
};

export default Subscription;
