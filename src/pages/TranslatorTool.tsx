import React, { useState, useRef, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle, CardDescription, CardFooter } from '@/components/ui/card';
import { Ta<PERSON>, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { Textarea } from '@/components/ui/textarea';
import { Switch } from '@/components/ui/switch';
import { Label } from '@/components/ui/label';
import {
  Mic,
  Camera,
  Languages,
  ArrowLeftRight,
  Copy,
  Volume2,
  Loader2,
  X,
  Check,
  Keyboard
} from 'lucide-react';
import { getTranslation } from '@/services/assistantService';
import { recognizeSpeech } from '@/services/speechRecognitionService';
import { recognizeTextFromImage } from '@/services/ocrService';
import { textToSpeech } from '@/services/openaiService';
import { toast } from 'sonner';
import CameraCapture from '@/components/CameraCapture';

const TranslatorTool: React.FC = () => {
  // Állapotok
  const [inputText, setInputText] = useState('');
  const [translatedText, setTranslatedText] = useState('');
  const [isTranslating, setIsTranslating] = useState(false);
  const [isRecording, setIsRecording] = useState(false);
  const [isPlayingAudio, setIsPlayingAudio] = useState(false);
  const [activeTab, setActiveTab] = useState('text');
  const [direction, setDirection] = useState<'hu-de' | 'de-hu'>('hu-de');
  const [showCamera, setShowCamera] = useState(false);
  const [isProcessingImage, setIsProcessingImage] = useState(false);
  const [copiedText, setCopiedText] = useState<string | null>(null);

  // Referenciák
  const mediaRecorderRef = useRef<MediaRecorder | null>(null);
  const audioChunksRef = useRef<Blob[]>([]);
  const audioRef = useRef<HTMLAudioElement | null>(null);

  // Irány megfordítása
  const toggleDirection = () => {
    setDirection(prev => prev === 'hu-de' ? 'de-hu' : 'hu-de');
  };

  // Fordítás végrehajtása
  const handleTranslate = async () => {
    if (!inputText.trim()) return;

    try {
      setIsTranslating(true);
      const targetLanguage = direction === 'hu-de' ? 'de' : 'hu';
      const result = await getTranslation(inputText, targetLanguage as 'hu' | 'de');
      setTranslatedText(result);
    } catch (error) {
      console.error('Fordítási hiba:', error);
      toast.error('Hiba történt a fordítás során. Kérlek, próbáld újra később!');
    } finally {
      setIsTranslating(false);
    }
  };

  // Szöveg másolása a vágólapra
  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text)
      .then(() => {
        setCopiedText(text);
        setTimeout(() => setCopiedText(null), 2000);
        toast.success('Szöveg másolva a vágólapra!');
      })
      .catch(err => {
        console.error('Hiba a másolás során:', err);
        toast.error('Nem sikerült másolni a szöveget.');
      });
  };

  // Szöveg felolvasása
  const handleSpeakText = async (text: string) => {
    if (!text.trim() || isPlayingAudio) return;

    try {
      setIsPlayingAudio(true);
      const language = direction === 'hu-de' ? 'de-DE' : 'hu-HU';
      const audioUrl = await textToSpeech(text, language);

      if (audioRef.current) {
        audioRef.current.src = audioUrl;
        audioRef.current.play();
      }
    } catch (error) {
      console.error('Hiba a szövegfelolvasás során:', error);
      toast.error('Hiba történt a szövegfelolvasás során. Kérlek, próbáld újra később!');
      setIsPlayingAudio(false);
    }
  };

  // Hangfelvétel indítása
  const startRecording = async () => {
    try {
      const stream = await navigator.mediaDevices.getUserMedia({ audio: true });

      const mediaRecorder = new MediaRecorder(stream);
      mediaRecorderRef.current = mediaRecorder;
      audioChunksRef.current = [];

      mediaRecorder.ondataavailable = (event) => {
        if (event.data.size > 0) {
          audioChunksRef.current.push(event.data);
        }
      };

      mediaRecorder.onstop = async () => {
        const audioBlob = new Blob(audioChunksRef.current, { type: 'audio/webm' });

        try {
          // Hangfelismerés
          const sourceLanguage = direction === 'hu-de' ? 'hu' : 'de';
          const recognizedText = await recognizeSpeech(audioBlob, sourceLanguage);
          setInputText(recognizedText);

          // Automatikus fordítás
          if (recognizedText.trim()) {
            setIsTranslating(true);
            const targetLanguage = direction === 'hu-de' ? 'de' : 'hu';
            const translatedResult = await getTranslation(recognizedText, targetLanguage as 'hu' | 'de');
            setTranslatedText(translatedResult);
            setIsTranslating(false);
          }
        } catch (error) {
          console.error('Hiba a hangfelismerés során:', error);
          toast.error('Hiba történt a hangfelismerés során. Kérlek, próbáld újra később!');
        }

        // Stream leállítása
        stream.getTracks().forEach(track => track.stop());
        setIsRecording(false);
      };

      mediaRecorder.start();
      setIsRecording(true);

      // Automatikus leállítás 10 másodperc után
      setTimeout(() => {
        if (mediaRecorderRef.current && mediaRecorderRef.current.state === 'recording') {
          mediaRecorderRef.current.stop();
        }
      }, 10000);

    } catch (error) {
      console.error('Hiba a hangfelvétel során:', error);
      toast.error('Nem sikerült hozzáférni a mikrofonhoz. Kérlek, ellenőrizd a mikrofon engedélyeket.');
      setIsRecording(false);
    }
  };

  // Hangfelvétel leállítása
  const stopRecording = () => {
    if (mediaRecorderRef.current && mediaRecorderRef.current.state === 'recording') {
      mediaRecorderRef.current.stop();
    }
  };

  // Kép feldolgozása
  const handleImageCapture = async (imageBlob: Blob) => {
    try {
      setIsProcessingImage(true);

      // OCR - szöveg felismerése a képről
      const sourceLanguage = direction === 'hu-de' ? 'hu' : 'de';
      const recognizedText = await recognizeTextFromImage(imageBlob, sourceLanguage);
      setInputText(recognizedText);

      // Automatikus fordítás
      if (recognizedText.trim()) {
        const targetLanguage = direction === 'hu-de' ? 'de' : 'hu';
        const translatedResult = await getTranslation(recognizedText, targetLanguage as 'hu' | 'de');
        setTranslatedText(translatedResult);
      }

      setShowCamera(false);
    } catch (error) {
      console.error('Hiba a képfeldolgozás során:', error);
      toast.error('Hiba történt a képfeldolgozás során. Kérlek, próbáld újra később!');
    } finally {
      setIsProcessingImage(false);
    }
  };

  // Audio eseménykezelők
  useEffect(() => {
    const audio = new Audio();
    audioRef.current = audio;

    audio.onended = () => {
      setIsPlayingAudio(false);
    };

    audio.onerror = () => {
      console.error('Hiba a hang lejátszása során');
      setIsPlayingAudio(false);
      toast.error('Hiba történt a hang lejátszása során.');
    };

    return () => {
      audio.pause();
      audio.src = '';
    };
  }, []);

  // Komponens törlődésekor leállítjuk a felvételt
  useEffect(() => {
    return () => {
      if (mediaRecorderRef.current && mediaRecorderRef.current.state === 'recording') {
        mediaRecorderRef.current.stop();
      }
    };
  }, []);

  return (
    <div className="container mx-auto py-6 px-4">
      <div className="max-w-3xl mx-auto">
        <h1 className="text-2xl font-bold mb-2 flex items-center gap-2">
          <Languages className="h-6 w-6 text-language-primary" />
          Fordító eszköz
        </h1>
        <p className="text-gray-600 mb-6">
          Fordíts szöveget magyar és német nyelv között, használd a mikrofont beszédfelismeréshez vagy a kamerát szöveg felismeréséhez képről.
        </p>

        <Card className="mb-6">
          <CardHeader className="pb-3">
            <div className="flex justify-between items-center">
              <CardTitle className="text-lg">Fordítási irány</CardTitle>
              <Button
                variant="ghost"
                size="sm"
                onClick={toggleDirection}
                className="flex items-center gap-1"
              >
                <span className={direction === 'hu-de' ? 'font-bold text-language-primary' : ''}>
                  Magyar
                </span>
                <ArrowLeftRight className="h-4 w-4 mx-1" />
                <span className={direction === 'de-hu' ? 'font-bold text-language-primary' : ''}>
                  Német
                </span>
              </Button>
            </div>
            <CardDescription>
              {direction === 'hu-de'
                ? 'Magyarról németre fordítás'
                : 'Németről magyarra fordítás'}
            </CardDescription>
          </CardHeader>
        </Card>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
          <Card>
            <CardHeader className="pb-3">
              <CardTitle className="text-lg">
                {direction === 'hu-de' ? 'Magyar szöveg' : 'Német szöveg'}
              </CardTitle>
              <CardDescription>
                Írd be vagy diktáld a fordítandó szöveget
              </CardDescription>
            </CardHeader>
            <CardContent>
              {!showCamera ? (
                <>
                  <Tabs value={activeTab} onValueChange={setActiveTab} className="mb-4">
                    <TabsList className="grid grid-cols-3">
                      <TabsTrigger value="text" className="flex items-center gap-1">
                        <Keyboard className="h-4 w-4" />
                        <span className="hidden sm:inline">Szöveg</span>
                      </TabsTrigger>
                      <TabsTrigger value="voice" className="flex items-center gap-1">
                        <Mic className="h-4 w-4" />
                        <span className="hidden sm:inline">Hang</span>
                      </TabsTrigger>
                      <TabsTrigger value="camera" className="flex items-center gap-1">
                        <Camera className="h-4 w-4" />
                        <span className="hidden sm:inline">Kamera</span>
                      </TabsTrigger>
                    </TabsList>

                    <TabsContent value="text" className="mt-4">
                      <Textarea
                        placeholder={`Írd be a ${direction === 'hu-de' ? 'magyar' : 'német'} szöveget...`}
                        value={inputText}
                        onChange={(e) => setInputText(e.target.value)}
                        className="min-h-[120px] resize-none"
                      />
                      <div className="flex justify-end mt-2">
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => setInputText('')}
                          disabled={!inputText}
                          className="mr-2"
                        >
                          <X className="h-4 w-4 mr-1" />
                          Törlés
                        </Button>
                        <Button
                          onClick={handleTranslate}
                          disabled={!inputText.trim() || isTranslating}
                          className="bg-language-primary hover:bg-language-primary/90"
                        >
                          {isTranslating ? (
                            <>
                              <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                              Fordítás...
                            </>
                          ) : (
                            <>
                              <Languages className="h-4 w-4 mr-2" />
                              Fordítás
                            </>
                          )}
                        </Button>
                      </div>
                    </TabsContent>

                    <TabsContent value="voice" className="mt-4">
                      <div className="flex flex-col items-center justify-center py-8">
                        <Button
                          onClick={isRecording ? stopRecording : startRecording}
                          className={`rounded-full w-16 h-16 ${
                            isRecording
                              ? 'bg-red-500 hover:bg-red-600 animate-pulse'
                              : 'bg-language-primary hover:bg-language-primary/90'
                          }`}
                        >
                          <Mic className="h-6 w-6" />
                        </Button>
                        <p className="mt-4 text-sm text-gray-600">
                          {isRecording
                            ? 'Felvétel folyamatban... Kattints a leállításhoz.'
                            : `Kattints a gombra a ${direction === 'hu-de' ? 'magyar' : 'német'} beszéd rögzítéséhez.`}
                        </p>
                        {inputText && (
                          <div className="mt-4 w-full">
                            <p className="text-sm font-medium mb-1">Felismert szöveg:</p>
                            <div className="p-3 bg-gray-50 rounded-md text-sm">
                              {inputText}
                            </div>
                          </div>
                        )}
                      </div>
                    </TabsContent>

                    <TabsContent value="camera" className="mt-4">
                      <div className="flex flex-col items-center justify-center py-4">
                        <Button
                          onClick={() => setShowCamera(true)}
                          className="bg-language-primary hover:bg-language-primary/90"
                        >
                          <Camera className="h-4 w-4 mr-2" />
                          Kamera megnyitása
                        </Button>
                        <p className="mt-2 text-sm text-gray-600">
                          Készíts képet a fordítandó szövegről
                        </p>
                        {inputText && (
                          <div className="mt-4 w-full">
                            <p className="text-sm font-medium mb-1">Felismert szöveg:</p>
                            <div className="p-3 bg-gray-50 rounded-md text-sm">
                              {inputText}
                            </div>
                          </div>
                        )}
                      </div>
                    </TabsContent>
                  </Tabs>
                </>
              ) : (
                <CameraCapture
                  onCapture={handleImageCapture}
                  onCancel={() => setShowCamera(false)}
                  isLoading={isProcessingImage}
                />
              )}
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="pb-3">
              <CardTitle className="text-lg">
                {direction === 'hu-de' ? 'Német fordítás' : 'Magyar fordítás'}
              </CardTitle>
              <CardDescription>
                A fordítás eredménye
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="min-h-[120px] bg-gray-50 rounded-md p-3 mb-3">
                {isTranslating ? (
                  <div className="flex items-center justify-center h-full">
                    <Loader2 className="h-5 w-5 animate-spin text-language-primary mr-2" />
                    <span className="text-sm text-gray-600">Fordítás folyamatban...</span>
                  </div>
                ) : translatedText ? (
                  <p className="whitespace-pre-wrap">{translatedText}</p>
                ) : (
                  <p className="text-gray-400 text-center py-10">
                    A fordítás itt fog megjelenni
                  </p>
                )}
              </div>

              <div className="flex justify-end gap-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => handleSpeakText(translatedText)}
                  disabled={!translatedText || isPlayingAudio}
                >
                  {isPlayingAudio ? (
                    <Loader2 className="h-4 w-4 mr-1 animate-spin" />
                  ) : (
                    <Volume2 className="h-4 w-4 mr-1" />
                  )}
                  Felolvasás
                </Button>

                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => copyToClipboard(translatedText)}
                  disabled={!translatedText}
                >
                  {copiedText === translatedText ? (
                    <>
                      <Check className="h-4 w-4 mr-1 text-green-500" />
                      Másolva
                    </>
                  ) : (
                    <>
                      <Copy className="h-4 w-4 mr-1" />
                      Másolás
                    </>
                  )}
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>

        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="text-lg">Beállítások</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex items-center justify-between">
              <div className="space-y-0.5">
                <Label htmlFor="auto-translate">Automatikus fordítás</Label>
                <p className="text-sm text-gray-500">
                  Automatikusan fordítsa le a szöveget bevitel közben
                </p>
              </div>
              <Switch id="auto-translate" />
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default TranslatorTool;
