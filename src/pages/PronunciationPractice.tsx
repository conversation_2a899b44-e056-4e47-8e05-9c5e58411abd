import React from 'react';
import PronunciationPracticeComponent from '../components/games/PronunciationPractice';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../components/ui/card';

const PronunciationPracticePage: React.FC = () => {
  return (
    <div className="container mx-auto px-4 py-8 mb-20 md:mb-0 md:pt-24">
      <h1 className="text-2xl font-bold text-center mb-6">Kiejtésgyakorló</h1>
      
      <Card className="mb-8">
        <CardHeader>
          <CardTitle>Gyakorold a német kiejtést</CardTitle>
          <CardDescription>
            Fejleszd a kiejtésedet hangfelismerés segítségével. Hallgasd meg a helyes kiejtést, majd próbáld meg utánozni.
            A rendszer értékeli a kiejtésedet és visszajelzést ad.
          </CardDescription>
        </CardHeader>
        <CardContent>
          <PronunciationPracticeComponent />
        </CardContent>
      </Card>
      
      <Card>
        <CardHeader>
          <CardTitle>Tippek a jobb kiejtéshez</CardTitle>
        </CardHeader>
        <CardContent>
          <ul className="list-disc pl-5 space-y-2">
            <li>Figyelj a német nyelv speciális hangjaira, mint az ö, ü, ä.</li>
            <li>A német r hang kiejtése eltér a magyartól, próbáld a torok hátsó részében képezni.</li>
            <li>A ch hang kiejtése függ a megelőző magánhangzótól (ich vs. ach).</li>
            <li>Gyakorold a hosszú és rövid magánhangzók közötti különbséget.</li>
            <li>Figyelj a hangsúlyra, a német szavakban általában az első szótag hangsúlyos.</li>
            <li>Rendszeres gyakorlással fejlődik a kiejtésed, ne add fel!</li>
          </ul>
        </CardContent>
      </Card>
    </div>
  );
};

export default PronunciationPracticePage;
