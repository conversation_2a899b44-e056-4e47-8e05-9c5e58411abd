import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '@/contexts/AuthContext';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Label } from '@/components/ui/label';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { toast } from 'sonner';
import { motion } from 'framer-motion';
import { User, Mail, CreditCard, LogOut, CheckCircle, AlertTriangle, Calendar, Clock, RefreshCw, Volume2 } from 'lucide-react';
import InvoicesList from '@/components/subscription/InvoicesList';
import SpeechSettingsTab from '@/components/profile/SpeechSettingsTab';

const Profile: React.FC = () => {
  const { user, logout, hasSubscription, checkSubscription, subscriptionStatus } = useAuth();
  const navigate = useNavigate();
  const [isRefreshing, setIsRefreshing] = useState(false);

  // Előfizetés lejárati dátumának formázása
  const formatExpiryDate = (dateString: string | null) => {
    if (!dateString) return 'Nincs megadva';

    const date = new Date(dateString);
    return date.toLocaleDateString('hu-HU', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  // Előfizetés frissítése
  const refreshSubscription = async () => {
    try {
      setIsRefreshing(true);
      if (user) {
        console.log('Profil oldal: Előfizetés frissítése a következő felhasználónak:', user.email);
        await checkSubscription(user);
        toast.success('Előfizetés állapota frissítve!');
      } else {
        toast.error('Nincs bejelentkezett felhasználó');
      }
    } catch (error) {
      console.error('Hiba az előfizetés frissítésekor:', error);
      toast.error('Nem sikerült frissíteni az előfizetés állapotát');
    } finally {
      setIsRefreshing(false);
    }
  };

  // Automatikus előfizetés ellenőrzés betöltéskor, de csak egyszer
  useEffect(() => {
    // Csak egyszer frissítjük az előfizetést, amikor a komponens betöltődik
    // és a felhasználó be van jelentkezve
    let isMounted = true;

    if (user && isMounted) {
      console.log('Profil oldal: Előfizetés ellenőrzése a következő felhasználónak:', user.email);
      checkSubscription(user);
    }

    // Cleanup függvény, amely jelzi, ha a komponens eltávolításra kerül
    return () => {
      isMounted = false;
    };
  }, [user]); // checkSubscription eltávolítva a függőségekből

  // Kijelentkezés kezelése
  const handleLogout = () => {
    logout();
    navigate('/login');
  };

  return (
    <div className="container mx-auto px-4 py-8 mb-20 md:mb-0 md:pt-24">
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
        className="max-w-3xl mx-auto"
      >
        <h1 className="text-2xl font-bold text-center mb-6">Profilom</h1>

        <Tabs defaultValue="profile" className="w-full">
          <TabsList className="grid w-full grid-cols-4">
            <TabsTrigger value="profile">Profil adatok</TabsTrigger>
            <TabsTrigger value="subscription">Előfizetés</TabsTrigger>
            <TabsTrigger value="invoices">Előfizetési számlák</TabsTrigger>
            <TabsTrigger value="speech-settings" className="flex items-center gap-1">
              <Volume2 className="h-4 w-4" />
              <span>Beszédbeállítások</span>
            </TabsTrigger>
          </TabsList>

          <TabsContent value="profile">
            <Card>
              <CardHeader>
                <CardTitle>Profil adatok</CardTitle>
                <CardDescription>
                  Itt kezelheted a fiókod adatait
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                {user && (
                  <>
                    <div className="flex flex-col md:flex-row gap-4">
                      <div className="space-y-2 flex-1">
                        <Label htmlFor="name">Név</Label>
                        <div className="relative">
                          <User className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                          <Input
                            id="name"
                            value={user.name}
                            className="pl-10"
                            readOnly
                          />
                        </div>
                      </div>

                      <div className="space-y-2 flex-1">
                        <Label htmlFor="email">Email cím</Label>
                        <div className="relative">
                          <Mail className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                          <Input
                            id="email"
                            value={user.email}
                            className="pl-10"
                            readOnly
                          />
                        </div>
                      </div>
                    </div>

                    <div className="bg-gray-50 p-4 rounded-lg border">
                      <h3 className="font-medium mb-2">Fiók állapota</h3>
                      <div className="flex items-center">
                        <div className="mr-2">
                          {hasSubscription ? (
                            <CheckCircle className="h-5 w-5 text-green-500" />
                          ) : (
                            <AlertTriangle className="h-5 w-5 text-orange-500" />
                          )}
                        </div>
                        <div>
                          {hasSubscription ? (
                            <p className="text-green-600">Aktív előfizetés</p>
                          ) : (
                            <p className="text-orange-600">Nincs aktív előfizetés</p>
                          )}
                        </div>
                      </div>
                    </div>
                  </>
                )}
              </CardContent>
              <CardFooter className="flex justify-between">
                <Button variant="outline" onClick={handleLogout} className="flex items-center">
                  <LogOut className="h-4 w-4 mr-2" />
                  Kijelentkezés
                </Button>
              </CardFooter>
            </Card>
          </TabsContent>

          <TabsContent value="subscription">
            <Card>
              <CardHeader>
                <CardTitle>Előfizetés kezelése</CardTitle>
                <CardDescription>
                  Itt kezelheted az előfizetésed
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                {user && (
                  <div className="bg-gray-50 p-4 rounded-lg border">
                    <div className="flex justify-between items-center mb-2">
                      <h3 className="font-medium text-lg">Előfizetési adatok</h3>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={refreshSubscription}
                        disabled={isRefreshing}
                        className="h-8 px-2"
                      >
                        <RefreshCw className={`h-4 w-4 mr-1 ${isRefreshing ? 'animate-spin' : ''}`} />
                        Frissítés
                      </Button>
                    </div>

                    <div className="space-y-3 mt-4">
                      <div className="flex items-start">
                        <div className="bg-gray-100 p-2 rounded-full mr-3">
                          <CreditCard className="h-5 w-5 text-gray-600" />
                        </div>
                        <div>
                          <p className="text-sm font-medium">Előfizetés típusa</p>
                          <p className="text-sm text-gray-600">Havi előfizetés</p>
                        </div>
                      </div>

                      <div className="flex items-start">
                        <div className="bg-gray-100 p-2 rounded-full mr-3">
                          <Calendar className="h-5 w-5 text-gray-600" />
                        </div>
                        <div>
                          <p className="text-sm font-medium">Előfizetés állapota</p>
                          <p className="text-sm text-gray-600">
                            {hasSubscription ? (
                              <span className="text-green-600 flex items-center">
                                <CheckCircle className="h-4 w-4 mr-1" /> Aktív
                              </span>
                            ) : (
                              <span className="text-red-600 flex items-center">
                                <AlertTriangle className="h-4 w-4 mr-1" /> Inaktív
                              </span>
                            )}
                          </p>
                        </div>
                      </div>

                      {subscriptionStatus.expiryDate && (
                        <div className="flex items-start">
                          <div className="bg-gray-100 p-2 rounded-full mr-3">
                            <Clock className="h-5 w-5 text-gray-600" />
                          </div>
                          <div>
                            <p className="text-sm font-medium">Érvényesség</p>
                            <p className="text-sm text-gray-600">
                              {formatExpiryDate(subscriptionStatus.expiryDate.toISOString())}
                            </p>
                          </div>
                        </div>
                      )}

                      {subscriptionStatus.cancelAtPeriodEnd && (
                        <div className="mt-2 p-2 bg-orange-50 border border-orange-200 rounded-md">
                          <p className="text-sm text-orange-700 flex items-center">
                            <AlertTriangle className="h-4 w-4 mr-1" />
                            Az előfizetésed lemondva, de a jelenlegi időszak végéig még aktív marad.
                          </p>
                        </div>
                      )}
                    </div>
                  </div>
                )}
              </CardContent>
              <CardFooter>
                <Button
                  onClick={() => navigate('/subscription')}
                  className="w-full"
                >
                  Előfizetés kezelése
                </Button>
              </CardFooter>
            </Card>
          </TabsContent>

          <TabsContent value="invoices">
            <Card>
              <CardHeader>
                <CardTitle>Előfizetési számlák</CardTitle>
                <CardDescription>
                  Itt tekintheted meg az előfizetéshez kapcsolódó számláidat és fizetési előzményeidet
                </CardDescription>
              </CardHeader>
              <CardContent>
                <InvoicesList />
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="speech-settings">
            <SpeechSettingsTab />
          </TabsContent>

        </Tabs>
      </motion.div>
    </div>
  );
};

export default Profile;
