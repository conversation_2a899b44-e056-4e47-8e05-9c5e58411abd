import React, { useState, useEffect, useRef } from "react";
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle, CardDescription, CardFooter } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { vocabularyData, phrasesData, QuizQuestion } from "@/data/languageData";
import { Badge } from "@/components/ui/badge";
import { Gamepad2, Brain, Target, Timer, Volume2, Loader2, Pencil, Headphones, X, ArrowLeft, ArrowRight, RotateCcw, Layers, MoveHorizontal, Check, Dices, Puzzle, AlignJustify, CaseUpper } from "lucide-react";
import { cn } from "@/lib/utils";
import { Progress } from "@/components/ui/progress";
import { Input } from "@/components/ui/input";
import { textToSpeech } from "@/services/openaiService";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";

const Games = () => {
  const [score, setScore] = useState(0);
  const [currentGame, setCurrentGame] = useState<string | null>(null);
  const [currentWord, setCurrentWord] = useState<string>("");
  const [options, setOptions] = useState<string[]>([]);
  const [isCorrect, setIsCorrect] = useState<boolean | null>(null);
  const [currentPhrase, setCurrentPhrase] = useState<string>("");
  const [shuffledWords, setShuffledWords] = useState<string[]>([]);
  const [selectedWords, setSelectedWords] = useState<string[]>([]);
  const [timeLeft, setTimeLeft] = useState<number>(10);
  const [timerActive, setTimerActive] = useState(false);
  const [currentQuickQuestion, setCurrentQuickQuestion] = useState<{
    word: string;
    translation: string;
    options: string[];
  } | null>(null);
  const [quickAnswerStreak, setQuickAnswerStreak] = useState(0);

  // Hallás utáni írás játékhoz
  const [dictationText, setDictationText] = useState("");
  const [userInput, setUserInput] = useState("");
  const [isPlayingAudio, setIsPlayingAudio] = useState(false);
  const [dictationResult, setDictationResult] = useState<"correct" | "incorrect" | null>(null);
  const audioRef = useRef<HTMLAudioElement | null>(null);

  // Szövegkiegészítés játékhoz
  const [gapFillPhrase, setGapFillPhrase] = useState("");
  const [gapFillOriginal, setGapFillOriginal] = useState("");
  const [gapFillOptions, setGapFillOptions] = useState<string[]>([]);
  const [gapFillAnswer, setGapFillAnswer] = useState("");
  const [gapFillUserAnswer, setGapFillUserAnswer] = useState("");

  // Kategória párosítás játékhoz
  const [categoryWords, setCategoryWords] = useState<{word: string, category: string, displayCategory: string}[]>([]);
  const [availableCategories, setAvailableCategories] = useState<string[]>([]);
  const [selectedCategory, setSelectedCategory] = useState<string>("");
  const [selectedWord, setSelectedWord] = useState<string>("");
  const [matchedPairs, setMatchedPairs] = useState<{word: string, category: string, displayCategory: string}[]>([]);
  const [wrongAttempts, setWrongAttempts] = useState<{word: string, category: string, displayCategory: string}[]>([]);
  const [categoryGameLevel, setCategoryGameLevel] = useState<number>(1);

  // Anagramma játékhoz
  const [anagramWord, setAnagramWord] = useState<string>("");
  const [anagramOriginal, setAnagramOriginal] = useState<string>("");
  const [anagramLetters, setAnagramLetters] = useState<{letter: string, selected: boolean}[]>([]);
  const [anagramUserWord, setAnagramUserWord] = useState<string>("");
  const [anagramHint, setAnagramHint] = useState<string>("");
  const [anagramDifficulty, setAnagramDifficulty] = useState<number>(1);
  const [anagramAttempts, setAnagramAttempts] = useState<number>(0);

  // Akasztófa játékhoz
  const [hangmanWord, setHangmanWord] = useState<string>("");
  const [hangmanHint, setHangmanHint] = useState<string>("");
  const [hangmanGuessedLetters, setHangmanGuessedLetters] = useState<string[]>([]);
  const [hangmanWrongGuesses, setHangmanWrongGuesses] = useState<number>(0);
  const [hangmanMaxWrongGuesses, setHangmanMaxWrongGuesses] = useState<number>(6);

  // Komponens unmount eseménykezelő az audio erőforrások felszabadításához
  useEffect(() => {
    return () => {
      if (audioRef.current) {
        audioRef.current.pause();
        if (audioRef.current.src) {
          URL.revokeObjectURL(audioRef.current.src);
        }
      }
    };
  }, []);

  useEffect(() => {
    let timer: NodeJS.Timeout;
    if (timerActive && timeLeft > 0) {
      timer = setInterval(() => {
        setTimeLeft((prev) => prev - 0.1);
      }, 100);
    } else if (timeLeft <= 0 && currentGame === "quickAnswer") {
      handleQuickAnswerTimeout();
    }
    return () => clearInterval(timer);
  }, [timerActive, timeLeft]);

  const startWordMatch = () => {
    const randomWord = vocabularyData[Math.floor(Math.random() * vocabularyData.length)];
    const allWords = vocabularyData.map(item => item.german);
    const shuffledWords = allWords
      .filter(word => word !== randomWord.german)
      .sort(() => Math.random() - 0.5)
      .slice(0, 3);

    setCurrentWord(randomWord.hungarian);
    setOptions([...shuffledWords, randomWord.german].sort(() => Math.random() - 0.5));
    setCurrentGame("wordMatch");
    setIsCorrect(null);
  };

  const checkAnswer = (answer: string) => {
    const correct = vocabularyData.find(item => item.hungarian === currentWord)?.german === answer;
    setIsCorrect(correct);
    if (correct) {
      setScore(prev => prev + 1);
    }
    setTimeout(() => {
      startWordMatch();
    }, 1500);
  };

  const startWordOrder = () => {
    const randomPhrase = phrasesData[Math.floor(Math.random() * phrasesData.length)];
    const words = randomPhrase.german.split(" ");
    setCurrentPhrase(randomPhrase.hungarian);
    setShuffledWords(words.sort(() => Math.random() - 0.5));
    setSelectedWords([]);
    setCurrentGame("wordOrder");
    setIsCorrect(null);
  };

  const handleWordSelection = (word: string) => {
    if (!selectedWords.includes(word)) {
      const newSelected = [...selectedWords, word];
      setSelectedWords(newSelected);
      checkWordOrderCompletion(newSelected);
    }
  };

  // Szó törlése a kiválasztott szavak közül
  const handleWordRemoval = (index: number) => {
    const newSelected = [...selectedWords];
    newSelected.splice(index, 1);
    setSelectedWords(newSelected);
    setIsCorrect(null); // Törléskor visszaállítjuk a helyes/helytelen állapotot
  };

  // Szó mozgatása balra
  const moveWordLeft = (index: number) => {
    if (index > 0) {
      const newSelected = [...selectedWords];
      const temp = newSelected[index];
      newSelected[index] = newSelected[index - 1];
      newSelected[index - 1] = temp;
      setSelectedWords(newSelected);
      setIsCorrect(null); // Mozgatáskor visszaállítjuk a helyes/helytelen állapotot
    }
  };

  // Szó mozgatása jobbra
  const moveWordRight = (index: number) => {
    if (index < selectedWords.length - 1) {
      const newSelected = [...selectedWords];
      const temp = newSelected[index];
      newSelected[index] = newSelected[index + 1];
      newSelected[index + 1] = temp;
      setSelectedWords(newSelected);
      setIsCorrect(null); // Mozgatáskor visszaállítjuk a helyes/helytelen állapotot
    }
  };

  // Összes kiválasztott szó törlése
  const clearSelectedWords = () => {
    setSelectedWords([]);
    setIsCorrect(null);
  };

  // Ellenőrizzük, hogy a mondat teljes-e és helyes-e
  const checkWordOrderCompletion = (words: string[]) => {
    const correctWords = phrasesData.find(item => item.hungarian === currentPhrase)?.german.split(" ") || [];

    if (words.length === shuffledWords.length) {
      const isCorrectOrder = words.join(" ") === correctWords.join(" ");
      setIsCorrect(isCorrectOrder);
      if (isCorrectOrder) {
        setScore(prev => prev + 1);
      }
      setTimeout(() => {
        startWordOrder();
      }, 1500);
    }
  };

  // Ellenőrizzük a jelenlegi sorrendet (manuális ellenőrzés gombhoz)
  const checkCurrentOrder = () => {
    if (selectedWords.length === shuffledWords.length) {
      const correctWords = phrasesData.find(item => item.hungarian === currentPhrase)?.german.split(" ") || [];
      const isCorrectOrder = selectedWords.join(" ") === correctWords.join(" ");
      setIsCorrect(isCorrectOrder);
      if (isCorrectOrder) {
        setScore(prev => prev + 1);
        setTimeout(() => {
          startWordOrder();
        }, 1500);
      }
    }
  };

  const startQuickAnswer = () => {
    const randomWord = vocabularyData[Math.floor(Math.random() * vocabularyData.length)];
    const otherWords = vocabularyData
      .filter((item) => item.german !== randomWord.german)
      .map((item) => item.german)
      .sort(() => Math.random() - 0.5)
      .slice(0, 3);

    setCurrentQuickQuestion({
      word: randomWord.hungarian,
      translation: randomWord.german,
      options: [...otherWords, randomWord.german].sort(() => Math.random() - 0.5),
    });
    setTimeLeft(10);
    setTimerActive(true);
    setCurrentGame("quickAnswer");
    setIsCorrect(null); // Visszaállítjuk az isCorrect állapotot null-ra
  };

  const handleQuickAnswer = (answer: string) => {
    if (!currentQuickQuestion) return;

    const isCorrect = answer === currentQuickQuestion.translation;
    setIsCorrect(isCorrect);

    if (isCorrect) {
      setScore((prev) => prev + Math.ceil(timeLeft));
      setQuickAnswerStreak((prev) => prev + 1);
    } else {
      setQuickAnswerStreak(0);
    }

    setTimeout(() => {
      startQuickAnswer();
    }, 1000);
  };

  const handleQuickAnswerTimeout = () => {
    setIsCorrect(false);
    setQuickAnswerStreak(0);
    setTimerActive(false);
    setTimeout(() => {
      startQuickAnswer();
    }, 1000);
  };

  // Szövegfelolvasás funkció
  const handleSpeech = async (text: string, language: 'hu-HU' | 'de-DE') => {
    try {
      setIsPlayingAudio(true);

      // Ha van korábbi audio, azt leállítjuk és felszabadítjuk
      if (audioRef.current) {
        audioRef.current.pause();
        URL.revokeObjectURL(audioRef.current.src);
      }

      // Lekérjük az audio URL-t az OpenAI API-tól
      const audioUrl = await textToSpeech(text, language);

      // Létrehozunk egy új audio elemet
      const audio = new Audio(audioUrl);
      audioRef.current = audio;

      // Lejátszás befejezésekor frissítjük az állapotot és felszabadítjuk az erőforrást
      audio.onended = () => {
        setIsPlayingAudio(false);
      };

      // Lejátszás
      await audio.play();
    } catch (error) {
      console.error('Hiba a szövegfelolvasás során:', error);
      setIsPlayingAudio(false);
    }
  };

  // Hallás utáni írás játék indítása
  const startDictation = () => {
    // Véletlenszerű szó vagy rövid mondat kiválasztása
    const useVocabulary = Math.random() > 0.5;
    let text = "";

    if (useVocabulary) {
      const randomWord = vocabularyData[Math.floor(Math.random() * vocabularyData.length)];
      text = randomWord.german;
    } else {
      // Rövidebb mondatokat választunk, hogy könnyebb legyen leírni
      const shortPhrases = phrasesData.filter(phrase => phrase.german.split(' ').length <= 5);
      if (shortPhrases.length > 0) {
        const randomPhrase = shortPhrases[Math.floor(Math.random() * shortPhrases.length)];
        text = randomPhrase.german;
      } else {
        const randomPhrase = phrasesData[Math.floor(Math.random() * phrasesData.length)];
        text = randomPhrase.german;
      }
    }

    setDictationText(text);
    setUserInput("");
    setDictationResult(null);
    setCurrentGame("dictation");

    // Automatikusan lejátsszuk a szöveget
    setTimeout(() => {
      handleSpeech(text, 'de-DE');
    }, 500);
  };

  // Hallás utáni írás ellenőrzése
  const checkDictation = () => {
    // Normalizáljuk a szöveget az összehasonlításhoz (kisbetűsítés, felesleges szóközök eltávolítása)
    const normalizedInput = userInput.trim().toLowerCase();
    const normalizedText = dictationText.trim().toLowerCase();

    const isCorrect = normalizedInput === normalizedText;
    setDictationResult(isCorrect ? "correct" : "incorrect");

    if (isCorrect) {
      setScore(prev => prev + 3); // Több pont a hallás utáni írásért
    }

    setTimeout(() => {
      startDictation();
    }, 2000);
  };

  // Szövegkiegészítés játék indítása
  const startGapFill = () => {
    // Véletlenszerű mondat kiválasztása
    const randomPhrase = phrasesData[Math.floor(Math.random() * phrasesData.length)];
    const words = randomPhrase.german.split(" ");

    // Legalább 3 szóból álló mondatokat választunk
    if (words.length < 3) {
      startGapFill(); // Újrapróbáljuk másik mondattal
      return;
    }

    // Véletlenszerűen kiválasztunk egy szót, amit ki kell majd tölteni
    const gapIndex = Math.floor(Math.random() * words.length);
    const gapWord = words[gapIndex];

    // Létrehozzuk a kiürített mondatot
    words[gapIndex] = "_____";
    const gappedPhrase = words.join(" ");

    // Opciók létrehozása (helyes válasz + 3 véletlenszerű szó)
    const allWords = vocabularyData.map(item => item.german);
    const otherOptions = allWords
      .filter(word => word !== gapWord && word.length > 2) // Kizárjuk a túl rövid szavakat
      .sort(() => Math.random() - 0.5)
      .slice(0, 3);

    setGapFillPhrase(gappedPhrase);
    setGapFillOriginal(randomPhrase.german);
    setGapFillAnswer(gapWord);
    setGapFillOptions([...otherOptions, gapWord].sort(() => Math.random() - 0.5));
    setGapFillUserAnswer("");
    setIsCorrect(null);
    setCurrentGame("gapFill");

    // Automatikusan lejátsszuk a teljes mondatot
    setTimeout(() => {
      handleSpeech(randomPhrase.german, 'de-DE');
    }, 500);
  };

  // Szövegkiegészítés ellenőrzése
  const checkGapFill = (answer: string) => {
    setGapFillUserAnswer(answer);
    const isCorrect = answer === gapFillAnswer;
    setIsCorrect(isCorrect);

    if (isCorrect) {
      setScore(prev => prev + 2);
    }

    setTimeout(() => {
      startGapFill();
    }, 2000);
  };

  // Kategória fordítások
  const categoryTranslations: Record<string, string> = {
    "greetings": "Köszönések",
    "basics": "Alapok",
    "food": "Ételek",
    "numbers": "Számok",
    "family": "Család",
    "colors": "Színek",
    "hobbies": "Hobbik",
    "clothing": "Ruházat",
    "emotions": "Érzelmek",
    "places": "Helyek",
    "technology": "Technológia",
    "relationships": "Kapcsolatok",
    "events": "Események",
    "education": "Oktatás",
    "travel": "Utazás",
    "environment": "Környezet",
    "work": "Munka",
    "health": "Egészség",
    "finance": "Pénzügyek",
    "nature": "Természet",
    "logic": "Logika",
    "business": "Üzlet",
    "ethics": "Etika",
    "society": "Társadalom",
    "economics": "Gazdaság",
    "politics": "Politika"
  };

  // Kategória párosítás játék indítása
  const startCategoryMatch = () => {
    // Szókincs adatok csoportosítása kategóriák szerint
    const categories = [...new Set(vocabularyData.map(item => item.category))];

    // Szükségünk van legalább 4 kategóriára
    if (categories.length < 4) {
      console.error("Nincs elég kategória a játékhoz");
      return;
    }

    // Véletlenszerűen kiválasztunk 4 kategóriát
    const shuffledCategories = [...categories].sort(() => Math.random() - 0.5).slice(0, 4);

    // Minden kategóriából kiválasztunk 2 szót (a nehézségi szinttől függően)
    const wordsPerCategory = categoryGameLevel === 1 ? 2 : categoryGameLevel === 2 ? 3 : 4;

    const selectedWords: {word: string, category: string, displayCategory: string}[] = [];

    shuffledCategories.forEach(category => {
      const wordsInCategory = vocabularyData.filter(item => item.category === category);
      const randomWords = [...wordsInCategory].sort(() => Math.random() - 0.5).slice(0, wordsPerCategory);

      randomWords.forEach(word => {
        selectedWords.push({
          word: word.german,
          category: category,
          displayCategory: categoryTranslations[category] || category
        });
      });
    });

    // Állapót beállítása
    setCategoryWords(selectedWords.sort(() => Math.random() - 0.5));
    setAvailableCategories(shuffledCategories);
    setMatchedPairs([]);
    setWrongAttempts([]);
    setSelectedCategory("");
    setSelectedWord("");
    setCurrentGame("categoryMatch");
    setIsCorrect(null);
  };

  // Szó kiválasztása a kategória párosítás játékban
  const handleCategoryWordSelection = (word: string) => {
    // Ha már van kiválasztott kategória, akkor párosítjuk a szóval
    if (selectedCategory) {
      const wordObj = categoryWords.find(w => w.word === word);
      if (wordObj) {
        if (wordObj.category === selectedCategory) {
          // Helyes párosítás
          setMatchedPairs(prev => [...prev, {
            word,
            category: selectedCategory,
            displayCategory: wordObj.displayCategory
          }]);
          setScore(prev => prev + 1);
          // Eltávolítjuk a párosított szót a listából
          setCategoryWords(prev => prev.filter(w => w.word !== word));
        } else {
          // Helytelen párosítás
          const selectedCategoryObj = categoryWords.find(w => w.category === selectedCategory);
          setWrongAttempts(prev => [...prev, {
            word,
            category: selectedCategory,
            displayCategory: selectedCategoryObj?.displayCategory || selectedCategory
          }]);
          setTimeout(() => {
            setWrongAttempts(prev => prev.filter(w => w.word !== word));
          }, 1000);
        }
        setSelectedCategory("");
      }
    } else {
      // Ha nincs kiválasztott kategória, akkor kiválasztjuk a szót
      setSelectedWord(word);
    }
  };

  // Kategória kiválasztása a kategória párosítás játékban
  const handleCategorySelection = (category: string) => {
    // Ha már van kiválasztott szó, akkor párosítjuk a kategóriával
    if (selectedWord) {
      const wordObj = categoryWords.find(w => w.word === selectedWord);
      if (wordObj) {
        if (wordObj.category === category) {
          // Helyes párosítás
          const categoryObj = categoryWords.find(w => w.category === category);
          setMatchedPairs(prev => [...prev, {
            word: selectedWord,
            category,
            displayCategory: categoryObj?.displayCategory || categoryTranslations[category] || category
          }]);
          setScore(prev => prev + 1);
          // Eltávolítjuk a párosított szót a listából
          setCategoryWords(prev => prev.filter(w => w.word !== selectedWord));
        } else {
          // Helytelen párosítás
          setWrongAttempts(prev => [...prev, {
            word: selectedWord,
            category,
            displayCategory: categoryTranslations[category] || category
          }]);
          setTimeout(() => {
            setWrongAttempts(prev => prev.filter(w => w.word !== selectedWord));
          }, 1000);
        }
        setSelectedWord("");
      }
    } else {
      // Ha nincs kiválasztott szó, akkor kiválasztjuk a kategóriát
      setSelectedCategory(category);
    }
  };

  // Ellenőrizzük, hogy minden szó párosítva van-e
  useEffect(() => {
    if (currentGame === "categoryMatch" && categoryWords.length === 0 && matchedPairs.length > 0) {
      // Minden szó párosítva van, játék vége
      setTimeout(() => {
        // Növeljük a nehézségi szintet, ha már legalább 3 kört játszott
        if (matchedPairs.length >= 8 && categoryGameLevel < 3) {
          setCategoryGameLevel(prev => prev + 1);
        }
        startCategoryMatch();
      }, 2000);
    }
  }, [categoryWords, matchedPairs, currentGame, categoryGameLevel]);

  // Anagramma játék indítása
  const startAnagram = () => {
    // Nehézségi szint alapján választunk szót
    // 1. szint: rövid szavak (3-5 betű)
    // 2. szint: közepes szavak (6-8 betű)
    // 3. szint: hosszú szavak (9+ betű)

    let filteredWords = vocabularyData.filter(item => {
      const wordLength = item.german.length;
      if (anagramDifficulty === 1) return wordLength >= 3 && wordLength <= 5 && !item.german.includes(" ");
      if (anagramDifficulty === 2) return wordLength >= 6 && wordLength <= 8 && !item.german.includes(" ");
      return wordLength >= 9 && wordLength <= 12 && !item.german.includes(" ");
    });

    // Ha nincs megfelelő szó, akkor az összes egy szavas szóból választunk
    if (filteredWords.length === 0) {
      filteredWords = vocabularyData.filter(item => !item.german.includes(" "));
    }

    // Véletlenszerűen kiválasztunk egy szót
    const randomWord = filteredWords[Math.floor(Math.random() * filteredWords.length)];
    const word = randomWord.german;

    // Betűk összekeverése
    const letters = word.split("").map(letter => ({ letter, selected: false }));
    const shuffledLetters = [...letters].sort(() => Math.random() - 0.5);

    // Állapot beállítása
    setAnagramOriginal(word);
    setAnagramLetters(shuffledLetters);
    setAnagramUserWord("");
    setAnagramHint(randomWord.hungarian);
    setAnagramAttempts(0);
    setCurrentGame("anagram");
    setIsCorrect(null);
  };

  // Betű kiválasztása az anagramma játékban
  const selectAnagramLetter = (index: number) => {
    if (anagramLetters[index].selected) return;

    // Kiválasztjuk a betűt és hozzáadjuk a felhasználó által összeállított szóhoz
    const newLetters = [...anagramLetters];
    newLetters[index].selected = true;
    setAnagramLetters(newLetters);
    setAnagramUserWord(prev => prev + anagramLetters[index].letter);
  };

  // Anagramma játék újraindítása
  const resetAnagramWord = () => {
    // Visszaállítjuk a betűket és a felhasználó által összeállított szót
    const newLetters = anagramLetters.map(letter => ({ ...letter, selected: false }));
    setAnagramLetters(newLetters);
    setAnagramUserWord("");
  };

  // Anagramma ellenőrzése
  const checkAnagram = () => {
    // Növeljük a próbálkozások számát
    setAnagramAttempts(prev => prev + 1);

    // Ellenőrizzük, hogy a felhasználó által összeállított szó megegyezik-e az eredetivel
    const isCorrect = anagramUserWord === anagramOriginal;
    setIsCorrect(isCorrect);

    if (isCorrect) {
      // Pontszám növelése a nehézségi szint és a próbálkozások száma alapján
      const basePoints = anagramDifficulty * 2;
      const attemptBonus = Math.max(0, 5 - anagramAttempts); // Max 5 pont bónusz, ha elsőre eltalálja
      setScore(prev => prev + basePoints + attemptBonus);

      // Növeljük a nehézségi szintet, ha már legalább 3 szót kitalált az adott szinten
      if (anagramDifficulty < 3 && score > anagramDifficulty * 10) {
        setAnagramDifficulty(prev => prev + 1);
      }

      // Új szó betöltése kis késéssel
      setTimeout(() => {
        startAnagram();
      }, 2000);
    } else {
      // Helytelen válasz esetén visszaállítjuk a betűket
      setTimeout(() => {
        resetAnagramWord();
        setIsCorrect(null);
      }, 1500);
    }
  };

  // Akasztófa játék indítása
  const startHangman = () => {
    // Csak egy szavas szavakat választunk
    const oneWordItems = vocabularyData.filter(item => !item.german.includes(" "));

    // Véletlenszerűen kiválasztunk egy szót
    const randomWord = oneWordItems[Math.floor(Math.random() * oneWordItems.length)];
    const word = randomWord.german.toLowerCase();

    // Állapot beállítása
    setHangmanWord(word);
    setHangmanHint(randomWord.hungarian);
    setHangmanGuessedLetters([]);
    setHangmanWrongGuesses(0);
    setCurrentGame("hangman");
    setIsCorrect(null);
  };

  // Betű kitalálása az akasztófa játékban
  const guessHangmanLetter = (letter: string) => {
    // Ha már kitalálták ezt a betűt, vagy vége a játéknak, akkor nem csinálunk semmit
    if (hangmanGuessedLetters.includes(letter) || hangmanWrongGuesses >= hangmanMaxWrongGuesses) {
      return;
    }

    // Hozzáadjuk a betűt a kitalált betűkhöz
    const newGuessedLetters = [...hangmanGuessedLetters, letter];
    setHangmanGuessedLetters(newGuessedLetters);

    // Ellenőrizzük, hogy a betű benne van-e a szóban
    if (!hangmanWord.includes(letter)) {
      // Ha nincs, növeljük a rossz tippek számát
      const newWrongGuesses = hangmanWrongGuesses + 1;
      setHangmanWrongGuesses(newWrongGuesses);

      // Ellenőrizzük, hogy vége-e a játéknak
      if (newWrongGuesses >= hangmanMaxWrongGuesses) {
        setIsCorrect(false);
        setTimeout(() => {
          startHangman();
        }, 3000);
      }
    } else {
      // Ellenőrizzük, hogy minden betűt kitaláltak-e már
      const isWordGuessed = [...hangmanWord].every(char => newGuessedLetters.includes(char));

      if (isWordGuessed) {
        setIsCorrect(true);
        // Pontszám növelése a szó hossza és a rossz tippek száma alapján
        const basePoints = hangmanWord.length;
        const bonusPoints = Math.max(0, hangmanMaxWrongGuesses - hangmanWrongGuesses) * 2;
        setScore(prev => prev + basePoints + bonusPoints);

        setTimeout(() => {
          startHangman();
        }, 2000);
      }
    }
  };

  return (
    <div className="container mx-auto px-4 py-8 mb-20 md:mb-0 md:pt-24">
      <h1 className="text-2xl font-bold text-center mb-6">Nyelvi játékok</h1>

      {!currentGame ? (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 max-w-5xl mx-auto">
          <Card className="hover:shadow-lg transition-shadow cursor-pointer" onClick={startWordMatch}>
            <CardHeader>
              <CardTitle className="flex items-center justify-between">
                <span>Szópárosítás</span>
                <Gamepad2 className="h-6 w-6 text-language-primary" />
              </CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-gray-600">Válaszd ki a magyar szó német megfelelőjét!</p>
            </CardContent>
          </Card>

          <Card className="hover:shadow-lg transition-shadow cursor-pointer" onClick={startWordOrder}>
            <CardHeader>
              <CardTitle className="flex items-center justify-between">
                <span>Szórend gyakorlás</span>
                <Brain className="h-6 w-6 text-language-secondary" />
              </CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-gray-600">Rakd sorba a szavakat helyes mondatokká!</p>
            </CardContent>
          </Card>

          <Card
            className="hover:shadow-lg transition-shadow cursor-pointer"
            onClick={startQuickAnswer}
          >
            <CardHeader>
              <CardTitle className="flex items-center justify-between">
                <span>Gyors válasz</span>
                <Target className="h-6 w-6 text-language-accent" />
              </CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-gray-600">Válaszolj gyorsan a kérdésekre!</p>
            </CardContent>
          </Card>

          <Card
            className="hover:shadow-lg transition-shadow cursor-pointer"
            onClick={startDictation}
          >
            <CardHeader>
              <CardTitle className="flex items-center justify-between">
                <span>Hallás utáni írás</span>
                <Pencil className="h-6 w-6 text-blue-500" />
              </CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-gray-600">Hallgasd meg és írd le a hallott szöveget!</p>
            </CardContent>
          </Card>

          <Card
            className="hover:shadow-lg transition-shadow cursor-pointer"
            onClick={startGapFill}
          >
            <CardHeader>
              <CardTitle className="flex items-center justify-between">
                <span>Szövegkiegészítés</span>
                <Headphones className="h-6 w-6 text-purple-500" />
              </CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-gray-600">Hallgasd meg és egészítsd ki a hiányzó szót!</p>
            </CardContent>
          </Card>

          <Card
            className="hover:shadow-lg transition-shadow cursor-pointer"
            onClick={startCategoryMatch}
          >
            <CardHeader>
              <CardTitle className="flex items-center justify-between">
                <span>Kategória párosítás</span>
                <Puzzle className="h-6 w-6 text-orange-500" />
              </CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-gray-600">Párosítsd a szavakat a megfelelő kategóriákkal!</p>
            </CardContent>
          </Card>

          <Card
            className="hover:shadow-lg transition-shadow cursor-pointer"
            onClick={startAnagram}
          >
            <CardHeader>
              <CardTitle className="flex items-center justify-between">
                <span>Anagramma</span>
                <AlignJustify className="h-6 w-6 text-blue-500" />
              </CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-gray-600">Rakd ki a szót az összekevert betűkből!</p>
            </CardContent>
          </Card>

          <Card
            className="hover:shadow-lg transition-shadow cursor-pointer"
            onClick={startHangman}
          >
            <CardHeader>
              <CardTitle className="flex items-center justify-between">
                <span>Akasztófa</span>
                <CaseUpper className="h-6 w-6 text-purple-500" />
              </CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-gray-600">Találd ki a szót betűnként!</p>
            </CardContent>
          </Card>

          <Card
            className="hover:shadow-lg transition-shadow cursor-pointer"
            onClick={() => window.location.href = '/grammar/a1'}
          >
            <CardHeader>
              <CardTitle className="flex items-center justify-between">
                <span>Nyelvtani gyakorlatok (A1)</span>
                <Layers className="h-6 w-6 text-green-500" />
              </CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-gray-600">Gyakorold a német nyelvtant interaktív feladatokkal!</p>
            </CardContent>
          </Card>
        </div>
      ) : currentGame === "wordMatch" ? (
        <div className="max-w-xl mx-auto">
          <Card>
            <CardContent className="pt-6">
              <div className="text-center mb-6">
                <p className="text-sm text-gray-500 mb-2">Pontszám: {score}</p>
                <h2 className="text-2xl font-bold mb-4">{currentWord}</h2>
                <p className="text-gray-600 mb-4">Válaszd ki a helyes német fordítást!</p>
              </div>

              <div className="grid grid-cols-2 gap-4">
                {options.map((option, index) => (
                  <Button
                    key={index}
                    onClick={() => checkAnswer(option)}
                    variant={
                      isCorrect === null
                        ? "outline"
                        : vocabularyData.find(item => item.hungarian === currentWord)?.german === option
                        ? "default"
                        : "outline"
                    }
                    className={
                      isCorrect !== null &&
                      vocabularyData.find(item => item.hungarian === currentWord)?.german === option
                        ? "bg-green-500 hover:bg-green-600"
                        : ""
                    }
                  >
                    {option}
                  </Button>
                ))}
              </div>

              <div className="mt-6 text-center">
                <Button variant="ghost" onClick={() => {
                  setCurrentGame(null);
                  setScore(0);
                }}>
                  Vissza a játékokhoz
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>
      ) : currentGame === "wordOrder" ? (
        <div className="max-w-xl mx-auto">
          <Card>
            <CardContent className="pt-6">
              <div className="text-center mb-6">
                <p className="text-sm text-gray-500 mb-2">Pontszám: {score}</p>
                <h2 className="text-2xl font-bold mb-4">{currentPhrase}</h2>
                <p className="text-gray-600 mb-4">Rakd sorba a szavakat a helyes német mondathoz!</p>
              </div>

              <div className="mb-6">
                <div className="p-4 bg-gray-100 rounded-lg min-h-[60px] mb-4">
                  {selectedWords.length > 0 ? (
                    <div className="flex flex-wrap gap-2">
                      {selectedWords.map((word, index) => (
                        <div key={index} className="group relative">
                          <div className="px-3 py-1 bg-language-primary text-white rounded flex items-center gap-1">
                            <span>{word}</span>
                            <Button
                              variant="ghost"
                              size="icon"
                              className="h-5 w-5 p-0 text-white hover:bg-white/20 rounded-full"
                              onClick={(e) => {
                                e.stopPropagation();
                                handleWordRemoval(index);
                              }}
                            >
                              <X className="h-3 w-3" />
                            </Button>
                          </div>
                          <div className="absolute -bottom-6 left-0 right-0 hidden group-hover:flex justify-center gap-1 z-10">
                            <Button
                              variant="secondary"
                              size="icon"
                              className="h-5 w-5 p-0 bg-white shadow-sm"
                              onClick={() => moveWordLeft(index)}
                              disabled={index === 0}
                            >
                              <ArrowLeft className="h-3 w-3" />
                            </Button>
                            <Button
                              variant="secondary"
                              size="icon"
                              className="h-5 w-5 p-0 bg-white shadow-sm"
                              onClick={() => moveWordRight(index)}
                              disabled={index === selectedWords.length - 1}
                            >
                              <ArrowRight className="h-3 w-3" />
                            </Button>
                          </div>
                        </div>
                      ))}
                    </div>
                  ) : (
                    <p className="text-gray-500 text-center">Válassz szavakat a listából</p>
                  )}
                </div>

                <div className="flex justify-between items-center mb-3">
                  <Button
                    variant="outline"
                    size="sm"
                    className="text-gray-500"
                    onClick={clearSelectedWords}
                    disabled={selectedWords.length === 0}
                  >
                    <RotateCcw className="h-3 w-3 mr-1" />
                    Ürítés
                  </Button>

                  <Button
                    variant="outline"
                    size="sm"
                    className="text-language-primary"
                    onClick={checkCurrentOrder}
                    disabled={selectedWords.length !== shuffledWords.length || isCorrect !== null}
                  >
                    Ellenőrzés
                  </Button>
                </div>

                <div className="flex flex-wrap gap-2">
                  {shuffledWords.map((word, index) => (
                    <Button
                      key={index}
                      onClick={() => handleWordSelection(word)}
                      variant="outline"
                      className={cn(
                        "transition-opacity",
                        selectedWords.includes(word) && "opacity-50 cursor-not-allowed"
                      )}
                      disabled={selectedWords.includes(word)}
                    >
                      {word}
                    </Button>
                  ))}
                </div>

                {isCorrect !== null && (
                  <div className={`mt-4 p-3 rounded-md ${isCorrect ? "bg-green-100" : "bg-red-100"}`}>
                    <p className="font-medium">
                      {isCorrect ? "Helyes! 😄" : "Nem helyes 😢"}
                    </p>
                    {!isCorrect && (
                      <p className="text-sm mt-1">
                        Próbáld újrarendezni a szavakat, vagy kezdj új mondatot.
                      </p>
                    )}
                  </div>
                )}
              </div>

              <div className="mt-6 text-center">
                <Button
                  variant="ghost"
                  onClick={() => {
                    setCurrentGame(null);
                    setScore(0);
                  }}
                >
                  Vissza a játékokhoz
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>
      ) : currentGame === "quickAnswer" ? (
        <div className="max-w-xl mx-auto">
          <Card>
            <CardContent className="pt-6">
              <div className="text-center mb-6">
                <div className="flex items-center justify-between mb-4">
                  <p className="text-sm text-gray-500">Pontszám: {score}</p>
                  <p className="text-sm text-gray-500">Sorozat: {quickAnswerStreak}</p>
                </div>

                <Progress
                  value={timeLeft * 10}
                  className={cn(
                    "mb-4",
                    timeLeft < 3 ? "[&>div]:bg-red-500" :
                    timeLeft < 5 ? "[&>div]:bg-yellow-500" :
                    "[&>div]:bg-green-500"
                  )}
                />

                <div className="flex items-center justify-center gap-2 mb-4">
                  <Timer className="h-5 w-5" />
                  <span className="font-bold">{Math.ceil(timeLeft)}s</span>
                </div>

                <h2 className="text-2xl font-bold mb-4">
                  {currentQuickQuestion?.word}
                </h2>
                <p className="text-gray-600 mb-4">
                  Válaszd ki gyorsan a helyes német fordítást!
                </p>
              </div>

              <div className="grid grid-cols-2 gap-4">
                {currentQuickQuestion?.options.map((option, index) => (
                  <Button
                    key={index}
                    onClick={() => handleQuickAnswer(option)}
                    variant={
                      isCorrect === null
                        ? "outline"
                        : currentQuickQuestion.translation === option
                        ? "default"
                        : "outline"
                    }
                    className={cn(
                      isCorrect !== null &&
                      currentQuickQuestion.translation === option &&
                      "bg-green-500 hover:bg-green-600"
                    )}
                  >
                    {option}
                  </Button>
                ))}
              </div>

              <div className="mt-6 text-center">
                <Button
                  variant="ghost"
                  onClick={() => {
                    setCurrentGame(null);
                    setScore(0);
                    setTimerActive(false);
                    setQuickAnswerStreak(0);
                  }}
                >
                  Vissza a játékokhoz
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>
      ) : currentGame === "dictation" ? (
        <div className="max-w-xl mx-auto">
          <Card>
            <CardContent className="pt-6">
              <div className="text-center mb-6">
                <p className="text-sm text-gray-500 mb-2">Pontszám: {score}</p>
                <h2 className="text-2xl font-bold mb-4">Hallás utáni írás</h2>
                <p className="text-gray-600 mb-4">Hallgasd meg és írd le a hallott szöveget!</p>
              </div>

              <div className="mb-6">
                <Button
                  onClick={() => handleSpeech(dictationText, 'de-DE')}
                  className="w-full mb-4"
                  disabled={isPlayingAudio}
                >
                  {isPlayingAudio ? (
                    <>
                      <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                      <span>Lejátszás...</span>
                    </>
                  ) : (
                    <>
                      <Volume2 className="h-4 w-4 mr-2" />
                      <span>Hallgasd meg újra</span>
                    </>
                  )}
                </Button>

                <Input
                  placeholder="Írd le, amit hallottál..."
                  value={userInput}
                  onChange={(e) => setUserInput(e.target.value)}
                  className="mb-4"
                  disabled={dictationResult !== null}
                />

                <Button
                  onClick={checkDictation}
                  className="w-full"
                  disabled={userInput.trim() === "" || dictationResult !== null}
                >
                  Ellenőrzés
                </Button>

                {dictationResult && (
                  <div className={`mt-4 p-3 rounded-md ${dictationResult === "correct" ? "bg-green-100" : "bg-red-100"}`}>
                    <p className="font-medium">
                      {dictationResult === "correct" ? "Helyes! 😄" : "Nem helyes 😢"}
                    </p>
                    <p className="text-sm mt-1">
                      A helyes válasz: <span className="font-bold">{dictationText}</span>
                    </p>
                  </div>
                )}
              </div>

              <div className="mt-6 text-center">
                <Button
                  variant="ghost"
                  onClick={() => {
                    setCurrentGame(null);
                    setScore(0);
                  }}
                >
                  Vissza a játékokhoz
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>
      ) : currentGame === "gapFill" ? (
        <div className="max-w-xl mx-auto">
          <Card>
            <CardContent className="pt-6">
              <div className="text-center mb-6">
                <p className="text-sm text-gray-500 mb-2">Pontszám: {score}</p>
                <h2 className="text-2xl font-bold mb-4">Szövegkiegészítés</h2>
                <p className="text-gray-600 mb-4">Hallgasd meg és válaszd ki a hiányzó szót!</p>
              </div>

              <div className="mb-6">
                <Button
                  onClick={() => handleSpeech(gapFillOriginal, 'de-DE')}
                  className="w-full mb-4"
                  disabled={isPlayingAudio}
                >
                  {isPlayingAudio ? (
                    <>
                      <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                      <span>Lejátszás...</span>
                    </>
                  ) : (
                    <>
                      <Volume2 className="h-4 w-4 mr-2" />
                      <span>Hallgasd meg újra</span>
                    </>
                  )}
                </Button>

                <div className="p-4 bg-gray-100 rounded-lg mb-4">
                  <p className="text-lg text-center">{gapFillPhrase}</p>
                </div>

                <div className="grid grid-cols-2 gap-4">
                  {gapFillOptions.map((option, index) => (
                    <Button
                      key={index}
                      onClick={() => checkGapFill(option)}
                      variant={isCorrect === null ? "outline" : gapFillAnswer === option ? "default" : "outline"}
                      className={cn(
                        isCorrect !== null && gapFillAnswer === option && "bg-green-500 hover:bg-green-600"
                      )}
                      disabled={gapFillUserAnswer !== ""}
                    >
                      {option}
                    </Button>
                  ))}
                </div>

                {gapFillUserAnswer && (
                  <div className={`mt-4 p-3 rounded-md ${isCorrect ? "bg-green-100" : "bg-red-100"}`}>
                    <p className="font-medium">
                      {isCorrect ? "Helyes! 😄" : "Nem helyes 😢"}
                    </p>
                    <p className="text-sm mt-1">
                      A helyes mondat: <span className="font-bold">{gapFillOriginal}</span>
                    </p>
                  </div>
                )}
              </div>

              <div className="mt-6 text-center">
                <Button
                  variant="ghost"
                  onClick={() => {
                    setCurrentGame(null);
                    setScore(0);
                  }}
                >
                  Vissza a játékokhoz
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>
      ) : currentGame === "categoryMatch" ? (
        <div className="max-w-4xl mx-auto">
          <Card>
            <CardHeader>
              <div className="flex items-center justify-between">
                <CardTitle className="flex items-center gap-2">
                  <Puzzle className="h-6 w-6 text-orange-500" />
                  <span>Kategória párosítás</span>
                </CardTitle>
                <Badge variant="outline" className="bg-orange-50 text-orange-700">
                  Szint: {categoryGameLevel}
                </Badge>
              </div>
              <CardDescription>
                Párosítsd a szavakat a megfelelő kategóriákkal! Válassz először egy szót vagy egy kategóriát, majd válaszd ki a párját.
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="text-center mb-4">
                <p className="text-sm text-gray-500 mb-2">Pontszám: {score}</p>
                <p className="text-sm text-gray-500 mb-4">Hátralévő szavak: {categoryWords.length}</p>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {/* Szavak */}
                <div>
                  <h3 className="text-lg font-medium mb-3">Szavak</h3>
                  <div className="grid grid-cols-2 gap-2">
                    {categoryWords.map((item, index) => (
                      <Button
                        key={index}
                        variant={selectedWord === item.word ? "default" : "outline"}
                        className={cn(
                          "transition-all",
                          selectedWord === item.word && "bg-orange-500 hover:bg-orange-600",
                          wrongAttempts.some(w => w.word === item.word) && "bg-red-500 hover:bg-red-600 text-white"
                        )}
                        onClick={() => handleCategoryWordSelection(item.word)}
                      >
                        {item.word}
                      </Button>
                    ))}
                  </div>
                </div>

                {/* Kategóriák */}
                <div>
                  <h3 className="text-lg font-medium mb-3">Kategóriák</h3>
                  <div className="grid grid-cols-2 gap-2">
                    {availableCategories.map((category, index) => (
                      <Button
                        key={index}
                        variant={selectedCategory === category ? "default" : "outline"}
                        className={cn(
                          "transition-all",
                          selectedCategory === category && "bg-orange-500 hover:bg-orange-600",
                          wrongAttempts.some(w => w.category === category) && "bg-red-500 hover:bg-red-600 text-white"
                        )}
                        onClick={() => handleCategorySelection(category)}
                      >
                        {categoryTranslations[category] || category}
                      </Button>
                    ))}
                  </div>
                </div>
              </div>

              {/* Párosított elemek */}
              {matchedPairs.length > 0 && (
                <div className="mt-6">
                  <h3 className="text-lg font-medium mb-3">Sikeres párosítások</h3>
                  <div className="bg-green-50 p-4 rounded-lg">
                    <div className="grid grid-cols-2 gap-4">
                      {matchedPairs.map((pair, index) => (
                        <div key={index} className="flex items-center justify-between bg-white p-2 rounded border border-green-200">
                          <span className="font-medium">{pair.word}</span>
                          <div className="flex items-center">
                            <ArrowRight className="h-4 w-4 mx-2 text-green-500" />
                            <Badge variant="outline" className="bg-green-100 text-green-700">
                              {pair.displayCategory}
                            </Badge>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                </div>
              )}

              {categoryWords.length === 0 && matchedPairs.length > 0 && (
                <div className="mt-6 bg-orange-100 p-4 rounded-lg text-center">
                  <h3 className="text-lg font-medium mb-2 text-orange-800">Szint teljesítve! 🎉</h3>
                  <p className="text-orange-700">Következő szint betöltése...</p>
                </div>
              )}
            </CardContent>
            <CardFooter className="flex justify-center">
              <Button
                variant="ghost"
                onClick={() => {
                  setCurrentGame(null);
                  setScore(0);
                  setCategoryGameLevel(1);
                }}
              >
                Vissza a játékokhoz
              </Button>
            </CardFooter>
          </Card>
        </div>
      ) : currentGame === "anagram" ? (
        <div className="max-w-xl mx-auto">
          <Card>
            <CardHeader>
              <div className="flex items-center justify-between">
                <CardTitle className="flex items-center gap-2">
                  <AlignJustify className="h-6 w-6 text-blue-500" />
                  <span>Anagramma</span>
                </CardTitle>
                <Badge variant="outline" className="bg-blue-50 text-blue-700">
                  Szint: {anagramDifficulty}
                </Badge>
              </div>
              <CardDescription>
                Rakd ki a szót az összekevert betűkből! A segítség a szó magyar jelentése.
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="text-center mb-6">
                <p className="text-sm text-gray-500 mb-2">Pontszám: {score}</p>
                <p className="text-sm text-gray-500 mb-2">Próbálkozások: {anagramAttempts}</p>
                <div className="bg-blue-50 p-3 rounded-lg mb-4">
                  <p className="font-medium text-blue-800">Segítség: {anagramHint}</p>
                </div>
              </div>

              {/* Felhasználó által összeállított szó */}
              <div className="bg-white border-2 border-blue-200 rounded-lg p-4 mb-6 min-h-16 flex items-center justify-center">
                <p className="text-2xl font-bold tracking-wider">
                  {anagramUserWord || "_ _ _ _ _"}
                </p>
              </div>

              {/* Visszajelzés */}
              {isCorrect !== null && (
                <div className={`p-3 rounded-lg mb-6 text-center ${isCorrect ? "bg-green-100 text-green-800" : "bg-red-100 text-red-800"}`}>
                  {isCorrect ? (
                    <p className="font-medium">Helyes! 🎉</p>
                  ) : (
                    <p className="font-medium">Helytelen! Próbáld újra.</p>
                  )}
                </div>
              )}

              {/* Betűk */}
              <div className="grid grid-cols-5 gap-2 mb-6">
                {anagramLetters.map((letterObj, index) => (
                  <Button
                    key={index}
                    variant="outline"
                    className={cn(
                      "text-lg font-bold h-12",
                      letterObj.selected ? "opacity-50 cursor-not-allowed" : "hover:bg-blue-100"
                    )}
                    disabled={letterObj.selected || isCorrect !== null}
                    onClick={() => selectAnagramLetter(index)}
                  >
                    {letterObj.letter}
                  </Button>
                ))}
              </div>

              <div className="flex gap-2 justify-center">
                <Button
                  variant="outline"
                  className="w-1/3"
                  onClick={resetAnagramWord}
                  disabled={anagramUserWord === "" || isCorrect !== null}
                >
                  <RotateCcw className="h-4 w-4 mr-2" />
                  Újrakezdés
                </Button>
                <Button
                  className="w-1/3 bg-blue-500 hover:bg-blue-600"
                  onClick={checkAnagram}
                  disabled={anagramUserWord === "" || isCorrect !== null}
                >
                  Ellenőrzés
                </Button>
              </div>
            </CardContent>
            <CardFooter className="flex justify-center">
              <Button
                variant="ghost"
                onClick={() => {
                  setCurrentGame(null);
                  setScore(0);
                  setAnagramDifficulty(1);
                }}
              >
                Vissza a játékokhoz
              </Button>
            </CardFooter>
          </Card>
        </div>
      ) : currentGame === "hangman" ? (
        <div className="max-w-xl mx-auto">
          <Card>
            <CardHeader>
              <div className="flex items-center justify-between">
                <CardTitle className="flex items-center gap-2">
                  <CaseUpper className="h-6 w-6 text-purple-500" />
                  <span>Akasztófa</span>
                </CardTitle>
              </div>
              <CardDescription>
                Találd ki a szót betűnként! A segítség a szó magyar jelentése.
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="text-center mb-6">
                <p className="text-sm text-gray-500 mb-2">Pontszám: {score}</p>
                <p className="text-sm text-gray-500 mb-2">Hátralévő próbálkozások: {hangmanMaxWrongGuesses - hangmanWrongGuesses}</p>
                <div className="bg-purple-50 p-3 rounded-lg mb-4">
                  <p className="font-medium text-purple-800">Segítség: {hangmanHint}</p>
                </div>
              </div>

              {/* Akasztófa ábrája */}
              <div className="flex justify-center mb-6">
                <div className="w-32 h-32 relative">
                  {/* Akasztófa rajzolása a rossz tippek száma alapján */}
                  <svg viewBox="0 0 100 100" className="w-full h-full">
                    {/* Akasztófa alapja */}
                    {hangmanWrongGuesses >= 1 && <line x1="10" y1="90" x2="90" y2="90" stroke="#6b7280" strokeWidth="2" />}
                    {/* Függőleges rúd */}
                    {hangmanWrongGuesses >= 2 && <line x1="30" y1="90" x2="30" y2="10" stroke="#6b7280" strokeWidth="2" />}
                    {/* Vízszintes rúd */}
                    {hangmanWrongGuesses >= 3 && <line x1="30" y1="10" x2="70" y2="10" stroke="#6b7280" strokeWidth="2" />}
                    {/* Kötél */}
                    {hangmanWrongGuesses >= 4 && <line x1="70" y1="10" x2="70" y2="20" stroke="#6b7280" strokeWidth="2" />}
                    {/* Fej */}
                    {hangmanWrongGuesses >= 5 && <circle cx="70" cy="30" r="10" stroke="#6b7280" strokeWidth="2" fill="none" />}
                    {/* Test */}
                    {hangmanWrongGuesses >= 6 && <line x1="70" y1="40" x2="70" y2="60" stroke="#6b7280" strokeWidth="2" />}
                    {/* Bal kar */}
                    {hangmanWrongGuesses >= 7 && <line x1="70" y1="45" x2="60" y2="55" stroke="#6b7280" strokeWidth="2" />}
                    {/* Jobb kar */}
                    {hangmanWrongGuesses >= 8 && <line x1="70" y1="45" x2="80" y2="55" stroke="#6b7280" strokeWidth="2" />}
                    {/* Bal láb */}
                    {hangmanWrongGuesses >= 9 && <line x1="70" y1="60" x2="60" y2="75" stroke="#6b7280" strokeWidth="2" />}
                    {/* Jobb láb */}
                    {hangmanWrongGuesses >= 10 && <line x1="70" y1="60" x2="80" y2="75" stroke="#6b7280" strokeWidth="2" />}
                  </svg>
                </div>
              </div>

              {/* Kitalálandó szó */}
              <div className="flex justify-center gap-1 mb-6">
                {[...hangmanWord].map((letter, index) => (
                  <div
                    key={index}
                    className="w-8 h-10 border-b-2 border-purple-500 flex items-center justify-center"
                  >
                    <span className="text-xl font-bold">
                      {hangmanGuessedLetters.includes(letter) ? letter : ""}
                    </span>
                  </div>
                ))}
              </div>

              {/* Visszajelzés */}
              {isCorrect !== null && (
                <div className={`p-3 rounded-lg mb-6 text-center ${isCorrect ? "bg-green-100 text-green-800" : "bg-red-100 text-red-800"}`}>
                  {isCorrect ? (
                    <p className="font-medium">Gratulálok! Kitaláltad a szót! 🎉</p>
                  ) : (
                    <p className="font-medium">Sajnos nem sikerült! A szó ez volt: <span className="font-bold">{hangmanWord}</span></p>
                  )}
                </div>
              )}

              {/* Betűk */}
              <div className="grid grid-cols-7 gap-2">
                {"abcdefghijklmnopqrstuvwxyzäöüß".split("").map((letter) => (
                  <Button
                    key={letter}
                    variant="outline"
                    size="sm"
                    className={cn(
                      "font-bold",
                      hangmanGuessedLetters.includes(letter) && hangmanWord.includes(letter) && "bg-green-100 text-green-800 border-green-300",
                      hangmanGuessedLetters.includes(letter) && !hangmanWord.includes(letter) && "bg-red-100 text-red-800 border-red-300",
                      hangmanGuessedLetters.includes(letter) && "cursor-not-allowed"
                    )}
                    disabled={hangmanGuessedLetters.includes(letter) || isCorrect !== null || hangmanWrongGuesses >= hangmanMaxWrongGuesses}
                    onClick={() => guessHangmanLetter(letter)}
                  >
                    {letter}
                  </Button>
                ))}
              </div>
            </CardContent>
            <CardFooter className="flex justify-center">
              <Button
                variant="ghost"
                onClick={() => {
                  setCurrentGame(null);
                  setScore(0);
                }}
              >
                Vissza a játékokhoz
              </Button>
            </CardFooter>
          </Card>
        </div>
      ) : null}
    </div>
  );
};

export default Games;
