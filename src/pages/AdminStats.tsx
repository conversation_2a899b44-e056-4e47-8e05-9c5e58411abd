import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { toast } from 'sonner';
import { RefreshCw, Users, CreditCard, MessageSquare, Calendar } from 'lucide-react';
import apiService from '@/services/apiService';
import { Skeleton } from '@/components/ui/skeleton';

interface StatsData {
  users: {
    total: number;
    activeSubscriptions: number;
    registrations: {
      today: number;
      weekly: number;
      monthly: number;
    };
  };
  tickets: {
    total: number;
    open: number;
    inProgress: number;
    closed: number;
  };
  points: {
    totalTransactions: number;
    totalAdded: number;
    totalUsed: number;
    balance: number;
  };
}

const AdminStats: React.FC = () => {
  const [stats, setStats] = useState<StatsData | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  // Statisztikák lekérése
  const fetchStats = async () => {
    try {
      setIsLoading(true);
      const response = await apiService.get('/admin/stats');
      setStats(response.data.data);
    } catch (error) {
      console.error('Hiba a statisztikák lekérésekor:', error);
      toast.error('Nem sikerült lekérni a statisztikákat');
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    fetchStats();
  }, []);

  // Skeleton loader a statisztikákhoz
  const StatSkeleton = () => (
    <div className="space-y-2">
      <Skeleton className="h-10 w-[100px]" />
      <Skeleton className="h-4 w-[150px]" />
    </div>
  );

  return (
    <div className="container mx-auto py-8">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold">Statisztikák</h1>
        <Button 
          variant="outline" 
          size="sm" 
          onClick={fetchStats} 
          disabled={isLoading}
        >
          <RefreshCw className={`h-4 w-4 mr-2 ${isLoading ? 'animate-spin' : ''}`} />
          Frissítés
        </Button>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        {/* Felhasználók */}
        <Card>
          <CardHeader className="flex flex-row items-center justify-between pb-2">
            <CardTitle className="text-sm font-medium">Felhasználók</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            {isLoading ? (
              <StatSkeleton />
            ) : stats ? (
              <>
                <div className="text-2xl font-bold">{stats.users.total}</div>
                <p className="text-xs text-muted-foreground">
                  {stats.users.activeSubscriptions} aktív előfizetés
                </p>
              </>
            ) : (
              <div className="text-sm text-muted-foreground">Nincs adat</div>
            )}
          </CardContent>
        </Card>

        {/* Új regisztrációk */}
        <Card>
          <CardHeader className="flex flex-row items-center justify-between pb-2">
            <CardTitle className="text-sm font-medium">Új regisztrációk</CardTitle>
            <Calendar className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            {isLoading ? (
              <StatSkeleton />
            ) : stats ? (
              <>
                <div className="text-2xl font-bold">{stats.users.registrations.monthly}</div>
                <div className="pt-2 flex items-center text-xs text-muted-foreground">
                  <div className="w-full grid grid-cols-2 gap-2">
                    <div>
                      <span className="font-medium">Ma:</span> {stats.users.registrations.today}
                    </div>
                    <div>
                      <span className="font-medium">Héten:</span> {stats.users.registrations.weekly}
                    </div>
                  </div>
                </div>
              </>
            ) : (
              <div className="text-sm text-muted-foreground">Nincs adat</div>
            )}
          </CardContent>
        </Card>

        {/* Support ticketek */}
        <Card>
          <CardHeader className="flex flex-row items-center justify-between pb-2">
            <CardTitle className="text-sm font-medium">Support ticketek</CardTitle>
            <MessageSquare className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            {isLoading ? (
              <StatSkeleton />
            ) : stats ? (
              <>
                <div className="text-2xl font-bold">{stats.tickets.total}</div>
                <div className="pt-2 flex items-center text-xs text-muted-foreground">
                  <div className="w-full grid grid-cols-3 gap-2">
                    <div>
                      <span className="font-medium">Nyitott:</span> {stats.tickets.open}
                    </div>
                    <div>
                      <span className="font-medium">Folyamatban:</span> {stats.tickets.inProgress}
                    </div>
                    <div>
                      <span className="font-medium">Lezárt:</span> {stats.tickets.closed}
                    </div>
                  </div>
                </div>
              </>
            ) : (
              <div className="text-sm text-muted-foreground">Nincs adat</div>
            )}
          </CardContent>
        </Card>

        {/* Pontok */}
        <Card>
          <CardHeader className="flex flex-row items-center justify-between pb-2">
            <CardTitle className="text-sm font-medium">Pontok</CardTitle>
            <CreditCard className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            {isLoading ? (
              <StatSkeleton />
            ) : stats ? (
              <>
                <div className="text-2xl font-bold">{stats.points.balance.toLocaleString()}</div>
                <div className="pt-2 flex items-center text-xs text-muted-foreground">
                  <div className="w-full grid grid-cols-2 gap-2">
                    <div>
                      <span className="font-medium">Kiosztva:</span> {stats.points.totalAdded.toLocaleString()}
                    </div>
                    <div>
                      <span className="font-medium">Felhasználva:</span> {Math.abs(stats.points.totalUsed).toLocaleString()}
                    </div>
                  </div>
                </div>
              </>
            ) : (
              <div className="text-sm text-muted-foreground">Nincs adat</div>
            )}
          </CardContent>
        </Card>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {/* Felhasználói statisztikák */}
        <Card>
          <CardHeader>
            <CardTitle>Felhasználói statisztikák</CardTitle>
            <CardDescription>
              Részletes felhasználói adatok
            </CardDescription>
          </CardHeader>
          <CardContent>
            {isLoading ? (
              <div className="space-y-4">
                {Array.from({ length: 4 }).map((_, index) => (
                  <div key={index} className="flex justify-between">
                    <Skeleton className="h-4 w-[150px]" />
                    <Skeleton className="h-4 w-[100px]" />
                  </div>
                ))}
              </div>
            ) : stats ? (
              <div className="space-y-4">
                <div className="flex justify-between items-center border-b pb-2">
                  <span className="font-medium">Összes felhasználó</span>
                  <span>{stats.users.total}</span>
                </div>
                <div className="flex justify-between items-center border-b pb-2">
                  <span className="font-medium">Aktív előfizetők</span>
                  <span>{stats.users.activeSubscriptions}</span>
                </div>
                <div className="flex justify-between items-center border-b pb-2">
                  <span className="font-medium">Előfizetési arány</span>
                  <span>
                    {stats.users.total > 0 
                      ? `${((stats.users.activeSubscriptions / stats.users.total) * 100).toFixed(1)}%` 
                      : '0%'}
                  </span>
                </div>
                <div className="flex justify-between items-center border-b pb-2">
                  <span className="font-medium">Mai regisztrációk</span>
                  <span>{stats.users.registrations.today}</span>
                </div>
                <div className="flex justify-between items-center border-b pb-2">
                  <span className="font-medium">Heti regisztrációk</span>
                  <span>{stats.users.registrations.weekly}</span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="font-medium">Havi regisztrációk</span>
                  <span>{stats.users.registrations.monthly}</span>
                </div>
              </div>
            ) : (
              <div className="text-center py-6 text-gray-500">
                <p>Nem sikerült betölteni a statisztikákat</p>
              </div>
            )}
          </CardContent>
        </Card>

        {/* Support statisztikák */}
        <Card>
          <CardHeader>
            <CardTitle>Support statisztikák</CardTitle>
            <CardDescription>
              Részletes support adatok
            </CardDescription>
          </CardHeader>
          <CardContent>
            {isLoading ? (
              <div className="space-y-4">
                {Array.from({ length: 4 }).map((_, index) => (
                  <div key={index} className="flex justify-between">
                    <Skeleton className="h-4 w-[150px]" />
                    <Skeleton className="h-4 w-[100px]" />
                  </div>
                ))}
              </div>
            ) : stats ? (
              <div className="space-y-4">
                <div className="flex justify-between items-center border-b pb-2">
                  <span className="font-medium">Összes ticket</span>
                  <span>{stats.tickets.total}</span>
                </div>
                <div className="flex justify-between items-center border-b pb-2">
                  <span className="font-medium">Nyitott ticketek</span>
                  <span>{stats.tickets.open}</span>
                </div>
                <div className="flex justify-between items-center border-b pb-2">
                  <span className="font-medium">Folyamatban lévő ticketek</span>
                  <span>{stats.tickets.inProgress}</span>
                </div>
                <div className="flex justify-between items-center border-b pb-2">
                  <span className="font-medium">Lezárt ticketek</span>
                  <span>{stats.tickets.closed}</span>
                </div>
                <div className="flex justify-between items-center border-b pb-2">
                  <span className="font-medium">Megoldási arány</span>
                  <span>
                    {stats.tickets.total > 0 
                      ? `${((stats.tickets.closed / stats.tickets.total) * 100).toFixed(1)}%` 
                      : '0%'}
                  </span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="font-medium">Aktív ticketek aránya</span>
                  <span>
                    {stats.tickets.total > 0 
                      ? `${(((stats.tickets.open + stats.tickets.inProgress) / stats.tickets.total) * 100).toFixed(1)}%` 
                      : '0%'}
                  </span>
                </div>
              </div>
            ) : (
              <div className="text-center py-6 text-gray-500">
                <p>Nem sikerült betölteni a statisztikákat</p>
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default AdminStats;
