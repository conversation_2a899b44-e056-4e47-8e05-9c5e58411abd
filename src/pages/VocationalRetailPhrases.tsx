import React, { useState, useRef } from "react";
import { <PERSON> } from "react-router-dom";
import { retailPhrases } from "@/data/vocational/retail";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle, CardDescription, CardFooter } from "@/components/ui/card";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from "@/components/ui/select";
import { Volume2, ChevronLeft, ChevronRight, Loader2, ShoppingBag, Briefcase, Book, Gamepad2 } from "lucide-react";
import { Badge } from "@/components/ui/badge";
import { textToSpeech } from "@/services/openaiService";

const VocationalRetailPhrases: React.FC = () => {
  const [filter, setFilter] = useState("all");
  const [currentIndex, setCurrentIndex] = useState(0);
  const [showTranslation, setShowTranslation] = useState(false);
  const [isPlayingHungarian, setIsPlayingHungarian] = useState(false);
  const [isPlayingGerman, setIsPlayingGerman] = useState(false);
  const audioRef = useRef<HTMLAudioElement | null>(null);

  // Kategóriák kinyerése
  const categories = retailPhrases.map(category => category.id);

  // Szűrés kategória alapján
  const filteredPhrases = filter === "all"
    ? retailPhrases.flatMap(category => category.items)
    : retailPhrases.find(category => category.id === filter)?.items || [];

  const currentPhrase = filteredPhrases[currentIndex];

  // Navigáció a mondatok között
  const goToNext = () => {
    if (currentIndex < filteredPhrases.length - 1) {
      setCurrentIndex(currentIndex + 1);
      setShowTranslation(false);
    }
  };

  const goToPrevious = () => {
    if (currentIndex > 0) {
      setCurrentIndex(currentIndex - 1);
      setShowTranslation(false);
    }
  };

  // Szövegfelolvasás funkció
  const handleSpeech = async (text: string, language: 'hu-HU' | 'de-DE') => {
    try {
      // Beállítjuk a megfelelő loading állapotot
      if (language === 'hu-HU') {
        setIsPlayingHungarian(true);
      } else {
        setIsPlayingGerman(true);
      }

      // Ha van korábbi audio, azt leállítjuk és felszabadítjuk
      if (audioRef.current) {
        audioRef.current.pause();
        URL.revokeObjectURL(audioRef.current.src);
      }

      // Lekérjük az audio URL-t az OpenAI API-tól
      const audioUrl = await textToSpeech(text, language);

      // Létrehozunk egy új audio elemet
      const audio = new Audio(audioUrl);
      audioRef.current = audio;

      // Lejátszás befejezésekor frissítjük az állapotot és felszabadítjuk az erőforrást
      audio.onended = () => {
        if (language === 'hu-HU') {
          setIsPlayingHungarian(false);
        } else {
          setIsPlayingGerman(false);
        }
        URL.revokeObjectURL(audioUrl);
      };

      // Lejátszás
      await audio.play();
    } catch (error) {
      console.error('Hiba a szövegfelolvasás során:', error);
      // Hiba esetén visszaállítjuk az állapotot
      if (language === 'hu-HU') {
        setIsPlayingHungarian(false);
      } else {
        setIsPlayingGerman(false);
      }
    }
  };

  return (
    <div className="container mx-auto px-4 py-8 mb-20 md:mb-0 md:pt-24">
      <div className="flex flex-col items-center mb-8">
        <div className="flex items-center gap-3 mb-2">
          <div className="p-2 rounded-full bg-language-quaternary/20">
            <ShoppingBag className="w-6 h-6 text-language-quaternary" />
          </div>
          <h1 className="text-2xl font-bold">Bolti eladó szakmai mondatok</h1>
        </div>
        <p className="text-gray-600 text-center max-w-2xl">
          Tanulj hasznos német mondatokat és kifejezéseket, amelyeket bolti eladóként használhatsz a mindennapi munkád során.
        </p>
      </div>

      <div className="max-w-md mx-auto mb-6">
        <Select
          value={filter}
          onValueChange={(value) => {
            setFilter(value);
            setCurrentIndex(0);
            setShowTranslation(false);
          }}
        >
          <SelectTrigger className="w-full">
            <SelectValue placeholder="Válassz kategóriát" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">Összes mondat</SelectItem>
            {retailPhrases.map(category => (
              <SelectItem key={category.id} value={category.id}>
                {category.title}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>

      {filteredPhrases.length > 0 ? (
        <div className="max-w-2xl mx-auto">
          <Card className="mb-6">
            <CardHeader>
              <CardTitle className="text-lg flex items-center gap-2">
                <Badge variant="outline" className="font-normal">
                  {filter === "all"
                    ? "Összes kategória"
                    : retailPhrases.find(c => c.id === filter)?.title || ""}
                </Badge>
              </CardTitle>
              <CardDescription>
                {currentIndex + 1} / {filteredPhrases.length}
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-6">
                <div>
                  <p className="text-xs text-gray-500 mb-1">Magyar:</p>
                  <p className="text-lg font-medium">{currentPhrase.hungarian}</p>
                  <Button
                    variant="ghost"
                    size="sm"
                    className="mt-1"
                    onClick={() => handleSpeech(currentPhrase.hungarian, 'hu-HU')}
                    disabled={isPlayingHungarian || isPlayingGerman}
                  >
                    {isPlayingHungarian ? (
                      <>
                        <Loader2 className="h-4 w-4 mr-1 animate-spin" />
                        <span className="text-xs">Lejátszás...</span>
                      </>
                    ) : (
                      <>
                        <Volume2 className="h-4 w-4 mr-1" />
                        <span className="text-xs">Hallgasd meg</span>
                      </>
                    )}
                  </Button>
                </div>

                {showTranslation ? (
                  <div>
                    <p className="text-xs text-gray-500 mb-1">Német:</p>
                    <p className="text-lg font-medium">{currentPhrase.german}</p>
                    <Button
                      variant="ghost"
                      size="sm"
                      className="mt-1"
                      onClick={() => handleSpeech(currentPhrase.german, 'de-DE')}
                      disabled={isPlayingHungarian || isPlayingGerman}
                    >
                      {isPlayingGerman ? (
                        <>
                          <Loader2 className="h-4 w-4 mr-1 animate-spin" />
                          <span className="text-xs">Lejátszás...</span>
                        </>
                      ) : (
                        <>
                          <Volume2 className="h-4 w-4 mr-1" />
                          <span className="text-xs">Hallgasd meg</span>
                        </>
                      )}
                    </Button>

                    {currentPhrase.notes && (
                      <div className="mt-3 p-2 bg-gray-50 rounded-md text-sm text-gray-600">
                        <p className="font-medium text-xs text-gray-500 mb-1">Megjegyzés:</p>
                        {currentPhrase.notes}
                      </div>
                    )}
                  </div>
                ) : (
                  <Button
                    variant="outline"
                    className="w-full"
                    onClick={() => setShowTranslation(true)}
                  >
                    Mutasd a német fordítást
                  </Button>
                )}
              </div>
            </CardContent>
            <CardFooter className="flex justify-between">
              <Button
                variant="outline"
                onClick={goToPrevious}
                disabled={currentIndex === 0}
              >
                <ChevronLeft className="h-4 w-4 mr-1" />
                Előző
              </Button>
              <Button
                variant="outline"
                onClick={goToNext}
                disabled={currentIndex === filteredPhrases.length - 1}
              >
                Következő
                <ChevronRight className="h-4 w-4 ml-1" />
              </Button>
            </CardFooter>
          </Card>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
            <Card className="hover:shadow-lg transition-shadow">
              <CardHeader>
                <CardTitle className="text-lg flex items-center gap-2">
                  <Book className="h-5 w-5 text-language-quaternary" />
                  Szakmai szókincs
                </CardTitle>
                <CardDescription>
                  Tanulj szakmai szavakat és kifejezéseket
                </CardDescription>
              </CardHeader>
              <CardContent>
                <p className="text-sm text-gray-600 mb-4">
                  Fedezd fel a bolti eladó szakmai szókincset különböző kategóriákban, és gyakorold a szavakat kártyákkal.
                </p>
                <Button asChild>
                  <Link to="/vocational/retail">
                    Szókincs megtekintése
                  </Link>
                </Button>
              </CardContent>
            </Card>

            <Card className="hover:shadow-lg transition-shadow">
              <CardHeader>
                <CardTitle className="text-lg flex items-center gap-2">
                  <Gamepad2 className="h-5 w-5 text-language-quaternary" />
                  Szakmai játékok
                </CardTitle>
                <CardDescription>
                  Gyakorold a szakmai szókincset interaktív játékokkal
                </CardDescription>
              </CardHeader>
              <CardContent>
                <p className="text-sm text-gray-600 mb-4">
                  Teszteld a tudásodat és fejleszd a nyelvi készségeidet különböző játékokkal, amelyek segítenek a szakmai szókincs elsajátításában.
                </p>
                <Button asChild>
                  <Link to="/vocational/retail/games">
                    Játékok indítása
                  </Link>
                </Button>
              </CardContent>
            </Card>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Gyakorlási tippek</CardTitle>
              </CardHeader>
              <CardContent>
                <ul className="list-disc pl-5 space-y-2 text-sm text-gray-600">
                  <li>Próbáld meg többször kimondani a mondatot</li>
                  <li>Gyakorold különböző helyzetekben</li>
                  <li>Írd le a mondatot emlékezetből</li>
                  <li>Alkoss hasonló mondatokat a minta alapján</li>
                </ul>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Használati kontextus</CardTitle>
              </CardHeader>
              <CardContent>
                <ul className="list-disc pl-5 space-y-2 text-sm text-gray-600">
                  <li>Vásárlók köszöntése és segítségnyújtás</li>
                  <li>Termékek bemutatása és ajánlása</li>
                  <li>Fizetési folyamat lebonyolítása</li>
                  <li>Reklamációk és problémák kezelése</li>
                  <li>Útbaigazítás az üzletben</li>
                </ul>
              </CardContent>
            </Card>
          </div>
        </div>
      ) : (
        <Card className="max-w-md mx-auto p-6 text-center">
          <p>Nincs találat a megadott feltételekkel.</p>
        </Card>
      )}
    </div>
  );
};

export default VocationalRetailPhrases;
