import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { toast } from 'sonner';
import { Loader2, MessageSquare, Plus, RefreshCw, Clock, CheckCircle, AlertCircle } from 'lucide-react';
import { formatDistanceToNow } from 'date-fns';
import { hu } from 'date-fns/locale';
import { useAuth } from '@/contexts/AuthContext';
import { SupportTicket, createTicket, getUserTickets, getAllTickets } from '@/services/supportService';
import AdminTicketList from '@/components/support/AdminTicketList';
import UserTicketList from '@/components/support/UserTicketList';
import NewTicketForm from '@/components/support/NewTicketForm';
import AdminMenu from '@/components/AdminMenu';

const Support = () => {
  const [activeTab, setActiveTab] = useState<string>('my-tickets');
  const [userTickets, setUserTickets] = useState<SupportTicket[]>([]);
  const [adminTickets, setAdminTickets] = useState<SupportTicket[]>([]);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [isRefreshing, setIsRefreshing] = useState<boolean>(false);

  const { user } = useAuth();
  const navigate = useNavigate();

  // Felhasználó ticketjeinek lekérdezése
  const fetchUserTickets = async (showLoadingIndicator = true) => {
    try {
      if (showLoadingIndicator) {
        setIsLoading(true);
      } else {
        setIsRefreshing(true);
      }

      const tickets = await getUserTickets();
      setUserTickets(tickets);

      if (!showLoadingIndicator) {
        toast.success('Ticketek sikeresen frissítve!');
      }
    } catch (error) {
      console.error('Hiba a ticketek lekérdezésekor:', error);
      toast.error('Nem sikerült lekérdezni a ticketeket. Kérjük, próbáld újra később!');
    } finally {
      setIsLoading(false);
      setIsRefreshing(false);
    }
  };

  // Admin ticketek lekérdezése
  const fetchAdminTickets = async (showLoadingIndicator = true) => {
    try {
      if (showLoadingIndicator) {
        setIsLoading(true);
      } else {
        setIsRefreshing(true);
      }

      const tickets = await getAllTickets();
      setAdminTickets(tickets);

      if (!showLoadingIndicator) {
        toast.success('Admin ticketek sikeresen frissítve!');
      }
    } catch (error) {
      console.error('Hiba az admin ticketek lekérdezésekor:', error);
      toast.error('Nem sikerült lekérdezni az admin ticketeket. Kérjük, próbáld újra később!');
    } finally {
      setIsLoading(false);
      setIsRefreshing(false);
    }
  };

  // Manuális frissítés
  const handleRefresh = () => {
    if (activeTab === 'admin-tickets' && user?.id === '11383db6-ab6e-4810-81a9-dc5ac1426d3a') {
      fetchAdminTickets(false);
    } else {
      fetchUserTickets(false);
    }
  };

  // Új ticket létrehozása után frissítjük a listát
  const handleTicketCreated = () => {
    fetchUserTickets();
    setActiveTab('my-tickets');
    toast.success('Ticket sikeresen létrehozva!');
  };

  // Komponens betöltésekor lekérdezzük a ticketeket
  useEffect(() => {
    fetchUserTickets();

    // Debug: felhasználói adatok kiírása
    console.log('Felhasználói adatok:', user);
    console.log('Felhasználó ID:', user?.id);

    // Ha a megadott ID-val rendelkező felhasználó, akkor az admin ticketeket is lekérdezzük
    if (user?.id === '11383db6-ab6e-4810-81a9-dc5ac1426d3a') {
      console.log('Admin jogosultság érvényesítve, admin ticketek lekérdezése');
      fetchAdminTickets();
    }
  }, [user?.id]);

  // Tab váltáskor frissítjük a megfelelő listát
  useEffect(() => {
    if (activeTab === 'admin-tickets' && user?.id === '11383db6-ab6e-4810-81a9-dc5ac1426d3a') {
      fetchAdminTickets();
    } else if (activeTab === 'my-tickets') {
      fetchUserTickets();
    }
  }, [activeTab, user?.id]);

  return (
    <div className="container mx-auto px-4 py-8 mb-20 md:mb-0 md:pt-24">
      <h1 className="text-2xl font-bold text-center mb-6">Ügyfélszolgálat</h1>

      <div className="flex flex-col md:flex-row gap-6 max-w-6xl mx-auto">
        {/* Admin menü megjelenítése bal oldalon */}
        <div className="md:w-1/4">
          <AdminMenu />
        </div>

        {/* Fő tartalom */}
        <div className="md:w-3/4">
          <Tabs defaultValue="my-tickets" value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="grid grid-cols-2 md:grid-cols-3 mb-6">
          <TabsTrigger value="my-tickets" className="flex items-center gap-2">
            <MessageSquare className="h-4 w-4" />
            <span>Saját ticketek</span>
          </TabsTrigger>
          <TabsTrigger value="new-ticket" className="flex items-center gap-2">
            <Plus className="h-4 w-4" />
            <span>Új ticket</span>
          </TabsTrigger>
          {user?.id === '11383db6-ab6e-4810-81a9-dc5ac1426d3a' && (
            <TabsTrigger value="admin-tickets" className="flex items-center gap-2">
              <CheckCircle className="h-4 w-4" />
              <span>Admin</span>
            </TabsTrigger>
          )}
        </TabsList>

        {/* Saját ticketek fül */}
        <TabsContent value="my-tickets">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between">
              <div>
                <CardTitle>Saját ticketek</CardTitle>
                <CardDescription>
                  Itt láthatod az általad létrehozott support ticketeket
                </CardDescription>
              </div>
              <Button
                variant="ghost"
                size="icon"
                onClick={handleRefresh}
                disabled={isRefreshing}
              >
                <RefreshCw className={`h-4 w-4 ${isRefreshing ? 'animate-spin' : ''}`} />
              </Button>
            </CardHeader>
            <CardContent>
              <UserTicketList
                tickets={userTickets}
                isLoading={isLoading}
                onTicketClick={(ticketId) => navigate(`/support/${ticketId}`)}
              />
            </CardContent>
            <CardFooter className="flex justify-center">
              <Button onClick={() => setActiveTab("new-ticket")} className="w-full md:w-auto">
                <Plus className="h-4 w-4 mr-2" />
                Új ticket létrehozása
              </Button>
            </CardFooter>
          </Card>
        </TabsContent>

        {/* Új ticket fül */}
        <TabsContent value="new-ticket">
          <Card>
            <CardHeader>
              <CardTitle>Új support ticket létrehozása</CardTitle>
              <CardDescription>
                Kérjük, add meg a problémád részleteit, és ügyfélszolgálatunk hamarosan válaszol
              </CardDescription>
            </CardHeader>
            <CardContent>
              <NewTicketForm onTicketCreated={handleTicketCreated} />
            </CardContent>
          </Card>
        </TabsContent>

        {/* Admin fül - csak az adott felhasználónak */}
        {user?.id === '11383db6-ab6e-4810-81a9-dc5ac1426d3a' && (
          <TabsContent value="admin-tickets">
            <Card>
              <CardHeader className="flex flex-row items-center justify-between">
                <div>
                  <CardTitle>Admin ticketek</CardTitle>
                  <CardDescription>
                    Itt kezelheted az összes felhasználói ticketet
                  </CardDescription>
                </div>
                <Button
                  variant="ghost"
                  size="icon"
                  onClick={handleRefresh}
                  disabled={isRefreshing}
                >
                  <RefreshCw className={`h-4 w-4 ${isRefreshing ? 'animate-spin' : ''}`} />
                </Button>
              </CardHeader>
              <CardContent>
                <AdminTicketList
                  tickets={adminTickets}
                  isLoading={isLoading}
                  onTicketClick={(ticketId) => navigate(`/support/${ticketId}`)}
                />
              </CardContent>
            </Card>
          </TabsContent>
        )}
      </Tabs>
        </div>
      </div>
    </div>
  );
};

export default Support;
