import React, { useEffect, useState } from "react";
import { Card, CardContent, CardDescription, Card<PERSON>ooter, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { useNavigate, useLocation } from "react-router-dom";
import { CheckCircle2, <PERSON><PERSON>eft, Loader2 } from "lucide-react";
import { useAuth } from "@/contexts/AuthContext";
import { verifyPointPurchase } from "@/services/pointService";
import { toast } from "sonner";

const PointsSuccess = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const { checkUserPoints } = useAuth();
  const query = new URLSearchParams(location.search);
  const sessionId = query.get("session_id");
  const [isVerifying, setIsVerifying] = useState(false);
  const [verified, setVerified] = useState(false);

  // Pontok ellenőrzése és hozzáadása
  const verifyPurchase = async () => {
    if (!sessionId || verified) return;

    try {
      setIsVerifying(true);
      // Pontok hozzáadása a felhasználóhoz
      await verifyPointPurchase(sessionId);
      setVerified(true);
      toast.success("A pontok sikeresen hozzá lettek adva a fiókodhoz!");
      // Frissítsük a pontokat a globális állapotban
      await checkUserPoints();
    } catch (error) {
      console.error("Hiba a vásárlás ellenőrzésekor:", error);
      toast.error("Nem sikerült ellenőrizni a vásárlást. Kérlek, próbáld újra később!");
    } finally {
      setIsVerifying(false);
    }
  };

  // Ha nincs session_id, átirányítjuk a felhasználót a pontok oldalra
  useEffect(() => {
    if (!sessionId) {
      navigate("/points");
    } else {
      // Ellenőrizzük a vásárlást és adjuk hozzá a pontokat
      verifyPurchase();
    }
  }, [sessionId, navigate]);

  return (
    <div className="container mx-auto px-4 py-8 mb-20 md:mb-0 md:pt-24">
      <div className="max-w-md mx-auto">
        <Card>
          <CardHeader>
            <div className="flex justify-center mb-4">
              {isVerifying ? (
                <Loader2 className="h-16 w-16 text-primary animate-spin" />
              ) : (
                <CheckCircle2 className="h-16 w-16 text-green-500" />
              )}
            </div>
            <CardTitle className="text-center text-2xl">
              {isVerifying ? "Fizetés ellenőrzése..." : "Sikeres vásárlás!"}
            </CardTitle>
            <CardDescription className="text-center">
              {isVerifying
                ? "Kérlek várj, amíg ellenőrizzük a fizetésedet..."
                : "A pontok sikeresen hozzá lettek adva a fiókodhoz."}
            </CardDescription>
          </CardHeader>
          <CardContent className="text-center">
            {isVerifying ? (
              <p>Ez néhány másodpercig tarthat...</p>
            ) : (
              <p>
                Köszönjük a vásárlást! A pontjaidat mostantól felhasználhatod az alkalmazásban.
              </p>
            )}
            {!isVerifying && !verified && (
              <div className="mt-4">
                <Button onClick={verifyPurchase} disabled={isVerifying}>
                  {isVerifying ? (
                    <>
                      <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                      Ellenőrzés...
                    </>
                  ) : (
                    "Pontok hozzáadása"
                  )}
                </Button>
              </div>
            )}
          </CardContent>
          <CardFooter className="flex justify-center">
            <Button onClick={() => navigate("/points")} className="flex items-center gap-2">
              <ArrowLeft className="h-4 w-4" />
              Vissza a pontjaimhoz
            </Button>
          </CardFooter>
        </Card>
      </div>
    </div>
  );
};

export default PointsSuccess;
