import React from "react";
import { useNavigate } from "react-router-dom";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { XCircle } from "lucide-react";

const SubscriptionCancel = () => {
  const navigate = useNavigate();

  return (
    <div className="container max-w-md py-10">
      <Card className="w-full">
        <CardHeader className="text-center">
          <div className="flex justify-center mb-4">
            <XCircle className="h-16 w-16 text-red-500" />
          </div>
          <CardTitle className="text-2xl">Előfizetés megszakítva</CardTitle>
          <CardDescription>
            Az előfizetési folyamat megszakadt vagy sikertelen volt.
          </CardDescription>
        </CardHeader>
        <CardContent className="text-center">
          <p className="mb-4">
            Nem tö<PERSON>t terhel<PERSON> a kártyádon. Ha problémád van az előfizetéssel, k<PERSON>rj<PERSON>k, próbáld újra vagy vedd fel a kapcsolatot az ügyfélszolgálattal.
          </p>
        </CardContent>
        <CardFooter className="flex justify-center gap-4">
          <Button variant="outline" onClick={() => navigate("/")}>
            Vissza a főoldalra
          </Button>
          <Button onClick={() => navigate("/subscription")}>
            Újra próbálkozás
          </Button>
        </CardFooter>
      </Card>
    </div>
  );
};

export default SubscriptionCancel;
