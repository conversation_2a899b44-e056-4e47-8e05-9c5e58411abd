import React, { useEffect, useState } from "react";
import { useNavigate, useSearchParams } from "react-router-dom";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { CheckCircle2, Users, Coins } from "lucide-react";
import { useAuth } from "@/contexts/AuthContext";
import { toast } from "sonner";
import { teacherService } from "@/services/apiService";

const TeacherSuccess = () => {
  const [searchParams] = useSearchParams();
  const sessionId = searchParams.get("session_id");
  const navigate = useNavigate();
  const { user, checkUserPoints } = useAuth();
  const [isLoading, setIsLoading] = useState(true);
  const [teacherData, setTeacherData] = useState<{
    isTeacher: boolean;
    maxStudents: number;
    monthlyPoints: number;
  } | null>(null);

  // Használjunk egy ref-et, hogy nyomon <PERSON>, hogy már lefutott-e a verifySubscription
  const hasVerified = React.useRef(false);

  useEffect(() => {
    // Csak egyszer futtatjuk le a verifySubscription függvényt
    if (hasVerified.current || !sessionId || !user) return;

    const verifySubscription = async () => {
      try {
        setIsLoading(true);
        hasVerified.current = true; // Megjelöljük, hogy már lefutott

        try {
          // Direkt lekérjük a Stripe session-t a session_id alapján
          const response = await teacherService.verifyTeacherSubscription(sessionId);
          setTeacherData(response.data);
          
          // Frissítsük a pontokat
          await checkUserPoints();
          
          toast.success("Tanári előfizetésed sikeresen aktiválva!", {
            id: "teacher-subscription-success", // Egyedi azonosító, hogy ne jelenjen meg többször
          });
        } catch (sessionError) {
          console.error("Hiba a session ellenőrzésekor:", sessionError);
          toast.error("Nem sikerült ellenőrizni az előfizetés állapotát. Kérjük, próbáld újra később!", {
            id: "teacher-subscription-error", // Egyedi azonosító, hogy ne jelenjen meg többször
          });
        }
      } catch (error) {
        console.error("Hiba az előfizetés ellenőrzésekor:", error);
      } finally {
        setIsLoading(false);
      }
    };

    verifySubscription();
  }, [sessionId, checkUserPoints, user]);

  return (
    <div className="container max-w-md py-10">
      <Card className="w-full">
        <CardHeader className="text-center">
          <div className="flex justify-center mb-4">
            <CheckCircle2 className="h-16 w-16 text-green-500" />
          </div>
          <CardTitle className="text-2xl">Sikeres tanári előfizetés!</CardTitle>
          <CardDescription>
            Köszönjük, hogy előfizettél a Magyar-Német Nyelvtanuló tanári csomagjára!
          </CardDescription>
        </CardHeader>
        <CardContent className="text-center">
          <p className="mb-4">
            A tanári előfizetésed sikeresen aktiválva lett. Most már hozzáférhetsz az összes tanári funkcióhoz.
          </p>
          {isLoading ? (
            <p className="text-muted-foreground">Előfizetés ellenőrzése...</p>
          ) : teacherData ? (
            <div className="space-y-4 mt-6">
              <div className="flex items-center justify-center gap-2 text-primary">
                <Users className="h-5 w-5" />
                <span className="font-medium">Maximum {teacherData.maxStudents} diák</span>
              </div>
              <div className="flex items-center justify-center gap-2 text-primary">
                <Coins className="h-5 w-5" />
                <span className="font-medium">{teacherData.monthlyPoints} pont havonta</span>
              </div>
            </div>
          ) : (
            <p className="text-red-500">Nem sikerült lekérni az előfizetés adatait</p>
          )}
        </CardContent>
        <CardFooter className="flex flex-col gap-4">
          <Button
            onClick={() => navigate("/teacher/dashboard")}
            className="w-full"
          >
            Tanári vezérlőpult megnyitása
          </Button>
          <Button
            variant="outline"
            onClick={() => navigate("/")}
            className="w-full"
          >
            Vissza a főoldalra
          </Button>
        </CardFooter>
      </Card>
    </div>
  );
};

export default TeacherSuccess;
