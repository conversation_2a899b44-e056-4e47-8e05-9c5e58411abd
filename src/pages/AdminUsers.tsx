import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { Table, TableHeader, TableBody, TableRow, TableCell, TableHead } from '@/components/ui/table';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Card, CardHeader, CardTitle, CardContent } from '@/components/ui/card';
import { Search, RefreshCw, Eye, MailCheck } from 'lucide-react';
import { toast } from 'sonner';
import apiService from '@/services/apiService';
import { format } from 'date-fns';
import { hu } from 'date-fns/locale';
import { Skeleton } from '@/components/ui/skeleton';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Label } from '@/components/ui/label';

interface User {
  id: string;
  name: string;
  email: string;
  points: number;
  isAdmin: boolean;
  createdAt: string;
  updatedAt: string;
  hasActiveSubscription: boolean;
  subscription: any;
  _count: {
    subscriptions: number;
    pointTransactions: number;
  };
}

const AdminUsers: React.FC = () => {
  const [users, setUsers] = useState<User[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedUser, setSelectedUser] = useState<string | null>(null);
  const [isResetDialogOpen, setIsResetDialogOpen] = useState(false);
  const [isResetting, setIsResetting] = useState(false);
  
  const navigate = useNavigate();

  // Felhasználók lekérése
  const fetchUsers = async () => {
    try {
      setIsLoading(true);
      const response = await apiService.get('/admin/users');
      setUsers(response.data.data);
    } catch (error) {
      console.error('Hiba a felhasználók lekérésekor:', error);
      toast.error('Nem sikerült lekérni a felhasználókat');
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    fetchUsers();
  }, []);

  // Jelszó visszaállítás
  const handleResetPassword = async () => {
    if (!selectedUser) return;
    
    try {
      setIsResetting(true);
      const response = await apiService.post(`/admin/users/${selectedUser}/reset-password`);
      
      if (response.data.success) {
        toast.success('Jelszó visszaállítási email elküldve');
        setIsResetDialogOpen(false);
      } else {
        toast.error(response.data.message || 'Hiba történt a jelszó visszaállítása során');
      }
    } catch (error) {
      console.error('Hiba a jelszó visszaállítása során:', error);
      toast.error('Nem sikerült elküldeni a jelszó visszaállítási emailt');
    } finally {
      setIsResetting(false);
    }
  };

  // Szűrt felhasználók
  const filteredUsers = users.filter(user => 
    user.name.toLowerCase().includes(searchTerm.toLowerCase()) || 
    user.email.toLowerCase().includes(searchTerm.toLowerCase())
  );

  return (
    <div className="container mx-auto py-8">
      <Card>
        <CardHeader className="flex flex-row items-center justify-between">
          <CardTitle>Felhasználók kezelése</CardTitle>
          <Button 
            variant="ghost" 
            size="icon" 
            onClick={fetchUsers} 
            disabled={isLoading}
          >
            <RefreshCw className={`h-4 w-4 ${isLoading ? 'animate-spin' : ''}`} />
          </Button>
        </CardHeader>
        <CardContent>
          <div className="flex items-center mb-4">
            <div className="relative flex-1">
              <Search className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
              <Input
                placeholder="Keresés név vagy email alapján..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>
          </div>

          <div className="rounded-md border">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Név</TableHead>
                  <TableHead>Email</TableHead>
                  <TableHead>Regisztráció dátuma</TableHead>
                  <TableHead>Előfizetés</TableHead>
                  <TableHead>Pontok</TableHead>
                  <TableHead>Műveletek</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {isLoading ? (
                  Array.from({ length: 5 }).map((_, index) => (
                    <TableRow key={index}>
                      <TableCell><Skeleton className="h-4 w-[100px]" /></TableCell>
                      <TableCell><Skeleton className="h-4 w-[150px]" /></TableCell>
                      <TableCell><Skeleton className="h-4 w-[120px]" /></TableCell>
                      <TableCell><Skeleton className="h-4 w-[80px]" /></TableCell>
                      <TableCell><Skeleton className="h-4 w-[60px]" /></TableCell>
                      <TableCell><Skeleton className="h-4 w-[100px]" /></TableCell>
                    </TableRow>
                  ))
                ) : filteredUsers.length === 0 ? (
                  <TableRow>
                    <TableCell colSpan={6} className="text-center py-4">
                      Nincs találat
                    </TableCell>
                  </TableRow>
                ) : (
                  filteredUsers.map(user => (
                    <TableRow key={user.id}>
                      <TableCell>{user.name}</TableCell>
                      <TableCell>{user.email}</TableCell>
                      <TableCell>
                        {format(new Date(user.createdAt), 'yyyy. MMMM d.', { locale: hu })}
                      </TableCell>
                      <TableCell>
                        {user.hasActiveSubscription ? (
                          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                            Aktív
                          </span>
                        ) : (
                          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                            Nincs
                          </span>
                        )}
                      </TableCell>
                      <TableCell>{user.points}</TableCell>
                      <TableCell>
                        <div className="flex space-x-2">
                          <Button 
                            variant="ghost" 
                            size="icon"
                            onClick={() => navigate(`/admin/users/${user.id}`)}
                            title="Részletek"
                          >
                            <Eye className="h-4 w-4" />
                          </Button>
                          
                          <Dialog open={isResetDialogOpen && selectedUser === user.id} onOpenChange={(open) => {
                            setIsResetDialogOpen(open);
                            if (!open) setSelectedUser(null);
                          }}>
                            <DialogTrigger asChild>
                              <Button 
                                variant="ghost" 
                                size="icon"
                                onClick={() => setSelectedUser(user.id)}
                                title="Jelszó visszaállítás"
                              >
                                <MailCheck className="h-4 w-4" />
                              </Button>
                            </DialogTrigger>
                            <DialogContent>
                              <DialogHeader>
                                <DialogTitle>Jelszó visszaállítás</DialogTitle>
                                <DialogDescription>
                                  Jelszó visszaállítási email küldése a felhasználónak.
                                </DialogDescription>
                              </DialogHeader>
                              <div className="py-4">
                                <div className="space-y-4">
                                  <div>
                                    <Label>Felhasználó</Label>
                                    <div className="font-medium">{user.name}</div>
                                  </div>
                                  <div>
                                    <Label>Email</Label>
                                    <div className="font-medium">{user.email}</div>
                                  </div>
                                </div>
                              </div>
                              <DialogFooter>
                                <Button variant="outline" onClick={() => setIsResetDialogOpen(false)}>Mégsem</Button>
                                <Button onClick={handleResetPassword} disabled={isResetting}>
                                  {isResetting ? 'Küldés...' : 'Email küldése'}
                                </Button>
                              </DialogFooter>
                            </DialogContent>
                          </Dialog>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))
                )}
              </TableBody>
            </Table>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default AdminUsers;
