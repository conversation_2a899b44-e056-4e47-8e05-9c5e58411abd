import React, { useState, useEffect, useRef } from "react";
import { Link } from "react-router-dom";
import { <PERSON>, CardContent, CardHeader, CardTitle, CardDescription, CardFooter } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { Progress } from "@/components/ui/progress";
import { retailVocabulary, retailPhrases } from "@/data/vocational/retail";
import { ShoppingBag, Brain, Target, Volume2, Loader2, Check, X, RotateCcw, MessageSquare, Briefcase, Book } from "lucide-react";
import { textToSpeech } from "@/services/openaiService";
import { cn } from "@/lib/utils";

const VocationalRetailGames: React.FC = () => {
  const [currentGame, setCurrentGame] = useState<string | null>(null);
  const [score, setScore] = useState(0);
  const [isCorrect, setIsCorrect] = useState<boolean | null>(null);
  const [audioRef, setAudioRef] = useState<HTMLAudioElement | null>(null);
  const [isPlayingAudio, setIsPlayingAudio] = useState(false);

  // Szópárosítás játék állapotai
  const [currentWord, setCurrentWord] = useState("");
  const [options, setOptions] = useState<string[]>([]);

  // Hallás utáni írás játék állapotai
  const [dictationText, setDictationText] = useState("");
  const [userInput, setUserInput] = useState("");
  const [showDictationAnswer, setShowDictationAnswer] = useState(false);

  // Szituációs játék állapotai
  const [currentSituation, setCurrentSituation] = useState<any>(null);
  const [situationOptions, setSituationOptions] = useState<string[]>([]);
  const [situationStep, setSituationStep] = useState(0);
  const [situationHistory, setSituationHistory] = useState<{text: string, isUser: boolean}[]>([]);

  // Játék újraindítása a pontszám nullázásával
  const resetGame = () => {
    setScore(0);
    setIsCorrect(null);
  };

  // Szövegfelolvasás funkció
  const handleSpeech = async (text: string, language: 'hu-HU' | 'de-DE') => {
    try {
      setIsPlayingAudio(true);

      // Ha van korábbi audio, azt leállítjuk és felszabadítjuk
      if (audioRef) {
        audioRef.pause();
        URL.revokeObjectURL(audioRef.src);
      }

      // Lekérjük az audio URL-t az OpenAI API-tól
      const audioUrl = await textToSpeech(text, language);

      // Létrehozunk egy új audio elemet
      const audio = new Audio(audioUrl);
      setAudioRef(audio);

      // Lejátszás befejezésekor frissítjük az állapotot és felszabadítjuk az erőforrást
      audio.onended = () => {
        setIsPlayingAudio(false);
        URL.revokeObjectURL(audioUrl);
      };

      // Lejátszás
      await audio.play();
    } catch (error) {
      console.error('Hiba a szövegfelolvasás során:', error);
      setIsPlayingAudio(false);
    }
  };

  // Szópárosítás játék indítása
  const startWordMatch = () => {
    resetGame();

    // Összegyűjtjük az összes szót
    const allWords = retailVocabulary.flatMap(category =>
      category.items.map(item => ({ german: item.german, hungarian: item.hungarian }))
    );

    // Véletlenszerűen kiválasztunk egy szót
    const randomWord = allWords[Math.floor(Math.random() * allWords.length)];

    // Véletlenszerű opciók generálása (3 helytelen + 1 helyes)
    const incorrectOptions = allWords
      .filter(word => word.german !== randomWord.german)
      .sort(() => Math.random() - 0.5)
      .slice(0, 3)
      .map(word => word.german);

    setCurrentWord(randomWord.hungarian);
    setOptions([...incorrectOptions, randomWord.german].sort(() => Math.random() - 0.5));
    setCurrentGame("wordMatch");
  };

  // Válasz ellenőrzése a szópárosítás játékban
  const checkWordMatchAnswer = (answer: string) => {
    const allWords = retailVocabulary.flatMap(category =>
      category.items.map(item => ({ german: item.german, hungarian: item.hungarian }))
    );

    const correct = allWords.find(word => word.hungarian === currentWord)?.german === answer;
    setIsCorrect(correct);

    if (correct) {
      setScore(prev => prev + 1);
    }

    setTimeout(() => {
      if (currentGame === "wordMatch") {
        // Új szó generálása
        const randomWord = allWords[Math.floor(Math.random() * allWords.length)];
        const incorrectOptions = allWords
          .filter(word => word.german !== randomWord.german)
          .sort(() => Math.random() - 0.5)
          .slice(0, 3)
          .map(word => word.german);

        setCurrentWord(randomWord.hungarian);
        setOptions([...incorrectOptions, randomWord.german].sort(() => Math.random() - 0.5));
        setIsCorrect(null);
      }
    }, 1500);
  };

  // Hallás utáni írás játék indítása
  const startDictation = () => {
    resetGame();

    // Összegyűjtjük az összes szót és rövid mondatot
    const vocabularyItems = retailVocabulary.flatMap(category =>
      category.items.map(item => item.german)
    );

    const phraseItems = retailPhrases.flatMap(category =>
      category.items
        .filter(item => item.german.split(' ').length <= 5) // Csak rövid mondatokat választunk
        .map(item => item.german)
    );

    // Véletlenszerűen választunk szót vagy mondatot
    const useVocabulary = Math.random() > 0.5;
    let text = "";

    if (useVocabulary) {
      text = vocabularyItems[Math.floor(Math.random() * vocabularyItems.length)];
    } else {
      text = phraseItems[Math.floor(Math.random() * phraseItems.length)];
    }

    setDictationText(text);
    setUserInput("");
    setShowDictationAnswer(false);
    setCurrentGame("dictation");

    // Automatikusan lejátsszuk a szöveget
    setTimeout(() => {
      handleSpeech(text, 'de-DE');
    }, 500);
  };

  // Hallás utáni írás ellenőrzése
  const checkDictation = () => {
    // Normalizáljuk a szöveget az összehasonlításhoz (kisbetűk, írásjelek eltávolítása)
    const normalizeText = (text: string) => {
      return text.toLowerCase().replace(/[.,!?;:]/g, '').trim();
    };

    const normalizedInput = normalizeText(userInput);
    const normalizedText = normalizeText(dictationText);

    // Ellenőrizzük a hasonlóságot (kis hibákat megengedünk)
    const correct = normalizedInput === normalizedText;
    const almostCorrect = normalizedText.includes(normalizedInput) || normalizedInput.includes(normalizedText);

    setIsCorrect(correct || almostCorrect);
    setShowDictationAnswer(true);

    if (correct) {
      setScore(prev => prev + 2); // Több pont a tökéletes válaszért
    } else if (almostCorrect) {
      setScore(prev => prev + 1); // Kevesebb pont a majdnem jó válaszért
    }
  };

  // Új diktálás indítása
  const nextDictation = () => {
    startDictation();
  };

  // Szituációs játék indítása
  const startSituationGame = () => {
    resetGame();

    // Szituációk definiálása
    const situations = [
      {
        title: "Vásárló köszöntése",
        intro: "Egy vásárló lép be az üzletbe. Hogyan köszöntöd?",
        steps: [
          {
            question: "Válaszd ki a megfelelő köszöntést:",
            options: [
              "Guten Tag! Kann ich Ihnen helfen?",
              "Hallo! Was willst du?",
              "Guten Tag! Kaufen Sie etwas!",
              "Willkommen! Sie müssen etwas kaufen."
            ],
            correctIndex: 0
          },
          {
            question: "A vásárló azt mondja: 'Ich suche ein Geschenk für meine Mutter.' (Ajándékot keresek anyukámnak.) Mit válaszolsz?",
            options: [
              "Wir haben keine Geschenke.",
              "Geschenke sind teuer hier.",
              "Was für ein Geschenk suchen Sie? Haben Sie bestimmte Vorstellungen?",
              "Ihre Mutter mag sicher Schuhe."
            ],
            correctIndex: 2
          },
          {
            question: "A vásárló érdeklődik: 'Haben Sie Parfüm?' (Van parfümjük?) Mit válaszolsz?",
            options: [
              "Nein, wir verkaufen kein Parfüm.",
              "Ja, die Parfümabteilung ist im ersten Stock rechts.",
              "Parfüm ist zu teuer.",
              "Kaufen Sie lieber etwas anderes."
            ],
            correctIndex: 1
          }
        ]
      },
      {
        title: "Reklamáció kezelése",
        intro: "Egy vásárló visszahoz egy terméket, mert nem működik. Hogyan kezeled a helyzetet?",
        steps: [
          {
            question: "A vásárló azt mondja: 'Dieses Gerät funktioniert nicht.' (Ez a készülék nem működik.) Mit válaszolsz?",
            options: [
              "Das ist nicht mein Problem.",
              "Sie haben es sicher falsch benutzt.",
              "Es tut mir leid für die Unannehmlichkeiten. Haben Sie den Kassenbon dabei?",
              "Wir nehmen keine Rückgaben an."
            ],
            correctIndex: 2
          },
          {
            question: "A vásárló mutatja a blokkot. Mit mondasz?",
            options: [
              "Der Kassenbon ist zu alt.",
              "Vielen Dank. Ich werde das überprüfen. Möchten Sie einen Umtausch oder eine Rückerstattung?",
              "Sie müssen trotzdem bezahlen.",
              "Kommen Sie morgen wieder."
            ],
            correctIndex: 1
          },
          {
            question: "A vásárló cserét szeretne. Mit válaszolsz?",
            options: [
              "Wir haben keine anderen Geräte mehr.",
              "Sie müssen ein neues kaufen.",
              "Gerne. Ich hole Ihnen ein neues Gerät. Bitte warten Sie einen Moment.",
              "Das ist nicht möglich."
            ],
            correctIndex: 2
          }
        ]
      },
      {
        title: "Fizetés a pénztárnál",
        intro: "Egy vásárló a pénztárhoz lép. Hogyan bonyolítod le a fizetést?",
        steps: [
          {
            question: "Hogyan köszöntöd a vásárlót a pénztárnál?",
            options: [
              "Schnell, schnell!",
              "Guten Tag! Haben Sie alles gefunden?",
              "Haben Sie Geld?",
              "Sie müssen jetzt bezahlen."
            ],
            correctIndex: 1
          },
          {
            question: "Hogyan közlöd a fizetendő összeget?",
            options: [
              "Das ist sehr teuer für Sie.",
              "Sie müssen mehr bezahlen.",
              "Das macht insgesamt 45,90 Euro, bitte.",
              "Geld, jetzt!"
            ],
            correctIndex: 2
          },
          {
            question: "A vásárló kártyával szeretne fizetni. Mit mondasz?",
            options: [
              "Karten akzeptieren wir nicht.",
              "Bitte geben Sie Ihre PIN ein und bestätigen Sie den Betrag.",
              "Nur Bargeld, bitte.",
              "Karten sind kompliziert."
            ],
            correctIndex: 1
          }
        ]
      }
    ];

    // Véletlenszerűen választunk egy szituációt
    const randomSituation = situations[Math.floor(Math.random() * situations.length)];
    setCurrentSituation(randomSituation);
    setSituationStep(0);
    setSituationOptions(randomSituation.steps[0].options);
    setSituationHistory([
      { text: randomSituation.intro, isUser: false }
    ]);

    setCurrentGame("situation");
  };

  // Szituációs játék válasz ellenőrzése
  const checkSituationAnswer = (answerIndex: number) => {
    const currentStepData = currentSituation.steps[situationStep];
    const isAnswerCorrect = answerIndex === currentStepData.correctIndex;

    setIsCorrect(isAnswerCorrect);

    // Frissítjük a beszélgetés történetét
    setSituationHistory(prev => [
      ...prev,
      { text: currentStepData.options[answerIndex], isUser: true }
    ]);

    if (isAnswerCorrect) {
      setScore(prev => prev + 1);

      // Ha van következő lépés
      if (situationStep < currentSituation.steps.length - 1) {
        setTimeout(() => {
          const nextStep = situationStep + 1;
          setSituationStep(nextStep);
          setSituationOptions(currentSituation.steps[nextStep].options);
          setSituationHistory(prev => [
            ...prev,
            { text: currentSituation.steps[nextStep].question, isUser: false }
          ]);
          setIsCorrect(null);
        }, 1500);
      } else {
        // Játék vége, sikeres befejezés
        setTimeout(() => {
          setSituationHistory(prev => [
            ...prev,
            { text: "Gratulálok! Sikeresen kezelted a helyzetet!", isUser: false }
          ]);
        }, 1500);
      }
    } else {
      // Helytelen válasz esetén
      setTimeout(() => {
        setSituationHistory(prev => [
          ...prev,
          {
            text: `A helyes válasz ez lett volna: "${currentStepData.options[currentStepData.correctIndex]}"`,
            isUser: false
          }
        ]);

        // Ha van következő lépés, akkor is folytatjuk
        if (situationStep < currentSituation.steps.length - 1) {
          setTimeout(() => {
            const nextStep = situationStep + 1;
            setSituationStep(nextStep);
            setSituationOptions(currentSituation.steps[nextStep].options);
            setSituationHistory(prev => [
              ...prev,
              { text: currentSituation.steps[nextStep].question, isUser: false }
            ]);
            setIsCorrect(null);
          }, 1500);
        }
      }, 1500);
    }
  };

  return (
    <div className="container mx-auto px-4 py-8 mb-20 md:mb-0 md:pt-24">
      <div className="flex flex-col items-center mb-8">
        <div className="flex items-center gap-3 mb-2">
          <div className="p-2 rounded-full bg-language-quaternary/20">
            <ShoppingBag className="w-6 h-6 text-language-quaternary" />
          </div>
          <h1 className="text-2xl font-bold">Bolti eladó szakmai játékok</h1>
        </div>
        <p className="text-gray-600 text-center max-w-2xl">
          Gyakorold a bolti eladó szakmai szókincset és kifejezéseket interaktív játékokkal.
          Teszteld a tudásodat és fejleszd a nyelvi készségeidet.
        </p>
      </div>

      {!currentGame ? (
        <>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 max-w-5xl mx-auto">
            <Card className="hover:shadow-lg transition-shadow cursor-pointer" onClick={startWordMatch}>
              <CardHeader>
                <CardTitle className="flex items-center justify-between">
                  <span>Szópárosítás</span>
                  <Brain className="h-6 w-6 text-language-quaternary" />
                </CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-gray-600">Válaszd ki a magyar szó német megfelelőjét!</p>
              </CardContent>
            </Card>

            <Card className="hover:shadow-lg transition-shadow cursor-pointer" onClick={startDictation}>
              <CardHeader>
                <CardTitle className="flex items-center justify-between">
                  <span>Hallás utáni írás</span>
                  <Volume2 className="h-6 w-6 text-language-quaternary" />
                </CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-gray-600">Hallgasd meg és írd le a német szavakat és kifejezéseket!</p>
              </CardContent>
            </Card>

            <Card className="hover:shadow-lg transition-shadow cursor-pointer" onClick={startSituationGame}>
              <CardHeader>
                <CardTitle className="flex items-center justify-between">
                  <span>Szituációs gyakorlat</span>
                  <MessageSquare className="h-6 w-6 text-language-quaternary" />
                </CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-gray-600">Gyakorold a bolti helyzeteket párbeszédek segítségével!</p>
              </CardContent>
            </Card>
          </div>

          <div className="mt-12 max-w-5xl mx-auto">
            <h2 className="text-xl font-bold mb-4">További tanulási lehetőségek</h2>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <Card className="hover:shadow-lg transition-shadow">
                <CardHeader>
                  <CardTitle className="text-lg flex items-center gap-2">
                    <Book className="h-5 w-5 text-language-quaternary" />
                    Szakmai szókincs
                  </CardTitle>
                  <CardDescription>
                    Tanulj szakmai szavakat és kifejezéseket
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <p className="text-sm text-gray-600 mb-4">
                    Fedezd fel a bolti eladó szakmai szókincset különböző kategóriákban, és gyakorold a szavakat kártyákkal.
                  </p>
                  <Button asChild>
                    <Link to="/vocational/retail">
                      Szókincs megtekintése
                    </Link>
                  </Button>
                </CardContent>
              </Card>

              <Card className="hover:shadow-lg transition-shadow">
                <CardHeader>
                  <CardTitle className="text-lg flex items-center gap-2">
                    <MessageSquare className="h-5 w-5 text-language-quaternary" />
                    Szakmai mondatok
                  </CardTitle>
                  <CardDescription>
                    Tanulj hasznos mondatokat és kifejezéseket
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <p className="text-sm text-gray-600 mb-4">
                    Fedezd fel a különböző kategóriákba rendezett szakmai mondatokat, amelyeket bolti eladóként használhatsz a mindennapi munkád során.
                  </p>
                  <Button asChild>
                    <Link to="/vocational/retail/phrases">
                      Mondatok megtekintése
                    </Link>
                  </Button>
                </CardContent>
              </Card>
            </div>
          </div>
        </>

      ) : currentGame === "wordMatch" ? (
        <div className="max-w-xl mx-auto">
          <Card>
            <CardContent className="pt-6">
              <div className="text-center mb-6">
                <p className="text-sm text-gray-500 mb-2">Pontszám: {score}</p>
                <h2 className="text-2xl font-bold mb-4">{currentWord}</h2>
                <p className="text-sm text-gray-500">Válaszd ki a német megfelelőjét:</p>
              </div>

              <div className="grid grid-cols-1 gap-3 mb-6">
                {options.map((option, index) => (
                  <Button
                    key={index}
                    variant={
                      isCorrect === null
                        ? "outline"
                        : isCorrect && option === options.find(opt => {
                            const allWords = retailVocabulary.flatMap(category =>
                              category.items.map(item => ({ german: item.german, hungarian: item.hungarian }))
                            );
                            return allWords.find(word => word.hungarian === currentWord)?.german === opt;
                          })
                        ? "success"
                        : isCorrect === false && option === options.find(opt => {
                            const allWords = retailVocabulary.flatMap(category =>
                              category.items.map(item => ({ german: item.german, hungarian: item.hungarian }))
                            );
                            return allWords.find(word => word.hungarian === currentWord)?.german === opt;
                          })
                        ? "success"
                        : isCorrect === false && options[index] === options.find(opt => {
                            const allWords = retailVocabulary.flatMap(category =>
                              category.items.map(item => ({ german: item.german, hungarian: item.hungarian }))
                            );
                            return allWords.find(word => word.hungarian === currentWord)?.german !== opt;
                          })
                        ? "destructive"
                        : "outline"
                    }
                    className="py-6 text-lg"
                    onClick={() => isCorrect === null && checkWordMatchAnswer(option)}
                    disabled={isCorrect !== null}
                  >
                    {option}
                  </Button>
                ))}
              </div>
            </CardContent>
            <CardFooter className="flex justify-between">
              <Button
                variant="outline"
                onClick={() => setCurrentGame(null)}
              >
                Vissza a játékokhoz
              </Button>
              <Button
                variant="default"
                onClick={startWordMatch}
              >
                <RotateCcw className="h-4 w-4 mr-2" />
                Új játék
              </Button>
            </CardFooter>
          </Card>
        </div>
      ) : currentGame === "dictation" ? (
        <div className="max-w-xl mx-auto">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Volume2 className="h-5 w-5 text-language-quaternary" />
                Hallás utáni írás
              </CardTitle>
              <CardDescription>
                Hallgasd meg a német szöveget, majd írd le, amit hallottál.
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="text-center mb-6">
                <p className="text-sm text-gray-500 mb-2">Pontszám: {score}</p>

                <Button
                  variant="outline"
                  className="mb-4"
                  onClick={() => handleSpeech(dictationText, 'de-DE')}
                  disabled={isPlayingAudio}
                >
                  {isPlayingAudio ? (
                    <>
                      <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                      Lejátszás...
                    </>
                  ) : (
                    <>
                      <Volume2 className="h-4 w-4 mr-2" />
                      Meghallgatás
                    </>
                  )}
                </Button>

                <div className="mb-4">
                  <Input
                    placeholder="Írd ide, amit hallottál..."
                    value={userInput}
                    onChange={(e) => setUserInput(e.target.value)}
                    disabled={showDictationAnswer}
                    className="text-center"
                  />
                </div>

                {!showDictationAnswer ? (
                  <Button
                    onClick={checkDictation}
                    disabled={userInput.trim() === ""}
                  >
                    Ellenőrzés
                  </Button>
                ) : (
                  <div className="space-y-4">
                    <div className={cn(
                      "p-3 rounded-md",
                      isCorrect ? "bg-green-50 border border-green-200" : "bg-red-50 border border-red-200"
                    )}>
                      <p className="text-sm font-medium mb-1">A helyes válasz:</p>
                      <p className="text-lg font-bold">{dictationText}</p>

                      <div className="flex items-center mt-2">
                        {isCorrect ? (
                          <Badge className="bg-green-100 text-green-800 flex items-center gap-1">
                            <Check className="h-3 w-3" /> Helyes
                          </Badge>
                        ) : (
                          <Badge className="bg-red-100 text-red-800 flex items-center gap-1">
                            <X className="h-3 w-3" /> Helytelen
                          </Badge>
                        )}
                      </div>
                    </div>

                    <Button onClick={nextDictation}>
                      Következő feladat
                    </Button>
                  </div>
                )}
              </div>
            </CardContent>
            <CardFooter className="flex justify-between">
              <Button
                variant="outline"
                onClick={() => setCurrentGame(null)}
              >
                Vissza a játékokhoz
              </Button>
              <Button
                variant="default"
                onClick={startDictation}
              >
                <RotateCcw className="h-4 w-4 mr-2" />
                Új játék
              </Button>
            </CardFooter>
          </Card>
        </div>
      ) : currentGame === "situation" ? (
        <div className="max-w-2xl mx-auto">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <MessageSquare className="h-5 w-5 text-language-quaternary" />
                {currentSituation?.title || "Szituációs gyakorlat"}
              </CardTitle>
              <CardDescription>
                Pontszám: {score} | Lépés: {situationStep + 1}/{currentSituation?.steps.length}
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4 mb-6">
                {situationHistory.map((item, index) => (
                  <div
                    key={index}
                    className={cn(
                      "p-3 rounded-lg",
                      item.isUser
                        ? "bg-language-quaternary/10 ml-12"
                        : "bg-gray-100 mr-12"
                    )}
                  >
                    {item.text}
                  </div>
                ))}
              </div>

              {situationStep < currentSituation?.steps.length && isCorrect === null && (
                <div className="space-y-3">
                  <p className="font-medium text-sm text-gray-500 mb-2">Válaszd ki a megfelelő választ:</p>
                  {situationOptions.map((option, index) => (
                    <Button
                      key={index}
                      variant="outline"
                      className="w-full justify-start py-3 px-4 h-auto"
                      onClick={() => checkSituationAnswer(index)}
                    >
                      {option}
                    </Button>
                  ))}
                </div>
              )}

              {isCorrect !== null && (
                <div className={cn(
                  "p-3 rounded-md mt-4",
                  isCorrect ? "bg-green-50 border border-green-200" : "bg-red-50 border border-red-200"
                )}>
                  <div className="flex items-center">
                    {isCorrect ? (
                      <Badge className="bg-green-100 text-green-800 flex items-center gap-1">
                        <Check className="h-3 w-3" /> Helyes válasz
                      </Badge>
                    ) : (
                      <Badge className="bg-red-100 text-red-800 flex items-center gap-1">
                        <X className="h-3 w-3" /> Helytelen válasz
                      </Badge>
                    )}
                  </div>
                </div>
              )}

              {situationStep >= currentSituation?.steps.length && (
                <Button
                  className="w-full mt-4"
                  onClick={startSituationGame}
                >
                  Új szituáció
                </Button>
              )}
            </CardContent>
            <CardFooter className="flex justify-between">
              <Button
                variant="outline"
                onClick={() => setCurrentGame(null)}
              >
                Vissza a játékokhoz
              </Button>
              <Button
                variant="default"
                onClick={startSituationGame}
              >
                <RotateCcw className="h-4 w-4 mr-2" />
                Új játék
              </Button>
            </CardFooter>
          </Card>
        </div>
      ) : null}
    </div>
  );
};

export default VocationalRetailGames;
