import React, { useEffect, useState } from "react";
import { useNavigate, useSearchParams } from "react-router-dom";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { CheckCircle2 } from "lucide-react";
import { useAuth } from "@/contexts/AuthContext";
import { toast } from "sonner";
import { subscriptionService } from "@/services/apiService";

const SubscriptionSuccess = () => {
  const [searchParams] = useSearchParams();
  const sessionId = searchParams.get("session_id");
  const navigate = useNavigate();
  const { user, checkSubscription } = useAuth();
  const [isLoading, setIsLoading] = useState(true);

  // Használjunk egy ref-et, hogy nyomon köves<PERSON>ük, hogy már lefutott-e a verifySubscription
  const hasVerified = React.useRef(false);

  useEffect(() => {
    // Csak egyszer futtatjuk le a verifySubscription függvényt
    if (hasVerified.current || !sessionId || !user) return;

    const verifySubscription = async () => {
      try {
        setIsLoading(true);
        hasVerified.current = true; // Megjelöljük, hogy már lefutott

        try {
          // Direkt lekérjük a Stripe session-t a session_id alapján
          await subscriptionService.verifySession(sessionId);
          // Frissítsük az előfizetés állapotát
          await checkSubscription();
          toast.success("Előfizetésed sikeresen aktiválva!", {
            id: "subscription-success", // Egyedi azonosító, hogy ne jelenjen meg többször
          });
        } catch (sessionError) {
          console.error("Hiba a session ellenőrzésekor:", sessionError);
          // Még mindig megpróbáljuk frissíteni az előfizetés állapotát
          await checkSubscription();
        }
      } catch (error) {
        console.error("Hiba az előfizetés ellenőrzésekor:", error);
        toast.error("Nem sikerült ellenőrizni az előfizetés állapotát. Kérjük, próbáld újra később!", {
          id: "subscription-error", // Egyedi azonosító, hogy ne jelenjen meg többször
        });
      } finally {
        setIsLoading(false);
      }
    };

    verifySubscription();
  }, [sessionId, checkSubscription, user]);

  return (
    <div className="container max-w-md py-10">
      <Card className="w-full">
        <CardHeader className="text-center">
          <div className="flex justify-center mb-4">
            <CheckCircle2 className="h-16 w-16 text-green-500" />
          </div>
          <CardTitle className="text-2xl">Sikeres előfizetés!</CardTitle>
          <CardDescription>
            Köszönjük, hogy előfizettél a Magyar-Német Nyelvtanuló alkalmazásra!
          </CardDescription>
        </CardHeader>
        <CardContent className="text-center">
          <p className="mb-4">
            Az előfizetésed sikeresen aktiválva lett. Most már hozzáférhetsz az összes prémium funkcióhoz.
          </p>
          {isLoading ? (
            <p className="text-muted-foreground">Előfizetés ellenőrzése...</p>
          ) : (
            <p className="text-green-600 font-medium">Előfizetésed aktív!</p>
          )}
        </CardContent>
        <CardFooter className="flex justify-center">
          <Button onClick={() => navigate("/")}>Vissza a főoldalra</Button>
        </CardFooter>
      </Card>
    </div>
  );
};

export default SubscriptionSuccess;
