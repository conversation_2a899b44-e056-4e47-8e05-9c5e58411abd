import React from "react";
import { <PERSON>, CardContent, CardDescription, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>eader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { useNavigate } from "react-router-dom";
import { <PERSON>Circle, ArrowLeft } from "lucide-react";

const PointsCancel = () => {
  const navigate = useNavigate();

  return (
    <div className="container mx-auto px-4 py-8 mb-20 md:mb-0 md:pt-24">
      <div className="max-w-md mx-auto">
        <Card>
          <CardHeader>
            <div className="flex justify-center mb-4">
              <XCircle className="h-16 w-16 text-red-500" />
            </div>
            <CardTitle className="text-center text-2xl">Vásárlás megszakítva</CardTitle>
            <CardDescription className="text-center">
              A pontok vásárlása megszakadt vagy sikertelen volt.
            </CardDescription>
          </CardHeader>
          <CardContent className="text-center">
            <p>
              A vásárlás nem fejeződött be, így nem kerültek pontok a fiókodhoz.
              Ha problémát tapasztalsz, kérjük, próbáld újra később.
            </p>
          </CardContent>
          <CardFooter className="flex justify-center">
            <Button onClick={() => navigate("/points")} className="flex items-center gap-2">
              <ArrowLeft className="h-4 w-4" />
              Vissza a pontjaimhoz
            </Button>
          </CardFooter>
        </Card>
      </div>
    </div>
  );
};

export default PointsCancel;
