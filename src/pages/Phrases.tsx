
import React, { useState, useRef } from "react";
import { phrasesData } from "@/data/languageData";
import { Button } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from "@/components/ui/select";
import { Volume2, ChevronLeft, ChevronRight, Loader2 } from "lucide-react";
import { Badge } from "@/components/ui/badge";
import { textToSpeech } from "@/services/openaiService";

const Phrases = () => {
  const [currentIndex, setCurrentIndex] = useState(0);
  const [filter, setFilter] = useState<string>("all");
  const [showTranslation, setShowTranslation] = useState(false);
  const [isPlayingHungarian, setIsPlayingHungarian] = useState(false);
  const [isPlayingGerman, setIsPlayingGerman] = useState(false);
  const audioRef = useRef<HTMLAudioElement | null>(null);

  // Komponens unmount eseménykezelő
  React.useEffect(() => {
    return () => {
      // Felszabadítjuk az erőforrásokat, amikor a felhasználó elhagyja az oldalt
      if (audioRef.current) {
        audioRef.current.pause();
        if (audioRef.current.src) {
          URL.revokeObjectURL(audioRef.current.src);
        }
      }
    };
  }, []);

  // Filter phrases based on selection
  const filteredPhrases = filter === "all"
    ? phrasesData
    : phrasesData.filter(item => item.difficulty === filter || item.category === filter);

  const currentPhrase = filteredPhrases[currentIndex];

  // Handle navigation
  const goToNext = () => {
    if (currentIndex < filteredPhrases.length - 1) {
      setCurrentIndex(currentIndex + 1);
      setShowTranslation(false);
    }
  };

  const goToPrevious = () => {
    if (currentIndex > 0) {
      setCurrentIndex(currentIndex - 1);
      setShowTranslation(false);
    }
  };

  // Szövegfelolvasás funkció
  const handleSpeech = async (text: string, language: 'hu-HU' | 'de-DE') => {
    try {
      // Beállítjuk a megfelelő loading állapotot
      if (language === 'hu-HU') {
        setIsPlayingHungarian(true);
      } else {
        setIsPlayingGerman(true);
      }

      // Ha van korábbi audio, azt leállítjuk és felszabadítjuk
      if (audioRef.current) {
        audioRef.current.pause();
        URL.revokeObjectURL(audioRef.current.src);
      }

      // Lekérjük az audio URL-t az OpenAI API-tól
      const audioUrl = await textToSpeech(text, language);

      // Létrehozunk egy új audio elemet
      const audio = new Audio(audioUrl);
      audioRef.current = audio;

      // Lejátszás befejezésekor frissítjük az állapotot és felszabadítjuk az erőforrást
      audio.onended = () => {
        if (language === 'hu-HU') {
          setIsPlayingHungarian(false);
        } else {
          setIsPlayingGerman(false);
        }
        URL.revokeObjectURL(audioUrl);
      };

      // Lejátszás
      await audio.play();
    } catch (error) {
      console.error('Hiba a szövegfelolvasás során:', error);
      // Hiba esetén visszaállítjuk az állapotot
      if (language === 'hu-HU') {
        setIsPlayingHungarian(false);
      } else {
        setIsPlayingGerman(false);
      }
    }
  };

  // Get unique categories for filter
  const categories = Array.from(new Set(phrasesData.map(item => item.category)));

  return (
    <div className="container mx-auto px-4 py-8 mb-20 md:mb-0 md:pt-24">
      <h1 className="text-2xl font-bold text-center mb-6">Mondatok</h1>

      <div className="max-w-md mx-auto mb-6">
        <Select
          value={filter}
          onValueChange={(value) => {
            setFilter(value);
            setCurrentIndex(0);
            setShowTranslation(false);
          }}
        >
          <SelectTrigger className="w-full">
            <SelectValue placeholder="Válassz kategóriát" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">Összes mondat</SelectItem>
            <SelectItem value="beginner">Kezdő szint</SelectItem>
            <SelectItem value="intermediate">Középhaladó szint</SelectItem>
            <SelectItem value="advanced">Haladó szint</SelectItem>
            {categories.map(category => (
              <SelectItem key={category} value={category}>
                {category.charAt(0).toUpperCase() + category.slice(1)}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>

      {filteredPhrases.length > 0 ? (
        <div className="max-w-lg mx-auto">
          <Card className="mb-6">
            <CardContent className="pt-6">
              <div className="flex items-center justify-between mb-2">
                <Badge variant="outline">{currentPhrase.category}</Badge>
                <Badge
                  variant={
                    currentPhrase.difficulty === "beginner" ? "default" :
                    currentPhrase.difficulty === "intermediate" ? "secondary" :
                    "destructive"
                  }
                >
                  {currentPhrase.difficulty === "beginner" ? "Kezdő" :
                   currentPhrase.difficulty === "intermediate" ? "Középhaladó" :
                   "Haladó"}
                </Badge>
              </div>

              <div className="space-y-6">
                <div>
                  <p className="text-xs text-gray-500 mb-1">Magyar:</p>
                  <p className="text-lg font-medium">{currentPhrase.hungarian}</p>
                  <Button
                    variant="ghost"
                    size="sm"
                    className="mt-1"
                    onClick={() => handleSpeech(currentPhrase.hungarian, 'hu-HU')}
                    disabled={isPlayingHungarian || isPlayingGerman}
                  >
                    {isPlayingHungarian ? (
                      <>
                        <Loader2 className="h-4 w-4 mr-1 animate-spin" />
                        <span className="text-xs">Lejátszás...</span>
                      </>
                    ) : (
                      <>
                        <Volume2 className="h-4 w-4 mr-1" />
                        <span className="text-xs">Hallgasd meg</span>
                      </>
                    )}
                  </Button>
                </div>

                {showTranslation ? (
                  <div>
                    <p className="text-xs text-gray-500 mb-1">Német:</p>
                    <p className="text-lg font-medium">{currentPhrase.german}</p>
                    <Button
                      variant="ghost"
                      size="sm"
                      className="mt-1"
                      onClick={() => handleSpeech(currentPhrase.german, 'de-DE')}
                      disabled={isPlayingHungarian || isPlayingGerman}
                    >
                      {isPlayingGerman ? (
                        <>
                          <Loader2 className="h-4 w-4 mr-1 animate-spin" />
                          <span className="text-xs">Lejátszás...</span>
                        </>
                      ) : (
                        <>
                          <Volume2 className="h-4 w-4 mr-1" />
                          <span className="text-xs">Hallgasd meg</span>
                        </>
                      )}
                    </Button>
                  </div>
                ) : (
                  <Button
                    variant="outline"
                    className="w-full"
                    onClick={() => setShowTranslation(true)}
                  >
                    Mutasd a német fordítást
                  </Button>
                )}
              </div>
            </CardContent>
          </Card>

          <div className="flex justify-between items-center">
            <Button
              variant="outline"
              onClick={goToPrevious}
              disabled={currentIndex === 0}
            >
              <ChevronLeft className="h-5 w-5 mr-1" />
              Előző
            </Button>

            <span className="text-sm text-gray-500">
              {currentIndex + 1} / {filteredPhrases.length}
            </span>

            <Button
              variant="outline"
              onClick={goToNext}
              disabled={currentIndex === filteredPhrases.length - 1}
            >
              Következő
              <ChevronRight className="h-5 w-5 ml-1" />
            </Button>
          </div>

          <div className="mt-8">
            <h3 className="text-lg font-bold mb-4">Gyakorlási tippek</h3>
            <ul className="list-disc pl-5 space-y-2 text-sm text-gray-600">
              <li>Próbáld meg többször kimondani a mondatot</li>
              <li>Gyakorold különböző helyzetekben</li>
              <li>Írd le a mondatot emlékezetből</li>
              <li>Alkoss hasonló mondatokat a minta alapján</li>
            </ul>
          </div>
        </div>
      ) : (
        <Card className="max-w-md mx-auto p-6 text-center">
          <p>Nincs találat a megadott feltételekkel.</p>
        </Card>
      )}
    </div>
  );
};

export default Phrases;
