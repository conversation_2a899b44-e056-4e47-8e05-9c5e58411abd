import React, { useState, useEffect } from 'react';
import { use<PERSON><PERSON><PERSON>, useNavigate } from 'react-router-dom';
import { 
  Card, 
  CardHeader, 
  CardTitle, 
  CardContent, 
  CardDescription,
  CardFooter
} from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { 
  Table, 
  TableHeader, 
  TableBody, 
  TableRow, 
  TableCell, 
  TableHead 
} from '@/components/ui/table';
import { toast } from 'sonner';
import { format } from 'date-fns';
import { hu } from 'date-fns/locale';
import { 
  ArrowLeft, 
  MailCheck, 
  Plus, 
  Minus,
  Calendar,
  CreditCard,
  User,
  Clock,
  MessageSquare,
  AlertCircle
} from 'lucide-react';
import apiService from '@/services/apiService';
import { Skeleton } from '@/components/ui/skeleton';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Textarea } from '@/components/ui/textarea';

interface UserDetail {
  user: {
    id: string;
    name: string;
    email: string;
    points: number;
    isAdmin: boolean;
    stripeCustomerId: string;
    createdAt: string;
    updatedAt: string;
  };
  subscriptions: Array<{
    id: string;
    status: string;
    stripeSubscriptionId: string;
    stripePriceId: string;
    stripeCurrentPeriodEnd: string;
    cancelAtPeriodEnd: boolean;
    createdAt: string;
  }>;
  pointTransactions: Array<{
    id: string;
    amount: number;
    type: string;
    description: string;
    createdAt: string;
  }>;
  supportTickets: Array<{
    id: string;
    subject: string;
    status: string;
    priority: string;
    createdAt: string;
    _count: {
      messages: number;
    };
  }>;
}

const AdminUserDetail: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  
  const [userDetail, setUserDetail] = useState<UserDetail | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isResetDialogOpen, setIsResetDialogOpen] = useState(false);
  const [isResetting, setIsResetting] = useState(false);
  const [isPointsDialogOpen, setIsPointsDialogOpen] = useState(false);
  const [pointsAmount, setPointsAmount] = useState('');
  const [pointsReason, setPointsReason] = useState('');
  const [pointsOperation, setPointsOperation] = useState<'add' | 'deduct'>('add');
  const [isAdjustingPoints, setIsAdjustingPoints] = useState(false);

  // Felhasználó részleteinek lekérése
  const fetchUserDetail = async () => {
    try {
      setIsLoading(true);
      const response = await apiService.get(`/admin/users/${id}`);
      setUserDetail(response.data.data);
    } catch (error) {
      console.error('Hiba a felhasználó részleteinek lekérésekor:', error);
      toast.error('Nem sikerült lekérni a felhasználó részleteit');
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    if (id) {
      fetchUserDetail();
    }
  }, [id]);

  // Jelszó visszaállítás
  const handleResetPassword = async () => {
    if (!id) return;
    
    try {
      setIsResetting(true);
      const response = await apiService.post(`/admin/users/${id}/reset-password`);
      
      if (response.data.success) {
        toast.success('Jelszó visszaállítási email elküldve');
        setIsResetDialogOpen(false);
      } else {
        toast.error(response.data.message || 'Hiba történt a jelszó visszaállítása során');
      }
    } catch (error) {
      console.error('Hiba a jelszó visszaállítása során:', error);
      toast.error('Nem sikerült elküldeni a jelszó visszaállítási emailt');
    } finally {
      setIsResetting(false);
    }
  };

  // Pontok módosítása
  const handleAdjustPoints = async () => {
    if (!id || !pointsAmount || isNaN(Number(pointsAmount))) {
      toast.error('Kérjük, adj meg egy érvényes pontszámot');
      return;
    }
    
    try {
      setIsAdjustingPoints(true);
      
      // Pontszám előjelének beállítása a művelet alapján
      const amount = pointsOperation === 'add' 
        ? Math.abs(Number(pointsAmount)) 
        : -Math.abs(Number(pointsAmount));
      
      const response = await apiService.post(`/admin/users/${id}/points`, {
        amount,
        reason: pointsReason
      });
      
      if (response.data.success) {
        toast.success(response.data.message || 'Pontok sikeresen módosítva');
        setIsPointsDialogOpen(false);
        fetchUserDetail(); // Frissítjük a felhasználó adatait
        
        // Mezők alaphelyzetbe állítása
        setPointsAmount('');
        setPointsReason('');
        setPointsOperation('add');
      } else {
        toast.error(response.data.message || 'Hiba történt a pontok módosítása során');
      }
    } catch (error) {
      console.error('Hiba a pontok módosítása során:', error);
      toast.error('Nem sikerült módosítani a pontokat');
    } finally {
      setIsAdjustingPoints(false);
    }
  };

  // Tranzakció típus fordítása
  const translateTransactionType = (type: string) => {
    const types: Record<string, string> = {
      'purchase': 'Vásárlás',
      'usage': 'Felhasználás',
      'subscription': 'Előfizetés',
      'admin_add': 'Admin hozzáadás',
      'admin_deduct': 'Admin levonás'
    };
    
    return types[type] || type;
  };

  // Előfizetés státusz fordítása
  const translateSubscriptionStatus = (status: string) => {
    const statuses: Record<string, string> = {
      'active': 'Aktív',
      'canceled': 'Lemondva',
      'inactive': 'Inaktív'
    };
    
    return statuses[status] || status;
  };

  // Ticket státusz fordítása
  const translateTicketStatus = (status: string) => {
    const statuses: Record<string, string> = {
      'open': 'Nyitott',
      'in_progress': 'Folyamatban',
      'closed': 'Lezárva'
    };
    
    return statuses[status] || status;
  };

  // Ticket prioritás fordítása
  const translateTicketPriority = (priority: string) => {
    const priorities: Record<string, string> = {
      'low': 'Alacsony',
      'medium': 'Közepes',
      'high': 'Magas'
    };
    
    return priorities[priority] || priority;
  };

  // Ticket státusz színe
  const getTicketStatusColor = (status: string) => {
    const colors: Record<string, string> = {
      'open': 'bg-blue-100 text-blue-800',
      'in_progress': 'bg-yellow-100 text-yellow-800',
      'closed': 'bg-green-100 text-green-800'
    };
    
    return colors[status] || 'bg-gray-100 text-gray-800';
  };

  // Ticket prioritás színe
  const getTicketPriorityColor = (priority: string) => {
    const colors: Record<string, string> = {
      'low': 'bg-green-100 text-green-800',
      'medium': 'bg-yellow-100 text-yellow-800',
      'high': 'bg-red-100 text-red-800'
    };
    
    return colors[priority] || 'bg-gray-100 text-gray-800';
  };

  if (isLoading) {
    return (
      <div className="container mx-auto py-8">
        <div className="flex items-center mb-6">
          <Button variant="ghost" size="icon" onClick={() => navigate('/admin/users')}>
            <ArrowLeft className="h-4 w-4" />
          </Button>
          <Skeleton className="h-8 w-[200px] ml-2" />
        </div>
        
        <Card>
          <CardHeader>
            <Skeleton className="h-6 w-[150px]" />
            <Skeleton className="h-4 w-[250px]" />
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {Array.from({ length: 5 }).map((_, index) => (
                <div key={index} className="flex justify-between">
                  <Skeleton className="h-4 w-[100px]" />
                  <Skeleton className="h-4 w-[200px]" />
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  if (!userDetail) {
    return (
      <div className="container mx-auto py-8">
        <div className="flex items-center mb-6">
          <Button variant="ghost" size="icon" onClick={() => navigate('/admin/users')}>
            <ArrowLeft className="h-4 w-4" />
          </Button>
          <h1 className="text-2xl font-bold ml-2">Felhasználó nem található</h1>
        </div>
        
        <Card>
          <CardContent className="py-8">
            <div className="text-center">
              <AlertCircle className="h-12 w-12 text-red-500 mx-auto mb-4" />
              <p className="text-lg">A keresett felhasználó nem található vagy nincs jogosultságod a megtekintéséhez.</p>
              <Button className="mt-4" onClick={() => navigate('/admin/users')}>
                Vissza a felhasználók listájához
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="container mx-auto py-8">
      <div className="flex items-center mb-6">
        <Button variant="ghost" size="icon" onClick={() => navigate('/admin/users')}>
          <ArrowLeft className="h-4 w-4" />
        </Button>
        <h1 className="text-2xl font-bold ml-2">Felhasználó részletei</h1>
      </div>
      
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
        {/* Felhasználói adatok */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <User className="h-5 w-5 mr-2" />
              Felhasználói adatok
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div>
                <Label>Név</Label>
                <div className="font-medium">{userDetail.user.name}</div>
              </div>
              <div>
                <Label>Email</Label>
                <div className="font-medium">{userDetail.user.email}</div>
              </div>
              <div>
                <Label>Regisztráció dátuma</Label>
                <div className="font-medium">
                  {format(new Date(userDetail.user.createdAt), 'yyyy. MMMM d. HH:mm', { locale: hu })}
                </div>
              </div>
              <div>
                <Label>Stripe azonosító</Label>
                <div className="font-medium text-sm">
                  {userDetail.user.stripeCustomerId || 'Nincs'}
                </div>
              </div>
              <div>
                <Label>Admin jogosultság</Label>
                <div className="font-medium">
                  {userDetail.user.isAdmin ? 'Igen' : 'Nem'}
                </div>
              </div>
            </div>
          </CardContent>
          <CardFooter className="flex justify-between">
            <Dialog open={isResetDialogOpen} onOpenChange={setIsResetDialogOpen}>
              <DialogTrigger asChild>
                <Button variant="outline" size="sm">
                  <MailCheck className="h-4 w-4 mr-2" />
                  Jelszó visszaállítás
                </Button>
              </DialogTrigger>
              <DialogContent>
                <DialogHeader>
                  <DialogTitle>Jelszó visszaállítás</DialogTitle>
                  <DialogDescription>
                    Jelszó visszaállítási email küldése a felhasználónak.
                  </DialogDescription>
                </DialogHeader>
                <div className="py-4">
                  <div className="space-y-4">
                    <div>
                      <Label>Felhasználó</Label>
                      <div className="font-medium">{userDetail.user.name}</div>
                    </div>
                    <div>
                      <Label>Email</Label>
                      <div className="font-medium">{userDetail.user.email}</div>
                    </div>
                  </div>
                </div>
                <DialogFooter>
                  <Button variant="outline" onClick={() => setIsResetDialogOpen(false)}>Mégsem</Button>
                  <Button onClick={handleResetPassword} disabled={isResetting}>
                    {isResetting ? 'Küldés...' : 'Email küldése'}
                  </Button>
                </DialogFooter>
              </DialogContent>
            </Dialog>
          </CardFooter>
        </Card>
        
        {/* Előfizetés adatok */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Calendar className="h-5 w-5 mr-2" />
              Előfizetés
            </CardTitle>
          </CardHeader>
          <CardContent>
            {userDetail.subscriptions.length > 0 ? (
              <div className="space-y-4">
                <div>
                  <Label>Státusz</Label>
                  <div className="font-medium">
                    {userDetail.subscriptions[0].status === 'active' ? (
                      <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                        Aktív
                      </span>
                    ) : (
                      <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                        {translateSubscriptionStatus(userDetail.subscriptions[0].status)}
                      </span>
                    )}
                  </div>
                </div>
                <div>
                  <Label>Következő fizetés</Label>
                  <div className="font-medium">
                    {format(new Date(userDetail.subscriptions[0].stripeCurrentPeriodEnd), 'yyyy. MMMM d.', { locale: hu })}
                  </div>
                </div>
                <div>
                  <Label>Lemondás a periódus végén</Label>
                  <div className="font-medium">
                    {userDetail.subscriptions[0].cancelAtPeriodEnd ? 'Igen' : 'Nem'}
                  </div>
                </div>
                <div>
                  <Label>Előfizetés kezdete</Label>
                  <div className="font-medium">
                    {format(new Date(userDetail.subscriptions[0].createdAt), 'yyyy. MMMM d.', { locale: hu })}
                  </div>
                </div>
                <div>
                  <Label>Stripe előfizetés azonosító</Label>
                  <div className="font-medium text-sm">
                    {userDetail.subscriptions[0].stripeSubscriptionId}
                  </div>
                </div>
              </div>
            ) : (
              <div className="text-center py-6 text-gray-500">
                <p>Nincs aktív előfizetés</p>
              </div>
            )}
          </CardContent>
        </Card>
        
        {/* Pont egyenleg */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <CreditCard className="h-5 w-5 mr-2" />
              Pont egyenleg
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-center py-6">
              <div className="text-4xl font-bold mb-2">{userDetail.user.points}</div>
              <p className="text-gray-500">Elérhető pont</p>
              
              <div className="mt-6 flex justify-center space-x-2">
                <Dialog open={isPointsDialogOpen} onOpenChange={setIsPointsDialogOpen}>
                  <DialogTrigger asChild>
                    <Button variant="outline" size="sm">
                      <Plus className="h-4 w-4 mr-2" />
                      Pontok módosítása
                    </Button>
                  </DialogTrigger>
                  <DialogContent>
                    <DialogHeader>
                      <DialogTitle>Pontok módosítása</DialogTitle>
                      <DialogDescription>
                        Pontok hozzáadása vagy levonása a felhasználó egyenlegéből.
                      </DialogDescription>
                    </DialogHeader>
                    <div className="py-4 space-y-4">
                      <div className="space-y-2">
                        <Label>Művelet</Label>
                        <Select 
                          value={pointsOperation} 
                          onValueChange={(value) => setPointsOperation(value as 'add' | 'deduct')}
                        >
                          <SelectTrigger>
                            <SelectValue placeholder="Válassz műveletet" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="add">Pontok hozzáadása</SelectItem>
                            <SelectItem value="deduct">Pontok levonása</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                      
                      <div className="space-y-2">
                        <Label>Pontok száma</Label>
                        <Input 
                          type="number" 
                          min="1"
                          value={pointsAmount} 
                          onChange={(e) => setPointsAmount(e.target.value)}
                          placeholder="Pl. 1000"
                        />
                      </div>
                      
                      <div className="space-y-2">
                        <Label>Indoklás (opcionális)</Label>
                        <Textarea 
                          value={pointsReason} 
                          onChange={(e) => setPointsReason(e.target.value)}
                          placeholder="Pl. Admin korrekció"
                          rows={3}
                        />
                      </div>
                    </div>
                    <DialogFooter>
                      <Button variant="outline" onClick={() => setIsPointsDialogOpen(false)}>Mégsem</Button>
                      <Button onClick={handleAdjustPoints} disabled={isAdjustingPoints || !pointsAmount}>
                        {isAdjustingPoints ? 'Feldolgozás...' : 'Pontok módosítása'}
                      </Button>
                    </DialogFooter>
                  </DialogContent>
                </Dialog>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
      
      {/* Részletes adatok tabfüleken */}
      <Tabs defaultValue="transactions">
        <TabsList className="mb-4">
          <TabsTrigger value="transactions">Pont tranzakciók</TabsTrigger>
          <TabsTrigger value="subscriptions">Előfizetések</TabsTrigger>
          <TabsTrigger value="tickets">Support ticketek</TabsTrigger>
        </TabsList>
        
        {/* Pont tranzakciók */}
        <TabsContent value="transactions">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <Clock className="h-5 w-5 mr-2" />
                Pont tranzakciók
              </CardTitle>
              <CardDescription>
                A felhasználó pont tranzakcióinak története
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="rounded-md border">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Dátum</TableHead>
                      <TableHead>Típus</TableHead>
                      <TableHead>Mennyiség</TableHead>
                      <TableHead>Leírás</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {userDetail.pointTransactions.length === 0 ? (
                      <TableRow>
                        <TableCell colSpan={4} className="text-center py-4">
                          Nincs pont tranzakció
                        </TableCell>
                      </TableRow>
                    ) : (
                      userDetail.pointTransactions.map(transaction => (
                        <TableRow key={transaction.id}>
                          <TableCell>
                            {format(new Date(transaction.createdAt), 'yyyy. MM. dd. HH:mm', { locale: hu })}
                          </TableCell>
                          <TableCell>{translateTransactionType(transaction.type)}</TableCell>
                          <TableCell className={transaction.amount > 0 ? 'text-green-600' : 'text-red-600'}>
                            {transaction.amount > 0 ? `+${transaction.amount}` : transaction.amount}
                          </TableCell>
                          <TableCell>{transaction.description || '-'}</TableCell>
                        </TableRow>
                      ))
                    )}
                  </TableBody>
                </Table>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
        
        {/* Előfizetések */}
        <TabsContent value="subscriptions">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <Calendar className="h-5 w-5 mr-2" />
                Előfizetések története
              </CardTitle>
              <CardDescription>
                A felhasználó összes előfizetése
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="rounded-md border">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Kezdés dátuma</TableHead>
                      <TableHead>Státusz</TableHead>
                      <TableHead>Következő fizetés</TableHead>
                      <TableHead>Lemondás</TableHead>
                      <TableHead>Stripe azonosító</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {userDetail.subscriptions.length === 0 ? (
                      <TableRow>
                        <TableCell colSpan={5} className="text-center py-4">
                          Nincs előfizetés
                        </TableCell>
                      </TableRow>
                    ) : (
                      userDetail.subscriptions.map(subscription => (
                        <TableRow key={subscription.id}>
                          <TableCell>
                            {format(new Date(subscription.createdAt), 'yyyy. MM. dd.', { locale: hu })}
                          </TableCell>
                          <TableCell>
                            <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                              subscription.status === 'active' 
                                ? 'bg-green-100 text-green-800' 
                                : 'bg-gray-100 text-gray-800'
                            }`}>
                              {translateSubscriptionStatus(subscription.status)}
                            </span>
                          </TableCell>
                          <TableCell>
                            {format(new Date(subscription.stripeCurrentPeriodEnd), 'yyyy. MM. dd.', { locale: hu })}
                          </TableCell>
                          <TableCell>
                            {subscription.cancelAtPeriodEnd ? 'Igen' : 'Nem'}
                          </TableCell>
                          <TableCell className="text-sm">
                            {subscription.stripeSubscriptionId}
                          </TableCell>
                        </TableRow>
                      ))
                    )}
                  </TableBody>
                </Table>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
        
        {/* Support ticketek */}
        <TabsContent value="tickets">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <MessageSquare className="h-5 w-5 mr-2" />
                Support ticketek
              </CardTitle>
              <CardDescription>
                A felhasználó által létrehozott support ticketek
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="rounded-md border">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Dátum</TableHead>
                      <TableHead>Tárgy</TableHead>
                      <TableHead>Státusz</TableHead>
                      <TableHead>Prioritás</TableHead>
                      <TableHead>Üzenetek</TableHead>
                      <TableHead>Művelet</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {userDetail.supportTickets.length === 0 ? (
                      <TableRow>
                        <TableCell colSpan={6} className="text-center py-4">
                          Nincs support ticket
                        </TableCell>
                      </TableRow>
                    ) : (
                      userDetail.supportTickets.map(ticket => (
                        <TableRow key={ticket.id}>
                          <TableCell>
                            {format(new Date(ticket.createdAt), 'yyyy. MM. dd.', { locale: hu })}
                          </TableCell>
                          <TableCell>{ticket.subject}</TableCell>
                          <TableCell>
                            <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getTicketStatusColor(ticket.status)}`}>
                              {translateTicketStatus(ticket.status)}
                            </span>
                          </TableCell>
                          <TableCell>
                            <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getTicketPriorityColor(ticket.priority)}`}>
                              {translateTicketPriority(ticket.priority)}
                            </span>
                          </TableCell>
                          <TableCell>{ticket._count.messages}</TableCell>
                          <TableCell>
                            <Button 
                              variant="ghost" 
                              size="sm"
                              onClick={() => navigate(`/support/${ticket.id}`)}
                            >
                              Megtekintés
                            </Button>
                          </TableCell>
                        </TableRow>
                      ))
                    )}
                  </TableBody>
                </Table>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default AdminUserDetail;
