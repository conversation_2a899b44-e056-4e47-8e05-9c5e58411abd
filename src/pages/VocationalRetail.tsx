import React, { useState, useRef } from "react";
import { <PERSON> } from "react-router-dom";
import { retailVocabulary, retailPhrases, VocationalItem } from "@/data/vocational/retail";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle, CardDescription, CardFooter } from "@/components/ui/card";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Switch } from "@/components/ui/switch";
import { Label } from "@/components/ui/label";
import { Badge } from "@/components/ui/badge";
import { Volume2, Loader2, BookOpen, Briefcase, ShoppingBag, MessageSquare, Gamepad2, RefreshCw, ThumbsUp, ThumbsDown } from "lucide-react";
import { textToSpeech } from "@/services/openaiService";

const VocationalRetail: React.FC = () => {
  const [activeTab, setActiveTab] = useState<string>(retailVocabulary[0].id);
  const [show<PERSON><PERSON>, set<PERSON>how<PERSON><PERSON>] = useState<boolean>(true);
  const [showHungarian, setShowHungarian] = useState<boolean>(true);
  const [showExamples, setShowExamples] = useState<boolean>(true);
  const [showNotes, setShowNotes] = useState<boolean>(true);
  const [flashcardMode, setFlashcardMode] = useState<boolean>(false);
  const [currentFlashcard, setCurrentFlashcard] = useState<VocationalItem | null>(null);
  const [showFlashcardAnswer, setShowFlashcardAnswer] = useState<boolean>(false);
  const [flashcardDirection, setFlashcardDirection] = useState<'de-hu' | 'hu-de'>('de-hu');
  const [isPlayingGerman, setIsPlayingGerman] = useState<boolean>(false);
  const [isPlayingHungarian, setIsPlayingHungarian] = useState<boolean>(false);
  const [situationMode, setSituationMode] = useState<boolean>(false);
  const [currentSituation, setCurrentSituation] = useState<{
    scenario: string;
    customerLine: string;
    correctResponse: string;
    alternatives: string[];
    shuffledResponses?: string[];
  } | null>(null);
  const [selectedResponse, setSelectedResponse] = useState<string | null>(null);
  const [showSituationFeedback, setShowSituationFeedback] = useState<boolean>(false);

  // Szókincsteszt állapotok
  const [quizMode, setQuizMode] = useState<boolean>(false);
  const [currentQuiz, setCurrentQuiz] = useState<{
    word: string;
    correctAnswer: string;
    options: string[];
    language: 'de' | 'hu';
  } | null>(null);
  const [selectedAnswer, setSelectedAnswer] = useState<string | null>(null);
  const [showQuizFeedback, setShowQuizFeedback] = useState<boolean>(false);
  const [quizScore, setQuizScore] = useState<{ correct: number; total: number }>({ correct: 0, total: 0 });

  // Szerepjáték állapotok
  const [roleplayMode, setRoleplayMode] = useState<boolean>(false);
  const [currentRoleplay, setCurrentRoleplay] = useState<{
    scenario: string;
    customerRole: string;
    sellerRole: string;
    dialogue: Array<{
      speaker: 'customer' | 'seller';
      text: string;
      translation: string;
    }>;
    currentStep: number;
  } | null>(null);
  const audioRef = useRef<HTMLAudioElement | null>(null);

  // Szövegfelolvasás funkció
  const handleSpeech = async (text: string, language: 'hu-HU' | 'de-DE') => {
    try {
      // Beállítjuk a megfelelő loading állapotot
      if (language === 'hu-HU') {
        setIsPlayingHungarian(true);
      } else {
        setIsPlayingGerman(true);
      }

      // Ha van korábbi audio, azt leállítjuk és felszabadítjuk
      if (audioRef.current) {
        audioRef.current.pause();
        URL.revokeObjectURL(audioRef.current.src);
      }

      // Lekérjük az audio URL-t az OpenAI API-tól
      const audioUrl = await textToSpeech(text, language);

      // Létrehozunk egy új audio elemet
      const audio = new Audio(audioUrl);
      audioRef.current = audio;

      // Lejátszás befejezésekor frissítjük az állapotot és felszabadítjuk az erőforrást
      audio.onended = () => {
        if (language === 'hu-HU') {
          setIsPlayingHungarian(false);
        } else {
          setIsPlayingGerman(false);
        }
        URL.revokeObjectURL(audioUrl);
      };

      // Lejátszás
      await audio.play();
    } catch (error) {
      console.error('Hiba a szövegfelolvasás során:', error);
      // Hiba esetén visszaállítjuk az állapotot
      if (language === 'hu-HU') {
        setIsPlayingHungarian(false);
      } else {
        setIsPlayingGerman(false);
      }
    }
  };

  // Kártyamód indítása
  const startFlashcardMode = () => {
    const activeCategory = retailVocabulary.find(cat => cat.id === activeTab);
    if (!activeCategory || activeCategory.items.length === 0) return;

    const randomIndex = Math.floor(Math.random() * activeCategory.items.length);
    setCurrentFlashcard(activeCategory.items[randomIndex]);
    setShowFlashcardAnswer(false);
    setFlashcardMode(true);
  };

  // Következő kártya
  const nextFlashcard = () => {
    const activeCategory = retailVocabulary.find(cat => cat.id === activeTab);
    if (!activeCategory || activeCategory.items.length === 0) return;

    // Véletlenszerűen választunk egy elemet, de ne legyen ugyanaz
    let randomIndex;
    do {
      randomIndex = Math.floor(Math.random() * activeCategory.items.length);
    } while (
      activeCategory.items.length > 1 &&
      currentFlashcard &&
      activeCategory.items[randomIndex].german === currentFlashcard.german
    );

    setCurrentFlashcard(activeCategory.items[randomIndex]);
    setShowFlashcardAnswer(false);
  };

  // Kilépés a kártyamódból
  const exitFlashcardMode = () => {
    setFlashcardMode(false);
    setCurrentFlashcard(null);
  };

  // Szituációs gyakorlatok
  const retailSituations = [
    {
      scenario: "Vásárló üdvözlése az üzletben",
      customerLine: "Guten Tag!",
      correctResponse: "Guten Tag! Kann ich Ihnen helfen?",
      alternatives: [
        "Guten Tag! Wie kann ich Ihnen behilflich sein?",
        "Guten Tag! Suchen Sie etwas Bestimmtes?",
        "Willkommen in unserem Geschäft! Kann ich Ihnen helfen?"
      ]
    },
    {
      scenario: "Vásárló kérdezi, hol találja a terméket",
      customerLine: "Entschuldigung, wo finde ich die Schuhabteilung?",
      correctResponse: "Die Schuhabteilung ist im ersten Stock.",
      alternatives: [
        "Die Schuhabteilung befindet sich im ersten Stock.",
        "Für Schuhe müssen Sie in den ersten Stock gehen.",
        "Schuhe finden Sie im ersten Stock."
      ]
    },
    {
      scenario: "Vásárló érdeklődik egy termék ára iránt",
      customerLine: "Was kostet dieser Artikel?",
      correctResponse: "Dieser Artikel kostet 29,99 Euro.",
      alternatives: [
        "Der Preis beträgt 29,99 Euro.",
        "Es kostet 29,99 Euro.",
        "Dieser Artikel ist für 29,99 Euro erhältlich."
      ]
    },
    {
      scenario: "Vásárló segítséget kér a méretválasztásban",
      customerLine: "Können Sie mir bei der Größe helfen?",
      correctResponse: "Natürlich. Welche Größe tragen Sie normalerweise?",
      alternatives: [
        "Gerne. Welche Größe haben Sie?",
        "Selbstverständlich. Welche Größe suchen Sie?",
        "Ja, natürlich. Wissen Sie Ihre Größe?"
      ]
    },
    {
      scenario: "Vásárló reklamálni szeretne",
      customerLine: "Ich möchte eine Reklamation machen.",
      correctResponse: "Es tut mir leid für die Unannehmlichkeiten. Wie kann ich Ihnen helfen?",
      alternatives: [
        "Das tut mir leid. Was ist das Problem?",
        "Natürlich. Bitte erklären Sie mir, was das Problem ist.",
        "Ich helfe Ihnen gerne. Was ist passiert?"
      ]
    },
    {
      scenario: "Vásárló fizetni szeretne",
      customerLine: "Ich möchte bezahlen.",
      correctResponse: "Gerne. Möchten Sie bar oder mit Karte bezahlen?",
      alternatives: [
        "Natürlich. Wie möchten Sie bezahlen?",
        "Die Kasse ist hier. Bar oder mit Karte?",
        "Selbstverständlich. Bevorzugen Sie Barzahlung oder Kartenzahlung?"
      ]
    },
    {
      scenario: "Vásárló kérdezi, van-e a termékből más színben",
      customerLine: "Haben Sie dieses Produkt auch in anderen Farben?",
      correctResponse: "Ja, wir haben dieses Produkt auch in Blau, Rot und Schwarz.",
      alternatives: [
        "Dieses Produkt ist auch in anderen Farben erhältlich: Blau, Rot und Schwarz.",
        "Wir führen dieses Modell auch in Blau, Rot und Schwarz.",
        "Ja, es gibt dieses Produkt auch in Blau, Rot und Schwarz."
      ]
    },
    {
      scenario: "Vásárló kérdezi a nyitvatartási időt",
      customerLine: "Wie lange haben Sie heute geöffnet?",
      correctResponse: "Wir haben heute bis 20 Uhr geöffnet.",
      alternatives: [
        "Unsere Öffnungszeiten sind heute bis 20 Uhr.",
        "Heute schließen wir um 20 Uhr.",
        "Das Geschäft ist heute bis 20 Uhr geöffnet."
      ]
    },
    {
      scenario: "Vásárló kérdezi, hol van a mosdó",
      customerLine: "Wo ist die Toilette, bitte?",
      correctResponse: "Die Toiletten befinden sich im ersten Stock neben den Umkleidekabinen.",
      alternatives: [
        "Die Toiletten sind im ersten Stock, neben den Umkleidekabinen.",
        "Im ersten Stock finden Sie die Toiletten neben den Umkleidekabinen.",
        "Gehen Sie bitte in den ersten Stock, dort sind die Toiletten neben den Umkleidekabinen."
      ]
    },
    {
      scenario: "Vásárló kérdezi, van-e akció",
      customerLine: "Gibt es zurzeit Sonderangebote?",
      correctResponse: "Ja, wir haben aktuell 20% Rabatt auf alle Winterartikel.",
      alternatives: [
        "Ja, alle Winterartikel sind um 20% reduziert.",
        "Wir bieten momentan 20% Rabatt auf Winterartikel an.",
        "Aktuell haben wir eine Aktion: 20% auf alle Winterartikel."
      ]
    }
  ];

  // Válaszlehetőségek keverése
  const shuffleResponses = (situation: typeof retailSituations[0]) => {
    return [situation.correctResponse, ...situation.alternatives].sort(() => Math.random() - 0.5);
  };

  // Szituációs gyakorlat indítása
  const startSituationMode = () => {
    const randomIndex = Math.floor(Math.random() * retailSituations.length);
    const situation = retailSituations[randomIndex];

    setCurrentSituation({
      ...situation,
      shuffledResponses: shuffleResponses(situation)
    });

    setSelectedResponse(null);
    setShowSituationFeedback(false);
    setSituationMode(true);
  };

  // Válasz kiválasztása
  const selectResponse = (response: string) => {
    setSelectedResponse(response);
    setShowSituationFeedback(true);
  };

  // Következő szituáció
  const nextSituation = () => {
    let randomIndex;
    do {
      randomIndex = Math.floor(Math.random() * retailSituations.length);
    } while (
      retailSituations.length > 1 &&
      currentSituation &&
      retailSituations[randomIndex].scenario === currentSituation.scenario
    );

    const situation = retailSituations[randomIndex];

    setCurrentSituation({
      ...situation,
      shuffledResponses: shuffleResponses(situation)
    });

    setSelectedResponse(null);
    setShowSituationFeedback(false);
  };

  // Kilépés a szituációs módból
  const exitSituationMode = () => {
    setSituationMode(false);
    setCurrentSituation(null);
  };

  // Szókincsteszt funkciók
  const getAllVocabularyItems = () => {
    return retailVocabulary.flatMap(category => category.items);
  };

  const getRandomOptions = (correctAnswer: string, allItems: VocationalItem[], language: 'de' | 'hu', count: number = 3) => {
    const otherItems = allItems.filter(item =>
      language === 'de'
        ? item.hungarian !== correctAnswer
        : item.german !== correctAnswer
    );

    const shuffled = [...otherItems].sort(() => Math.random() - 0.5);
    return shuffled.slice(0, count).map(item => language === 'de' ? item.hungarian : item.german);
  };

  const startQuizMode = () => {
    const allItems = getAllVocabularyItems();
    const randomItem = allItems[Math.floor(Math.random() * allItems.length)];
    const language = Math.random() > 0.5 ? 'de' : 'hu';

    const word = language === 'de' ? randomItem.german : randomItem.hungarian;
    const correctAnswer = language === 'de' ? randomItem.hungarian : randomItem.german;
    const options = getRandomOptions(correctAnswer, allItems, language);

    // Shuffle options and include correct answer
    const allOptions = [...options, correctAnswer].sort(() => Math.random() - 0.5);

    setCurrentQuiz({
      word,
      correctAnswer,
      options: allOptions,
      language
    });

    setSelectedAnswer(null);
    setShowQuizFeedback(false);
    setQuizMode(true);
  };

  const selectAnswer = (answer: string) => {
    setSelectedAnswer(answer);
    setShowQuizFeedback(true);

    if (answer === currentQuiz?.correctAnswer) {
      setQuizScore(prev => ({
        correct: prev.correct + 1,
        total: prev.total + 1
      }));
    } else {
      setQuizScore(prev => ({
        ...prev,
        total: prev.total + 1
      }));
    }
  };

  const nextQuiz = () => {
    const allItems = getAllVocabularyItems();
    const randomItem = allItems[Math.floor(Math.random() * allItems.length)];
    const language = Math.random() > 0.5 ? 'de' : 'hu';

    const word = language === 'de' ? randomItem.german : randomItem.hungarian;
    const correctAnswer = language === 'de' ? randomItem.hungarian : randomItem.german;
    const options = getRandomOptions(correctAnswer, allItems, language);

    // Shuffle options and include correct answer
    const allOptions = [...options, correctAnswer].sort(() => Math.random() - 0.5);

    setCurrentQuiz({
      word,
      correctAnswer,
      options: allOptions,
      language
    });

    setSelectedAnswer(null);
    setShowQuizFeedback(false);
  };

  const exitQuizMode = () => {
    setQuizMode(false);
    setCurrentQuiz(null);
    setQuizScore({ correct: 0, total: 0 });
  };

  // Szerepjáték funkciók
  const retailRoleplays = [
    {
      scenario: 'Vásárló üdvözlése és segítségnyújtás',
      customerRole: 'Vásárló',
      sellerRole: 'Eladó',
      dialogue: [
        {
          speaker: 'customer',
          text: 'Guten Tag!',
          translation: 'Jó napot!'
        },
        {
          speaker: 'seller',
          text: 'Guten Tag! Kann ich Ihnen helfen?',
          translation: 'Jó napot! Segíthetek valamiben?'
        },
        {
          speaker: 'customer',
          text: 'Ich suche ein Geschenk für meine Mutter.',
          translation: 'Ajándékot keresek anyukámnak.'
        },
        {
          speaker: 'seller',
          text: 'Was für Geschenke mag Ihre Mutter?',
          translation: 'Milyen ajándékokat szeret az édesanyja?'
        },
        {
          speaker: 'customer',
          text: 'Sie liest gerne Bücher und mag Schokolade.',
          translation: 'Szeret olvasni és szereti a csokoládét.'
        },
        {
          speaker: 'seller',
          text: 'Wir haben eine schöne Auswahl an Büchern in der Buchabteilung. Und daneben finden Sie auch feine Schokoladen.',
          translation: 'Szép választékunk van könyvekből a könyvrészlegen. És mellette finom csokoládékat is talál.'
        },
        {
          speaker: 'customer',
          text: 'Danke für Ihre Hilfe!',
          translation: 'Köszönöm a segítségét!'
        },
        {
          speaker: 'seller',
          text: 'Gerne! Wenn Sie weitere Fragen haben, stehe ich Ihnen zur Verfügung.',
          translation: 'Szívesen! Ha további kérdései vannak, állok rendelkezésére.'
        }
      ]
    },
    {
      scenario: 'Termék keresése és fizetés',
      customerRole: 'Vásárló',
      sellerRole: 'Eladó',
      dialogue: [
        {
          speaker: 'customer',
          text: 'Entschuldigung, wo finde ich Sportschuhe?',
          translation: 'Elnézést, hol találok sportcipőket?'
        },
        {
          speaker: 'seller',
          text: 'Sportschuhe finden Sie im ersten Stock, in der Sportabteilung.',
          translation: 'A sportcipőket az első emeleten találja, a sportrészlegen.'
        },
        {
          speaker: 'customer',
          text: 'Danke. Und haben Sie auch Laufschuhe?',
          translation: 'Köszönöm. És futócipőik is vannak?'
        },
        {
          speaker: 'seller',
          text: 'Ja, wir haben verschiedene Modelle von Laufschuhen. Welche Größe suchen Sie?',
          translation: 'Igen, különböző futócipő modelljeink vannak. Milyen méretet keres?'
        },
        {
          speaker: 'customer',
          text: 'Ich brauche Größe 42.',
          translation: '42-es méretre van szükségem.'
        },
        {
          speaker: 'seller',
          text: 'Kein Problem. Folgen Sie mir bitte zur Kasse, wenn Sie etwas gefunden haben.',
          translation: 'Semmi gond. Kérem, kövessen a pénztárhoz, ha talált valamit.'
        },
        {
          speaker: 'customer',
          text: 'Ich möchte diese Schuhe kaufen. Kann ich mit Karte bezahlen?',
          translation: 'Szeretném megvenni ezeket a cipőket. Fizethetek kártyával?'
        },
        {
          speaker: 'seller',
          text: 'Natürlich. Das macht 79,95 Euro. Bitte geben Sie Ihre PIN ein.',
          translation: 'Természetesen. Ez 79,95 euró. Kérem, adja meg a PIN-kódját.'
        }
      ]
    },
    {
      scenario: 'Reklamáció kezelése',
      customerRole: 'Vásárló',
      sellerRole: 'Eladó',
      dialogue: [
        {
          speaker: 'customer',
          text: 'Guten Tag, ich habe ein Problem mit diesem Produkt.',
          translation: 'Jó napot, problémám van ezzel a termékkel.'
        },
        {
          speaker: 'seller',
          text: 'Es tut mir leid, das zu hören. Was ist das Problem?',
          translation: 'Sajnálom, hogy ezt hallom. Mi a probléma?'
        },
        {
          speaker: 'customer',
          text: 'Es funktioniert nicht richtig. Ich habe es gestern gekauft.',
          translation: 'Nem működik rendesen. Tegnap vásároltam.'
        },
        {
          speaker: 'seller',
          text: 'Haben Sie den Kassenbon dabei?',
          translation: 'Önnel van a blokk?'
        },
        {
          speaker: 'customer',
          text: 'Ja, hier ist er.',
          translation: 'Igen, itt van.'
        },
        {
          speaker: 'seller',
          text: 'Danke. Möchten Sie das Produkt umtauschen oder eine Rückerstattung?',
          translation: 'Köszönöm. Szeretné kicserélni a terméket vagy visszatérítést kér?'
        },
        {
          speaker: 'customer',
          text: 'Ich hätte gerne eine Rückerstattung.',
          translation: 'Szeretnék visszatérítést kapni.'
        },
        {
          speaker: 'seller',
          text: 'Kein Problem. Ich erledige das sofort für Sie. Entschuldigen Sie die Unannehmlichkeiten.',
          translation: 'Semmi gond. Azonnal intézkedem. Elnézést a kellemetlenségért.'
        }
      ]
    }
  ];

  const startRoleplayMode = () => {
    const randomIndex = Math.floor(Math.random() * retailRoleplays.length);
    setCurrentRoleplay({
      ...retailRoleplays[randomIndex],
      currentStep: 0
    });
    setRoleplayMode(true);
  };

  const nextRoleplayStep = () => {
    if (currentRoleplay && currentRoleplay.currentStep < currentRoleplay.dialogue.length - 1) {
      setCurrentRoleplay({
        ...currentRoleplay,
        currentStep: currentRoleplay.currentStep + 1
      });
    }
  };

  const prevRoleplayStep = () => {
    if (currentRoleplay && currentRoleplay.currentStep > 0) {
      setCurrentRoleplay({
        ...currentRoleplay,
        currentStep: currentRoleplay.currentStep - 1
      });
    }
  };

  const nextRoleplay = () => {
    let randomIndex;
    do {
      randomIndex = Math.floor(Math.random() * retailRoleplays.length);
    } while (
      retailRoleplays.length > 1 &&
      currentRoleplay &&
      retailRoleplays[randomIndex].scenario === currentRoleplay.scenario
    );

    setCurrentRoleplay({
      ...retailRoleplays[randomIndex],
      currentStep: 0
    });
  };

  const exitRoleplayMode = () => {
    setRoleplayMode(false);
    setCurrentRoleplay(null);
  };

  return (
    <div className="container mx-auto px-4 py-8 mb-20 md:mb-0 md:pt-24">
      <div className="flex flex-col items-center mb-8">
        <div className="flex items-center gap-3 mb-2">
          <div className="p-2 rounded-full bg-language-quaternary/20">
            <ShoppingBag className="w-6 h-6 text-language-quaternary" />
          </div>
          <h1 className="text-2xl font-bold">Bolti eladó szakmai szókincs</h1>
        </div>
        <p className="text-gray-600 text-center max-w-2xl">
          Tanulj német szakmai szókincset a bolti eladó munkakörben való kommunikációhoz.
          Fedezd fel a különböző kategóriákat és gyakorold a szavakat kártyákkal.
        </p>
      </div>

      <Card className="w-full max-w-4xl mx-auto">
        <CardHeader>
          <div className="flex flex-col md:flex-row md:justify-between md:items-center gap-4">
            <CardTitle>Bolti eladó szókincs</CardTitle>

            <div className="flex flex-wrap gap-3 items-center">
              <Button
                variant="outline"
                size="sm"
                onClick={startFlashcardMode}
                className="flex items-center gap-2"
              >
                <BookOpen className="w-4 h-4" />
                <span>Kártyamód</span>
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={startSituationMode}
                className="flex items-center gap-2"
              >
                <MessageSquare className="w-4 h-4" />
                <span>Szituációs gyakorlat</span>
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={startQuizMode}
                className="flex items-center gap-2"
              >
                <Gamepad2 className="w-4 h-4" />
                <span>Szókincsteszt</span>
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={startRoleplayMode}
                className="flex items-center gap-2"
              >
                <MessageSquare className="w-4 h-4" />
                <span>Szerepjáték</span>
              </Button>
            </div>
          </div>
          <CardDescription>
            Válassz kategóriát és tanuld meg a szakmai kifejezéseket
          </CardDescription>
        </CardHeader>

        <CardContent>
          {roleplayMode ? (
            <div className="flex flex-col items-center">
              <div className="mb-4 flex items-center gap-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={exitRoleplayMode}
                >
                  Vissza a listához
                </Button>
              </div>

              <Card className="w-full max-w-2xl p-6 mb-4 border-2 border-language-quaternary/30">
                <div className="text-center mb-6">
                  <Badge variant="outline" className="mb-4">
                    {currentRoleplay?.scenario}
                  </Badge>

                  <div className="flex justify-between items-center mb-6">
                    <Badge variant="outline" className="px-3 py-1">
                      {currentRoleplay?.customerRole}: <span className="font-normal">{currentRoleplay?.dialogue[currentRoleplay?.currentStep || 0]?.speaker === 'customer' ? '👤' : ''}</span>
                    </Badge>
                    <Badge variant="outline" className="px-3 py-1">
                      {currentRoleplay?.sellerRole}: <span className="font-normal">{currentRoleplay?.dialogue[currentRoleplay?.currentStep || 0]?.speaker === 'seller' ? '👤' : ''}</span>
                    </Badge>
                  </div>

                  <div className="bg-gray-50 p-4 rounded-lg mb-6">
                    <div className="text-xl font-medium mb-2">
                      {currentRoleplay?.dialogue[currentRoleplay?.currentStep || 0]?.text}
                    </div>
                    <div className="text-sm text-gray-600">
                      {currentRoleplay?.dialogue[currentRoleplay?.currentStep || 0]?.translation}
                    </div>
                    <Button
                      variant="ghost"
                      size="sm"
                      className="mt-2"
                      onClick={() => handleSpeech(
                        currentRoleplay?.dialogue[currentRoleplay?.currentStep || 0]?.text || '',
                        'de-DE'
                      )}
                      disabled={isPlayingGerman || isPlayingHungarian}
                    >
                      {isPlayingGerman || isPlayingHungarian ? (
                        <Loader2 className="h-4 w-4 mr-1 animate-spin" />
                      ) : (
                        <Volume2 className="h-4 w-4 mr-1" />
                      )}
                      <span>Meghallgatás</span>
                    </Button>
                  </div>

                  <div className="flex justify-between items-center mb-4">
                    <Button
                      variant="outline"
                      onClick={prevRoleplayStep}
                      disabled={!currentRoleplay || currentRoleplay.currentStep === 0}
                    >
                      Előző
                    </Button>

                    <div className="text-sm text-gray-500">
                      {(currentRoleplay?.currentStep || 0) + 1} / {currentRoleplay?.dialogue.length}
                    </div>

                    <Button
                      variant="outline"
                      onClick={nextRoleplayStep}
                      disabled={!currentRoleplay || currentRoleplay.currentStep === (currentRoleplay.dialogue.length - 1)}
                    >
                      Következő
                    </Button>
                  </div>

                  {currentRoleplay && currentRoleplay.currentStep === (currentRoleplay.dialogue.length - 1) && (
                    <Button onClick={nextRoleplay} className="mt-4">
                      <RefreshCw className="w-4 h-4 mr-2" />
                      Új párbeszéd
                    </Button>
                  )}
                </div>
              </Card>
            </div>
          ) : quizMode ? (
            <div className="flex flex-col items-center">
              <div className="mb-4 flex items-center gap-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={exitQuizMode}
                >
                  Vissza a listához
                </Button>
              </div>

              <Card className="w-full max-w-2xl p-6 mb-4 border-2 border-language-quaternary/30">
                <div className="text-center mb-6">
                  <div className="flex justify-between items-center mb-6">
                    <Badge variant="outline" className="px-3 py-1">
                      {currentQuiz?.language === 'de' ? 'Német → Magyar' : 'Magyar → Német'}
                    </Badge>
                    <Badge variant="outline" className="px-3 py-1">
                      Pontszám: {quizScore.correct}/{quizScore.total}
                    </Badge>
                  </div>

                  <div className="flex items-center justify-center gap-2 mb-6">
                    <div className="p-2 rounded-full bg-gray-100">
                      <Gamepad2 className="w-5 h-5 text-gray-600" />
                    </div>
                    <div className="text-xl font-medium">
                      {currentQuiz?.word}
                    </div>
                    <Button
                      variant="ghost"
                      size="sm"
                      className="p-1 h-auto"
                      onClick={() => handleSpeech(
                        currentQuiz?.word || '',
                        currentQuiz?.language === 'de' ? 'de-DE' : 'hu-HU'
                      )}
                      disabled={isPlayingGerman || isPlayingHungarian}
                    >
                      {isPlayingGerman || isPlayingHungarian ? (
                        <Loader2 className="h-4 w-4 animate-spin" />
                      ) : (
                        <Volume2 className="h-4 w-4" />
                      )}
                    </Button>
                  </div>

                  <div className="space-y-3 mb-6">
                    <h3 className="text-sm font-medium text-gray-500">Válaszd ki a helyes fordítást:</h3>
                    <div className="grid grid-cols-1 gap-2">
                      {currentQuiz?.options.map((option, index) => (
                        <Button
                          key={index}
                          variant={selectedAnswer === option ? "default" : "outline"}
                          className={`justify-start text-left py-3 px-4 ${
                            showQuizFeedback && option === currentQuiz.correctAnswer
                              ? "border-green-500 bg-green-50"
                              : showQuizFeedback && selectedAnswer === option && option !== currentQuiz.correctAnswer
                              ? "border-red-500 bg-red-50"
                              : ""
                          }`}
                          onClick={() => selectAnswer(option)}
                          disabled={showQuizFeedback}
                        >
                          {option}
                        </Button>
                      ))}
                    </div>
                  </div>

                  {showQuizFeedback && (
                    <div className={`p-4 rounded-lg mb-4 ${selectedAnswer === currentQuiz?.correctAnswer ? 'bg-green-50 border border-green-200' : 'bg-red-50 border border-red-200'}`}>
                      <div className="flex items-center gap-2 mb-2">
                        {selectedAnswer === currentQuiz?.correctAnswer ? (
                          <>
                            <ThumbsUp className="w-5 h-5 text-green-500" />
                            <span className="font-medium text-green-700">Helyes válasz!</span>
                          </>
                        ) : (
                          <>
                            <ThumbsDown className="w-5 h-5 text-red-500" />
                            <span className="font-medium text-red-700">Helytelen válasz!</span>
                          </>
                        )}
                      </div>
                      {selectedAnswer !== currentQuiz?.correctAnswer && (
                        <div className="text-sm text-gray-600 mt-2">
                          <p>A helyes válasz:</p>
                          <p className="font-medium mt-1">{currentQuiz?.correctAnswer}</p>
                        </div>
                      )}
                    </div>
                  )}

                  {showQuizFeedback && (
                    <Button onClick={nextQuiz} className="mt-2">
                      <RefreshCw className="w-4 h-4 mr-2" />
                      Következő kérdés
                    </Button>
                  )}
                </div>
              </Card>
            </div>
          ) : situationMode ? (
            <div className="flex flex-col items-center">
              <div className="mb-4 flex items-center gap-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={exitSituationMode}
                >
                  Vissza a listához
                </Button>
              </div>

              <Card className="w-full max-w-2xl p-6 mb-4 border-2 border-language-quaternary/30">
                <div className="text-center mb-6">
                  <Badge variant="outline" className="mb-4">
                    {currentSituation?.scenario}
                  </Badge>
                  <div className="flex items-center justify-center gap-2 mb-6">
                    <div className="p-2 rounded-full bg-gray-100">
                      <MessageSquare className="w-5 h-5 text-gray-600" />
                    </div>
                    <div className="text-xl font-medium">
                      {currentSituation?.customerLine}
                    </div>
                    <Button
                      variant="ghost"
                      size="sm"
                      className="p-1 h-auto"
                      onClick={() => handleSpeech(currentSituation?.customerLine || '', 'de-DE')}
                      disabled={isPlayingGerman || isPlayingHungarian}
                    >
                      {isPlayingGerman ? (
                        <Loader2 className="h-4 w-4 animate-spin" />
                      ) : (
                        <Volume2 className="h-4 w-4" />
                      )}
                    </Button>
                  </div>

                  <div className="space-y-3 mb-6">
                    <h3 className="text-sm font-medium text-gray-500">Válassz egy megfelelő választ:</h3>
                    <div className="grid grid-cols-1 gap-2">
                      {currentSituation?.shuffledResponses?.map((response, index) => (
                          <Button
                            key={index}
                            variant={selectedResponse === response ? "default" : "outline"}
                            className="justify-start text-left py-3 px-4"
                            onClick={() => selectResponse(response)}
                            disabled={showSituationFeedback}
                          >
                            {response}
                          </Button>
                        ))}
                    </div>
                  </div>

                  {showSituationFeedback && (
                    <div className={`p-4 rounded-lg mb-4 ${selectedResponse === currentSituation?.correctResponse ? 'bg-green-50 border border-green-200' : 'bg-amber-50 border border-amber-200'}`}>
                      <div className="flex items-center gap-2 mb-2">
                        {selectedResponse === currentSituation?.correctResponse ? (
                          <>
                            <ThumbsUp className="w-5 h-5 text-green-500" />
                            <span className="font-medium text-green-700">Helyes válasz!</span>
                          </>
                        ) : (
                          <>
                            <ThumbsDown className="w-5 h-5 text-amber-500" />
                            <span className="font-medium text-amber-700">Próbáld újra!</span>
                          </>
                        )}
                      </div>
                      {selectedResponse !== currentSituation?.correctResponse && (
                        <div className="text-sm text-gray-600 mt-2">
                          <p>A helyes válasz:</p>
                          <p className="font-medium mt-1">{currentSituation?.correctResponse}</p>
                        </div>
                      )}
                    </div>
                  )}

                  {showSituationFeedback && (
                    <Button onClick={nextSituation} className="mt-2">
                      <RefreshCw className="w-4 h-4 mr-2" />
                      Következő szituáció
                    </Button>
                  )}
                </div>
              </Card>
            </div>
          ) : flashcardMode ? (
            <div className="flex flex-col items-center">
              <div className="mb-4 flex flex-col md:flex-row gap-4">
                <div className="flex items-center gap-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={exitFlashcardMode}
                  >
                    Vissza a listához
                  </Button>
                </div>

                <div className="flex items-center gap-2">
                  <Button
                    variant={flashcardDirection === 'de-hu' ? 'default' : 'outline'}
                    size="sm"
                    onClick={() => setFlashcardDirection('de-hu')}
                  >
                    Német → Magyar
                  </Button>
                  <Button
                    variant={flashcardDirection === 'hu-de' ? 'default' : 'outline'}
                    size="sm"
                    onClick={() => setFlashcardDirection('hu-de')}
                  >
                    Magyar → Német
                  </Button>
                </div>
              </div>

              <Card className="w-full max-w-md h-64 flex items-center justify-center p-6 mb-4 border-2 border-language-quaternary/30">
                <div className="text-center">
                  <div className="text-xl font-bold mb-4">
                    {flashcardDirection === 'de-hu' ? currentFlashcard?.german : currentFlashcard?.hungarian}
                  </div>

                  <Button
                    variant="ghost"
                    size="sm"
                    className="mb-4"
                    onClick={() => handleSpeech(
                      flashcardDirection === 'de-hu' ? currentFlashcard?.german || '' : currentFlashcard?.hungarian || '',
                      flashcardDirection === 'de-hu' ? 'de-DE' : 'hu-HU'
                    )}
                    disabled={isPlayingGerman || isPlayingHungarian}
                  >
                    {isPlayingGerman || isPlayingHungarian ? (
                      <Loader2 className="h-4 w-4 mr-1 animate-spin" />
                    ) : (
                      <Volume2 className="h-4 w-4 mr-1" />
                    )}
                    <span>Meghallgatás</span>
                  </Button>

                  {showFlashcardAnswer ? (
                    <div className="space-y-4 w-full">
                      <div className="text-center">
                        <Badge className="text-lg px-3 py-1">
                          {flashcardDirection === 'de-hu' ? currentFlashcard?.hungarian : currentFlashcard?.german}
                        </Badge>
                      </div>

                      {currentFlashcard?.example && (
                        <div className="text-sm italic text-center mt-4">
                          {currentFlashcard.example}
                        </div>
                      )}

                      {currentFlashcard?.notes && (
                        <div className="text-xs text-gray-500 text-center mt-2">
                          {currentFlashcard.notes}
                        </div>
                      )}
                    </div>
                  ) : (
                    <Button onClick={() => setShowFlashcardAnswer(true)}>
                      Mutasd a választ
                    </Button>
                  )}
                </div>
              </Card>

              {showFlashcardAnswer && (
                <div className="flex justify-center">
                  <Button onClick={nextFlashcard}>
                    Következő szó
                  </Button>
                </div>
              )}
            </div>
          ) : (
            <>
              <div className="mb-6">
                <div className="flex flex-wrap gap-4 mb-4">
                  <div className="flex items-center space-x-2">
                    <Switch
                      id="show-german"
                      checked={showGerman}
                      onCheckedChange={setShowGerman}
                    />
                    <Label htmlFor="show-german">Német</Label>
                  </div>

                  <div className="flex items-center space-x-2">
                    <Switch
                      id="show-hungarian"
                      checked={showHungarian}
                      onCheckedChange={setShowHungarian}
                    />
                    <Label htmlFor="show-hungarian">Magyar</Label>
                  </div>

                  <div className="flex items-center space-x-2">
                    <Switch
                      id="show-examples"
                      checked={showExamples}
                      onCheckedChange={setShowExamples}
                    />
                    <Label htmlFor="show-examples">Példamondatok</Label>
                  </div>

                  <div className="flex items-center space-x-2">
                    <Switch
                      id="show-notes"
                      checked={showNotes}
                      onCheckedChange={setShowNotes}
                    />
                    <Label htmlFor="show-notes">Megjegyzések</Label>
                  </div>
                </div>

                <div className="flex flex-col md:flex-row gap-6">
                  <Tabs defaultValue={activeTab} value={activeTab} onValueChange={setActiveTab} className="flex flex-col md:flex-row gap-6 w-full">
                    <div className="md:w-64 shrink-0">
                      <TabsList className="flex flex-col w-full h-auto">
                        {retailVocabulary.map(category => (
                          <TabsTrigger
                            key={category.id}
                            value={category.id}
                            className={`justify-start w-full text-left py-3 ${
                              activeTab === category.id ? 'font-bold' : ''
                            }`}
                          >
                            {category.title}
                          </TabsTrigger>
                        ))}
                      </TabsList>
                    </div>

                    <div className="flex-1">
                      {retailVocabulary.map(category => (
                        <TabsContent key={category.id} value={category.id} className="space-y-4 mt-0">
                          <div className="grid grid-cols-1 gap-2">
                            {category.items.map((item, index) => (
                          <div
                            key={`${category.id}-${index}`}
                            className="p-3 border rounded-lg hover:bg-gray-50 transition-colors"
                          >
                            <div className="flex flex-wrap justify-between gap-y-2">
                              {showGerman && (
                                <div className="font-medium flex items-center gap-2">
                                  {item.german}
                                  <Button
                                    variant="ghost"
                                    size="sm"
                                    className="p-1 h-auto"
                                    onClick={() => handleSpeech(item.german, 'de-DE')}
                                    disabled={isPlayingGerman || isPlayingHungarian}
                                  >
                                    {isPlayingGerman && index === category.items.indexOf(item) ? (
                                      <Loader2 className="h-3 w-3 animate-spin" />
                                    ) : (
                                      <Volume2 className="h-3 w-3" />
                                    )}
                                  </Button>
                                </div>
                              )}

                              {showHungarian && (
                                <div className="text-gray-700 flex items-center gap-2">
                                  {item.hungarian}
                                  <Button
                                    variant="ghost"
                                    size="sm"
                                    className="p-1 h-auto"
                                    onClick={() => handleSpeech(item.hungarian, 'hu-HU')}
                                    disabled={isPlayingGerman || isPlayingHungarian}
                                  >
                                    {isPlayingHungarian && index === category.items.indexOf(item) ? (
                                      <Loader2 className="h-3 w-3 animate-spin" />
                                    ) : (
                                      <Volume2 className="h-3 w-3" />
                                    )}
                                  </Button>
                                </div>
                              )}
                            </div>

                            {showExamples && item.example && (
                              <div className="mt-1 text-sm italic text-gray-600">
                                {item.example}
                              </div>
                            )}

                            {showNotes && item.notes && (
                              <div className="mt-1 text-xs text-gray-500">
                                {item.notes}
                              </div>
                            )}
                          </div>
                        ))}
                      </div>
                        </TabsContent>
                      ))}
                    </div>
                  </Tabs>
                </div>
              </div>
            </>
          )}
        </CardContent>

        <CardFooter className="flex justify-between border-t pt-4">
          <div className="text-sm text-gray-500">
            Összesen {retailVocabulary.reduce((acc, category) => acc + category.items.length, 0)} szakmai kifejezés
          </div>

          {!flashcardMode && !situationMode && !quizMode && !roleplayMode && (
            <div className="flex gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={startFlashcardMode}
                className="flex items-center gap-2"
              >
                <BookOpen className="w-4 h-4" />
                <span>Kártyamód</span>
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={startSituationMode}
                className="flex items-center gap-2"
              >
                <MessageSquare className="w-4 h-4" />
                <span>Szituációs gyakorlat</span>
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={startQuizMode}
                className="flex items-center gap-2"
              >
                <Gamepad2 className="w-4 h-4" />
                <span>Szókincsteszt</span>
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={startRoleplayMode}
                className="flex items-center gap-2"
              >
                <MessageSquare className="w-4 h-4" />
                <span>Szerepjáték</span>
              </Button>
            </div>
          )}
        </CardFooter>
      </Card>

      <div className="mt-8 max-w-4xl mx-auto">
        <h2 className="text-xl font-bold mb-4">További tanulási lehetőségek</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-8">
          <Card className="hover:shadow-lg transition-shadow">
            <CardHeader>
              <CardTitle className="text-lg flex items-center gap-2">
                <MessageSquare className="h-5 w-5 text-language-quaternary" />
                Szakmai mondatok
              </CardTitle>
              <CardDescription>
                Tanulj hasznos mondatokat és kifejezéseket a bolti eladó munkakörben
              </CardDescription>
            </CardHeader>
            <CardContent>
              <p className="text-sm text-gray-600 mb-4">
                Fedezd fel a különböző kategóriákba rendezett szakmai mondatokat, amelyeket bolti eladóként használhatsz a mindennapi munkád során.
              </p>
              <Button asChild>
                <Link to="/vocational/retail/phrases">
                  Mondatok megtekintése
                </Link>
              </Button>
            </CardContent>
          </Card>

          <Card className="hover:shadow-lg transition-shadow">
            <CardHeader>
              <CardTitle className="text-lg flex items-center gap-2">
                <Gamepad2 className="h-5 w-5 text-language-quaternary" />
                Szakmai játékok
              </CardTitle>
              <CardDescription>
                Gyakorold a szakmai szókincset interaktív játékokkal
              </CardDescription>
            </CardHeader>
            <CardContent>
              <p className="text-sm text-gray-600 mb-4">
                Teszteld a tudásodat és fejleszd a nyelvi készségeidet különböző játékokkal, amelyek segítenek a szakmai szókincs elsajátításában.
              </p>
              <Button asChild>
                <Link to="/vocational/retail/games">
                  Játékok indítása
                </Link>
              </Button>
            </CardContent>
          </Card>
        </div>

        <h2 className="text-xl font-bold mb-4">Tanulási tippek</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Szókincs bővítése</CardTitle>
            </CardHeader>
            <CardContent>
              <ul className="list-disc pl-5 space-y-2 text-sm">
                <li>Naponta tanulj 5-10 új szót vagy kifejezést</li>
                <li>Használd a kártyamódot a szavak gyakorlására</li>
                <li>Hallgasd meg a kiejtést és ismételd el hangosan</li>
                <li>Készíts saját mondatokat az új szavakkal</li>
              </ul>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Gyakorlati alkalmazás</CardTitle>
            </CardHeader>
            <CardContent>
              <ul className="list-disc pl-5 space-y-2 text-sm">
                <li>Játszd el a bolti szituációkat egy partnerrel</li>
                <li>Készíts szókártyákat a nehezebb kifejezésekből</li>
                <li>Nézz német nyelvű videókat bolti helyzetekről</li>
                <li>Gyakorold a mondatokat különböző kontextusokban</li>
              </ul>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
};

export default VocationalRetail;
