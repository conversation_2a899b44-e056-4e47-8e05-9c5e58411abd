
import React, { useState, useEffect, useMemo } from "react";
import { quizData } from "@/data/languageData";
import { But<PERSON> } from "@/components/ui/button";
import { <PERSON>, Card<PERSON>ontent, Card<PERSON><PERSON>er, <PERSON><PERSON><PERSON><PERSON>, CardFooter, CardDescription } from "@/components/ui/card";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import { CheckCircle2, XCircle, Timer, Brain, BookOpen, GraduationCap, Sparkles, BarChart } from "lucide-react";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from "@/components/ui/select";
import { toast } from "sonner";
import { Progress } from "@/components/ui/progress";
import { Badge } from "@/components/ui/badge";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";

// Nehézségi szintek megfeleltetése a CEFR szinteknek
const difficultyToCEFR = {
  beginner: "A1",
  intermediate: "A2",
  advanced: "B1"
};

const Quiz = () => {
  const [currentQuestionIndex, setCurrentQuestionIndex] = useState(0);
  const [selectedAnswer, setSelectedAnswer] = useState<string>("");
  const [isAnswered, setIsAnswered] = useState(false);
  const [score, setScore] = useState(0);
  const [difficulty, setDifficulty] = useState<string>("all");
  const [timeLeft, setTimeLeft] = useState(30);
  const [quizStarted, setQuizStarted] = useState(false);
  const [quizFinished, setQuizFinished] = useState(false);
  const [shuffledOptions, setShuffledOptions] = useState<string[]>([]);
  const [activeTab, setActiveTab] = useState<string>("all");

  // Filter questions based on selected difficulty
  const activeQuestions = useMemo(() => {
    if (difficulty === "all") {
      return quizData;
    } else {
      return quizData.filter(q => q.difficulty === difficulty);
    }
  }, [difficulty]);

  const currentQuestion = activeQuestions[currentQuestionIndex];

  // Timer effect
  useEffect(() => {
    let timer: NodeJS.Timeout | null = null;

    if (quizStarted && !isAnswered && !quizFinished) {
      timer = setInterval(() => {
        setTimeLeft((prev) => {
          if (prev <= 1) {
            clearInterval(timer as NodeJS.Timeout);
            setIsAnswered(true);
            return 0;
          }
          return prev - 1;
        });
      }, 1000);
    }

    return () => {
      if (timer) clearInterval(timer);
    };
  }, [quizStarted, isAnswered, quizFinished]);

  // Opciók keverése a kérdés betöltésekor
  useEffect(() => {
    if (currentQuestion && currentQuestion.type === "multiple-choice" && currentQuestion.options) {
      // Opciók keverése, hogy ne mindig az első legyen a helyes válasz
      const options = [...currentQuestion.options];
      for (let i = options.length - 1; i > 0; i--) {
        const j = Math.floor(Math.random() * (i + 1));
        [options[i], options[j]] = [options[j], options[i]];
      }
      setShuffledOptions(options);
    }
  }, [currentQuestion]);

  // Check answer
  const checkAnswer = () => {
    setIsAnswered(true);

    const isCorrect = selectedAnswer.toLowerCase() === currentQuestion.answer.toLowerCase();

    if (isCorrect) {
      setScore(score + 1);
      toast.success("Helyes válasz!");
    } else {
      toast.error(`Helytelen! A helyes válasz: ${currentQuestion.answer}`);
    }
  };

  // Go to next question
  const nextQuestion = () => {
    if (currentQuestionIndex < activeQuestions.length - 1) {
      setCurrentQuestionIndex(currentQuestionIndex + 1);
      setSelectedAnswer("");
      setIsAnswered(false);
      setTimeLeft(30);
    } else {
      setQuizFinished(true);
    }
  };

  // Start a new quiz
  const startNewQuiz = () => {
    setCurrentQuestionIndex(0);
    setSelectedAnswer("");
    setIsAnswered(false);
    setScore(0);
    setTimeLeft(30);
    setQuizStarted(true);
    setQuizFinished(false);
  };

  // Kérdések száma nehézségi szintenként
  const questionCountByLevel = useMemo(() => {
    const counts = {
      beginner: quizData.filter(q => q.difficulty === "beginner").length,
      intermediate: quizData.filter(q => q.difficulty === "intermediate").length,
      advanced: quizData.filter(q => q.difficulty === "advanced").length,
      all: quizData.length
    };
    return counts;
  }, []);

  if (!quizStarted) {
    return (
      <div className="container mx-auto px-4 py-8 mb-20 md:mb-0 md:pt-24">
        <h1 className="text-2xl font-bold text-center mb-2">Nyelvi kvíz</h1>
        <p className="text-center text-gray-600 mb-8">Teszteld a tudásodat különböző nehézségi szinteken</p>

        <div className="max-w-4xl mx-auto">
          <Tabs defaultValue="all" value={activeTab} onValueChange={(value) => {
            setActiveTab(value);
            setDifficulty(value);
          }}>
            <div className="flex justify-center mb-6">
              <TabsList className="grid grid-cols-4 w-full max-w-md">
                <TabsTrigger value="all" className="flex items-center gap-1">
                  <GraduationCap className="h-4 w-4" />
                  <span>Mind ({questionCountByLevel.all})</span>
                </TabsTrigger>
                <TabsTrigger value="beginner" className="flex items-center gap-1">
                  <BookOpen className="h-4 w-4" />
                  <span>A1 ({questionCountByLevel.beginner})</span>
                </TabsTrigger>
                <TabsTrigger value="intermediate" className="flex items-center gap-1">
                  <Brain className="h-4 w-4" />
                  <span>A2 ({questionCountByLevel.intermediate})</span>
                </TabsTrigger>
                <TabsTrigger value="advanced" className="flex items-center gap-1">
                  <Sparkles className="h-4 w-4" />
                  <span>B1 ({questionCountByLevel.advanced})</span>
                </TabsTrigger>
              </TabsList>
            </div>

            <TabsContent value="all" className="mt-0">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <GraduationCap className="h-5 w-5 text-language-primary" />
                    Összes nehézségi szint
                  </CardTitle>
                  <CardDescription>
                    Vegyes kérdések minden nehézségi szintről (A1, A2, B1)
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
                    <div className="bg-slate-50 p-4 rounded-lg">
                      <div className="flex items-center gap-2 mb-2">
                        <Badge variant="outline" className="bg-blue-50 text-blue-700 hover:bg-blue-50">A1</Badge>
                        <span className="text-sm font-medium">Kezdő szint</span>
                      </div>
                      <p className="text-sm text-gray-600">{questionCountByLevel.beginner} kérdés</p>
                    </div>
                    <div className="bg-slate-50 p-4 rounded-lg">
                      <div className="flex items-center gap-2 mb-2">
                        <Badge variant="outline" className="bg-green-50 text-green-700 hover:bg-green-50">A2</Badge>
                        <span className="text-sm font-medium">Középhaladó szint</span>
                      </div>
                      <p className="text-sm text-gray-600">{questionCountByLevel.intermediate} kérdés</p>
                    </div>
                    <div className="bg-slate-50 p-4 rounded-lg">
                      <div className="flex items-center gap-2 mb-2">
                        <Badge variant="outline" className="bg-purple-50 text-purple-700 hover:bg-purple-50">B1</Badge>
                        <span className="text-sm font-medium">Haladó szint</span>
                      </div>
                      <p className="text-sm text-gray-600">{questionCountByLevel.advanced} kérdés</p>
                    </div>
                  </div>

                  <div className="text-sm text-gray-600 mb-4">
                    <p>Összesen {questionCountByLevel.all} kérdés, minden kérdésre 30 másodperc áll rendelkezésedre.</p>
                  </div>
                </CardContent>
                <CardFooter>
                  <Button
                    className="w-full"
                    onClick={startNewQuiz}
                  >
                    Kvíz indítása
                  </Button>
                </CardFooter>
              </Card>
            </TabsContent>

            <TabsContent value="beginner" className="mt-0">
              <Card>
                <CardHeader className="bg-blue-50/50">
                  <div className="flex items-center justify-between">
                    <CardTitle className="flex items-center gap-2">
                      <BookOpen className="h-5 w-5 text-blue-600" />
                      A1 - Kezdő szint
                    </CardTitle>
                    <Badge variant="outline" className="bg-blue-100 text-blue-700 hover:bg-blue-100">
                      {questionCountByLevel.beginner} kérdés
                    </Badge>
                  </div>
                  <CardDescription>
                    Alapvető szókincs és kifejezések, egyszerű nyelvtani szerkezetek
                  </CardDescription>
                </CardHeader>
                <CardContent className="pt-4">
                  <div className="text-sm text-gray-600 mb-4">
                    <p>Az A1 szintű kérdések a német nyelv alapjait fedik le:</p>
                    <ul className="list-disc pl-5 mt-2 space-y-1">
                      <li>Köszönések és bemutatkozás</li>
                      <li>Számok és színek</li>
                      <li>Alapvető kérdések és válaszok</li>
                      <li>Mindennapi kifejezések</li>
                    </ul>
                  </div>
                </CardContent>
                <CardFooter>
                  <Button
                    className="w-full bg-blue-600 hover:bg-blue-700"
                    onClick={startNewQuiz}
                  >
                    A1 kvíz indítása
                  </Button>
                </CardFooter>
              </Card>
            </TabsContent>

            <TabsContent value="intermediate" className="mt-0">
              <Card>
                <CardHeader className="bg-green-50/50">
                  <div className="flex items-center justify-between">
                    <CardTitle className="flex items-center gap-2">
                      <Brain className="h-5 w-5 text-green-600" />
                      A2 - Középhaladó szint
                    </CardTitle>
                    <Badge variant="outline" className="bg-green-100 text-green-700 hover:bg-green-100">
                      {questionCountByLevel.intermediate} kérdés
                    </Badge>
                  </div>
                  <CardDescription>
                    Bővebb szókincs, összetettebb mondatok és nyelvtani szerkezetek
                  </CardDescription>
                </CardHeader>
                <CardContent className="pt-4">
                  <div className="text-sm text-gray-600 mb-4">
                    <p>Az A2 szintű kérdések már összetettebb nyelvtani szerkezeteket tartalmaznak:</p>
                    <ul className="list-disc pl-5 mt-2 space-y-1">
                      <li>Mindennapi témák (munka, szabadidő, utazás)</li>
                      <li>Igék ragozása különböző időkben</li>
                      <li>Mondatalkotás és szórend</li>
                      <li>Gyakorlati kifejezések</li>
                    </ul>
                  </div>
                </CardContent>
                <CardFooter>
                  <Button
                    className="w-full bg-green-600 hover:bg-green-700"
                    onClick={startNewQuiz}
                  >
                    A2 kvíz indítása
                  </Button>
                </CardFooter>
              </Card>
            </TabsContent>

            <TabsContent value="advanced" className="mt-0">
              <Card>
                <CardHeader className="bg-purple-50/50">
                  <div className="flex items-center justify-between">
                    <CardTitle className="flex items-center gap-2">
                      <Sparkles className="h-5 w-5 text-purple-600" />
                      B1 - Haladó szint
                    </CardTitle>
                    <Badge variant="outline" className="bg-purple-100 text-purple-700 hover:bg-purple-100">
                      {questionCountByLevel.advanced} kérdés
                    </Badge>
                  </div>
                  <CardDescription>
                    Összetett témák, absztrakt fogalmak és fejlett nyelvtani szerkezetek
                  </CardDescription>
                </CardHeader>
                <CardContent className="pt-4">
                  <div className="text-sm text-gray-600 mb-4">
                    <p>A B1 szintű kérdések már összetettebb témákat érintenek:</p>
                    <ul className="list-disc pl-5 mt-2 space-y-1">
                      <li>Társadalmi és környezetvédelmi témák</li>
                      <li>Technológia és tudomány</li>
                      <li>Összetett mondatszerkezetek</li>
                      <li>Absztrakt fogalmak és kifejezések</li>
                    </ul>
                  </div>
                </CardContent>
                <CardFooter>
                  <Button
                    className="w-full bg-purple-600 hover:bg-purple-700"
                    onClick={startNewQuiz}
                  >
                    B1 kvíz indítása
                  </Button>
                </CardFooter>
              </Card>
            </TabsContent>
          </Tabs>
        </div>
      </div>
    );
  }

  if (quizFinished) {
    const percentage = Math.round((score / activeQuestions.length) * 100);

    // Eredmény értékelése
    let resultMessage = "";
    let resultColor = "";
    let resultIcon = null;

    if (percentage >= 80) {
      resultMessage = "Nagyszerű eredmény! Remekül haladsz a német nyelvtanulással!";
      resultColor = "green";
      resultIcon = <Sparkles className="h-5 w-5 text-green-600" />;
    } else if (percentage >= 60) {
      resultMessage = "Jó eredmény! Folytasd a gyakorlást, hogy még jobb legyél!";
      resultColor = "blue";
      resultIcon = <BookOpen className="h-5 w-5 text-blue-600" />;
    } else {
      resultMessage = "Érdemes még gyakorolnod ezeket a témákat a jobb eredmény érdekében.";
      resultColor = "amber";
      resultIcon = <Brain className="h-5 w-5 text-amber-600" />;
    }

    return (
      <div className="container mx-auto px-4 py-8 mb-20 md:mb-0 md:pt-24">
        <h1 className="text-2xl font-bold text-center mb-2">Kvíz eredmények</h1>
        <p className="text-center text-gray-600 mb-8">
          {difficulty === "all" ? "Vegyes nehézségi szintű kvíz" :
           difficulty === "beginner" ? "A1 - Kezdő szintű kvíz" :
           difficulty === "intermediate" ? "A2 - Középhaladó szintű kvíz" :
           "B1 - Haladó szintű kvíz"}
        </p>

        <Card className="max-w-md mx-auto overflow-hidden">
          <div className={`bg-${resultColor}-50 p-4 flex items-center justify-center`}>
            <div className={`bg-${resultColor}-100 rounded-full p-3`}>
              {resultIcon}
            </div>
          </div>

          <CardContent className="pt-6 text-center">
            <h2 className="text-xl font-bold mb-2">
              Végeredmény: {score} / {activeQuestions.length}
            </h2>

            <div className="mb-6">
              <div className="flex justify-between text-sm text-gray-500 mb-1">
                <span>0%</span>
                <span>50%</span>
                <span>100%</span>
              </div>
              <div className="h-3 w-full bg-gray-100 rounded-full overflow-hidden">
                <div
                  className={`h-full bg-${resultColor}-500 transition-all duration-1000 ease-out`}
                  style={{ width: `${percentage}%` }}
                ></div>
              </div>
              <div className="flex justify-end mt-1">
                <Badge variant="outline" className={`bg-${resultColor}-50 text-${resultColor}-700`}>
                  {percentage}%
                </Badge>
              </div>
            </div>

            <div className={`bg-${resultColor}-50 p-4 rounded-lg mb-6 flex items-start`}>
              {resultIcon}
              <p className={`text-${resultColor}-700 ml-2`}>
                {resultMessage}
              </p>
            </div>

            <div className="grid grid-cols-2 gap-4">
              <Button
                variant="outline"
                onClick={() => {
                  setQuizStarted(true);
                  setQuizFinished(false);
                  setCurrentQuestionIndex(0);
                  setSelectedAnswer("");
                  setIsAnswered(false);
                  setScore(0);
                  setTimeLeft(30);
                }}
              >
                Újra próbálom
              </Button>

              <Button
                onClick={() => setQuizStarted(false)}
              >
                Vissza a kvíz menühöz
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  // Nehézségi szint jelölése
  const getDifficultyBadge = (difficulty: string) => {
    switch(difficulty) {
      case "beginner":
        return <Badge variant="outline" className="bg-blue-50 text-blue-700">A1</Badge>;
      case "intermediate":
        return <Badge variant="outline" className="bg-green-50 text-green-700">A2</Badge>;
      case "advanced":
        return <Badge variant="outline" className="bg-purple-50 text-purple-700">B1</Badge>;
      default:
        return null;
    }
  };

  return (
    <div className="container mx-auto px-4 py-8 mb-20 md:mb-0 md:pt-24">
      <h1 className="text-2xl font-bold text-center mb-2">Nyelvi kvíz</h1>
      <p className="text-center text-gray-600 mb-6">
        {difficulty === "all" ? "Vegyes nehézségi szintű kvíz" :
         difficulty === "beginner" ? "A1 - Kezdő szintű kvíz" :
         difficulty === "intermediate" ? "A2 - Középhaladó szintű kvíz" :
         "B1 - Haladó szintű kvíz"}
      </p>

      <div className="mb-4 flex justify-between items-center max-w-xl mx-auto">
        <div className="flex items-center gap-2">
          <div className="flex items-center">
            <span className="text-sm font-medium">Kérdés:</span>
            <span className="ml-1 px-2 py-1 bg-gray-100 rounded-md text-sm">
              {currentQuestionIndex + 1} / {activeQuestions.length}
            </span>
          </div>
          {getDifficultyBadge(currentQuestion.difficulty)}
        </div>
        <div className="flex items-center gap-2">
          <div className="flex items-center">
            <BarChart className="h-4 w-4 mr-1 text-gray-600" />
            <span className="text-sm font-medium">Pontszám:</span>
          </div>
          <Badge variant="outline" className="bg-language-primary/10 text-language-primary">
            {score}
          </Badge>
        </div>
      </div>

      <Card className="max-w-xl mx-auto mb-6 overflow-hidden">
        <CardHeader className={`pb-2 ${currentQuestion.difficulty === "beginner" ? "bg-blue-50/30" :
                                      currentQuestion.difficulty === "intermediate" ? "bg-green-50/30" :
                                      currentQuestion.difficulty === "advanced" ? "bg-purple-50/30" : ""}`}>
          <div className="flex items-center justify-between">
            <CardTitle>{currentQuestion.question}</CardTitle>
            <div className="flex items-center bg-white px-2 py-1 rounded-full shadow-sm">
              <Timer className="h-4 w-4 mr-1 text-gray-600" />
              <span className={`text-sm font-medium ${timeLeft < 10 ? "text-red-500" : "text-gray-600"}`}>
                {timeLeft}s
              </span>
            </div>
          </div>
        </CardHeader>
        <CardContent className="pt-4">
          {currentQuestion.type === "multiple-choice" && currentQuestion.options && (
            <RadioGroup
              value={selectedAnswer}
              onValueChange={setSelectedAnswer}
              disabled={isAnswered}
              className="space-y-3"
            >
              {shuffledOptions.map((option, index) => (
                <div key={index} className="flex items-start space-x-2">
                  <RadioGroupItem value={option} id={`option-${index}`} />
                  <Label
                    htmlFor={`option-${index}`}
                    className={`flex-grow p-3 rounded-md border transition-all ${!isAnswered ? "hover:bg-slate-50" : ""} ${
                      isAnswered && option === currentQuestion.answer
                        ? "bg-green-50 border-green-200 text-green-800"
                        : isAnswered && option === selectedAnswer && option !== currentQuestion.answer
                        ? "bg-red-50 border-red-200 text-red-800"
                        : "border-slate-200"
                    }`}
                  >
                    <div className="flex justify-between items-center">
                      <span>{option}</span>
                      {isAnswered && option === currentQuestion.answer && (
                        <CheckCircle2 className="h-5 w-5 text-green-600 ml-2 flex-shrink-0" />
                      )}
                      {isAnswered && option === selectedAnswer && option !== currentQuestion.answer && (
                        <XCircle className="h-5 w-5 text-red-600 ml-2 flex-shrink-0" />
                      )}
                    </div>
                  </Label>
                </div>
              ))}
            </RadioGroup>
          )}

          {currentQuestion.type === "fill-blank" && (
            <div>
              <Input
                placeholder="Írd be a válaszodat ide..."
                value={selectedAnswer}
                onChange={(e) => setSelectedAnswer(e.target.value)}
                className="mb-4 p-3 text-base"
                disabled={isAnswered}
              />

              {isAnswered && (
                <div className={`p-4 rounded-md border ${
                  selectedAnswer.toLowerCase() === currentQuestion.answer.toLowerCase()
                    ? "bg-green-50 border-green-200"
                    : "bg-red-50 border-red-200"
                }`}>
                  <div className="flex items-start">
                    {selectedAnswer.toLowerCase() === currentQuestion.answer.toLowerCase() ? (
                      <CheckCircle2 className="h-5 w-5 text-green-600 mr-3 mt-0.5 flex-shrink-0" />
                    ) : (
                      <XCircle className="h-5 w-5 text-red-600 mr-3 mt-0.5 flex-shrink-0" />
                    )}
                    <div>
                      <p className={selectedAnswer.toLowerCase() === currentQuestion.answer.toLowerCase() ?
                        "text-green-800 font-medium" : "text-red-800 font-medium"}>
                        {selectedAnswer.toLowerCase() === currentQuestion.answer.toLowerCase() ?
                          "Helyes válasz!" : "Helytelen válasz"}
                      </p>
                      {selectedAnswer.toLowerCase() !== currentQuestion.answer.toLowerCase() && (
                        <p className="text-sm text-gray-700 mt-2">
                          A helyes válasz: <span className="font-medium bg-green-100 px-1 py-0.5 rounded">{currentQuestion.answer}</span>
                        </p>
                      )}
                    </div>
                  </div>
                </div>
              )}
            </div>
          )}
        </CardContent>
        <CardFooter className="flex justify-between border-t p-4">
          <Button
            variant="outline"
            size="sm"
            onClick={() => setQuizStarted(false)}
          >
            Vissza a menühöz
          </Button>

          {!isAnswered ? (
            <Button
              onClick={checkAnswer}
              disabled={!selectedAnswer}
              size="lg"
              className={currentQuestion.difficulty === "beginner" ? "bg-blue-600 hover:bg-blue-700" :
                        currentQuestion.difficulty === "intermediate" ? "bg-green-600 hover:bg-green-700" :
                        currentQuestion.difficulty === "advanced" ? "bg-purple-600 hover:bg-purple-700" : ""}
            >
              Válasz ellenőrzése
            </Button>
          ) : (
            <Button
              onClick={nextQuestion}
              size="lg"
              className={currentQuestion.difficulty === "beginner" ? "bg-blue-600 hover:bg-blue-700" :
                        currentQuestion.difficulty === "intermediate" ? "bg-green-600 hover:bg-green-700" :
                        currentQuestion.difficulty === "advanced" ? "bg-purple-600 hover:bg-purple-700" : ""}
            >
              {currentQuestionIndex < activeQuestions.length - 1 ? "Következő kérdés" : "Befejezés"}
            </Button>
          )}
        </CardFooter>
      </Card>
    </div>
  );
};

export default Quiz;
