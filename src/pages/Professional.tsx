import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { Link } from 'react-router-dom';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { GraduationCap, ShoppingBag, Stethoscope, Utensils, Building, ChevronRight } from 'lucide-react';

// Szakmai kategóriák típus definíciója
type ProfessionalCategory = 'retail' | 'healthcare' | 'hospitality' | 'business';

// Szakmai kategória interface
interface Category {
  id: ProfessionalCategory;
  name: string;
  description: string;
  icon: React.ReactNode;
  path: string;
  isAvailable: boolean;
}

const Professional: React.FC = () => {
  const [activeTab, setActiveTab] = useState<ProfessionalCategory>('retail');

  // Szakmai kategóriák definíciója
  const categories: Category[] = [
    {
      id: 'retail',
      name: 'Bolti eladó',
      description: 'Szókincs és kifejezések bolti eladók számára',
      icon: <ShoppingBag className="h-5 w-5" />,
      path: '/vocational/retail',
      isAvailable: true
    },
    {
      id: 'healthcare',
      name: 'Egészségügy',
      description: 'Orvosi és egészségügyi szakszókincs',
      icon: <Stethoscope className="h-5 w-5" />,
      path: '/vocational/healthcare',
      isAvailable: false
    },
    {
      id: 'hospitality',
      name: 'Vendéglátás',
      description: 'Szókincs és kifejezések a vendéglátóipar számára',
      icon: <Utensils className="h-5 w-5" />,
      path: '/vocational/hospitality',
      isAvailable: false
    },
    {
      id: 'business',
      name: 'Üzleti élet',
      description: 'Üzleti kommunikáció és tárgyalástechnika',
      icon: <Building className="h-5 w-5" />,
      path: '/vocational/business',
      isAvailable: false
    }
  ];

  // Aktív kategória kiválasztása
  const activeCategory = categories.find(cat => cat.id === activeTab) || categories[0];

  return (
    <div className="container mx-auto px-4 py-8 mb-20 md:mb-0 md:pt-24">
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
        className="max-w-5xl mx-auto"
      >
        <div className="text-center mb-8">
          <h1 className="text-3xl font-bold mb-2">Szakmai szókincs</h1>
          <p className="text-gray-600">Szakma-specifikus szókincs és kifejezések különböző területekhez</p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
          {/* Bal oldali menü */}
          <div className="md:col-span-1">
            <Card>
              <CardHeader>
                <CardTitle className="text-xl flex items-center">
                  <GraduationCap className="h-5 w-5 mr-2 text-orange-500" />
                  Szakmák
                </CardTitle>
                <CardDescription>
                  Válassz szakmai területet
                </CardDescription>
              </CardHeader>
              <CardContent className="p-0">
                <Tabs
                  orientation="vertical"
                  value={activeTab}
                  onValueChange={(value) => setActiveTab(value as ProfessionalCategory)}
                  className="w-full"
                >
                  <TabsList className="flex flex-col h-auto w-full rounded-none">
                    {categories.map((category) => (
                      <TabsTrigger
                        key={category.id}
                        value={category.id}
                        className="justify-start py-3 px-4 border-b last:border-b-0 data-[state=active]:bg-orange-50 data-[state=active]:text-orange-700"
                      >
                        <div className="flex items-center">
                          <div className="mr-3">{category.icon}</div>
                          <div className="text-left">
                            <div className="font-medium">{category.name}</div>
                            {!category.isAvailable && (
                              <div className="text-xs text-gray-500">Hamarosan</div>
                            )}
                          </div>
                        </div>
                      </TabsTrigger>
                    ))}
                  </TabsList>
                </Tabs>
              </CardContent>
            </Card>
          </div>

          {/* Jobb oldali tartalom */}
          <div className="md:col-span-3">
            <Card>
              <CardHeader>
                <div className="flex items-center justify-between">
                  <CardTitle className="text-xl flex items-center">
                    {activeCategory.icon}
                    <span className="ml-2">{activeCategory.name}</span>
                  </CardTitle>
                </div>
                <CardDescription>
                  {activeCategory.description}
                </CardDescription>
              </CardHeader>
              <CardContent>
                {activeCategory.id === 'retail' ? (
                  <div className="space-y-6">
                    <div>
                      <h3 className="text-lg font-medium mb-3">Bolti eladó szakmai szókincs</h3>
                      <p className="text-gray-600 mb-4">
                        Ebben a szekcióban megtalálod a bolti eladók számára hasznos szavakat, kifejezéseket és párbeszédeket.
                        Tanuld meg a vásárlókkal való kommunikáció alapjait, a termékek bemutatását és az üzleti folyamatok szókincsét.
                      </p>

                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-6">
                        <Link to="/vocational/retail" className="block">
                          <Card className="h-full hover:shadow-md transition-shadow">
                            <CardHeader className="pb-2">
                              <CardTitle className="text-lg">Alapszókincs</CardTitle>
                            </CardHeader>
                            <CardContent>
                              <CardDescription>
                                Alapvető szavak és kifejezések bolti eladók számára
                              </CardDescription>
                            </CardContent>
                            <CardFooter>
                              <Button variant="ghost" size="sm" className="ml-auto">
                                <span className="mr-2">Tovább</span>
                                <ChevronRight className="h-4 w-4" />
                              </Button>
                            </CardFooter>
                          </Card>
                        </Link>

                        <Link to="/vocational/retail/phrases" className="block">
                          <Card className="h-full hover:shadow-md transition-shadow">
                            <CardHeader className="pb-2">
                              <CardTitle className="text-lg">Hasznos kifejezések</CardTitle>
                            </CardHeader>
                            <CardContent>
                              <CardDescription>
                                Gyakran használt kifejezések és párbeszédek
                              </CardDescription>
                            </CardContent>
                            <CardFooter>
                              <Button variant="ghost" size="sm" className="ml-auto">
                                <span className="mr-2">Tovább</span>
                                <ChevronRight className="h-4 w-4" />
                              </Button>
                            </CardFooter>
                          </Card>
                        </Link>

                        <Link to="/vocational/retail/games" className="block">
                          <Card className="h-full hover:shadow-md transition-shadow">
                            <CardHeader className="pb-2">
                              <CardTitle className="text-lg">Gyakorló játékok</CardTitle>
                            </CardHeader>
                            <CardContent>
                              <CardDescription>
                                Interaktív játékok a szakmai szókincs gyakorlásához
                              </CardDescription>
                            </CardContent>
                            <CardFooter>
                              <Button variant="ghost" size="sm" className="ml-auto">
                                <span className="mr-2">Tovább</span>
                                <ChevronRight className="h-4 w-4" />
                              </Button>
                            </CardFooter>
                          </Card>
                        </Link>
                      </div>
                    </div>
                  </div>
                ) : (
                  <div className="py-12 text-center">
                    <div className="bg-gray-100 rounded-full p-4 inline-block mb-4">
                      {activeCategory.icon}
                    </div>
                    <h3 className="text-lg font-medium mb-2">{activeCategory.name} szókincs</h3>
                    <p className="text-gray-500 mb-6">
                      Ez a szakmai terület hamarosan elérhető lesz. Dolgozunk rajta!
                    </p>
                    <Button variant="outline">
                      Értesítést kérek, ha elérhető lesz
                    </Button>
                  </div>
                )}
              </CardContent>
            </Card>
          </div>
        </div>
      </motion.div>
    </div>
  );
};

export default Professional;
