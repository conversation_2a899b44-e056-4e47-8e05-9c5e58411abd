
import React, { useState, useRef } from "react";
import { vocabularyData } from "@/data/languageData";
import { But<PERSON> } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from "@/components/ui/select";
import { ArrowLeftCircle, ArrowRightCircle, RotateCcw, Volume2, Loader2, Book, Filter, Sparkles } from "lucide-react";
import { textToSpeech } from "@/services/openaiService";
import { motion } from "framer-motion";
import VocabularyBackground from "@/components/VocabularyBackground";

const Vocabulary = () => {
  const [currentIndex, setCurrentIndex] = useState(0);
  const [isFlipped, setIsFlipped] = useState(false);
  const [filter, setFilter] = useState<string>("all");
  const [isPlayingHungarian, setIsPlayingHungarian] = useState(false);
  const [isPlaying<PERSON>erman, setIsPlayingGerman] = useState(false);
  const audioRef = useRef<HTMLAudioElement | null>(null);

  // Komponens unmount eseménykezelő
  React.useEffect(() => {
    return () => {
      // Felszabadítjuk az erőforrásokat, amikor a felhasználó elhagyja az oldalt
      if (audioRef.current) {
        audioRef.current.pause();
        if (audioRef.current.src) {
          URL.revokeObjectURL(audioRef.current.src);
        }
      }
    };
  }, []);

  // Filter vocabulary based on selection
  const filteredVocabulary = filter === "all"
    ? vocabularyData
    : vocabularyData.filter(item => item.difficulty === filter || item.category === filter);

  const currentWord = filteredVocabulary[currentIndex];

  // Handle navigation between cards
  const goToNext = () => {
    if (currentIndex < filteredVocabulary.length - 1) {
      setCurrentIndex(currentIndex + 1);
      setIsFlipped(false);
    }
  };

  const goToPrevious = () => {
    if (currentIndex > 0) {
      setCurrentIndex(currentIndex - 1);
      setIsFlipped(false);
    }
  };

  // Reset to a random card
  const goToRandom = () => {
    const randomIndex = Math.floor(Math.random() * filteredVocabulary.length);
    setCurrentIndex(randomIndex);
    setIsFlipped(false);
  };

  // Szövegfelolvasás funkció
  const handleSpeech = async (text: string, language: 'hu-HU' | 'de-DE') => {
    try {
      // Beállítjuk a megfelelő loading állapotot
      if (language === 'hu-HU') {
        setIsPlayingHungarian(true);
      } else {
        setIsPlayingGerman(true);
      }

      // Ha van korábbi audio, azt leállítjuk és felszabadítjuk
      if (audioRef.current) {
        audioRef.current.pause();
        URL.revokeObjectURL(audioRef.current.src);
      }

      // Lekérjük az audio URL-t az OpenAI API-tól
      const audioUrl = await textToSpeech(text, language);

      // Létrehozunk egy új audio elemet
      const audio = new Audio(audioUrl);
      audioRef.current = audio;

      // Lejátszás befejezésekor frissítjük az állapotot és felszabadítjuk az erőforrást
      audio.onended = () => {
        if (language === 'hu-HU') {
          setIsPlayingHungarian(false);
        } else {
          setIsPlayingGerman(false);
        }
        URL.revokeObjectURL(audioUrl);
      };

      // Lejátszás
      await audio.play();
    } catch (error) {
      console.error('Hiba a szövegfelolvasás során:', error);
      // Hiba esetén visszaállítjuk az állapotot
      if (language === 'hu-HU') {
        setIsPlayingHungarian(false);
      } else {
        setIsPlayingGerman(false);
      }
    }
  };

  // Get unique categories for filter
  const categories = Array.from(new Set(vocabularyData.map(item => item.category)));

  // For displaying card progress
  const currentPosition = `${currentIndex + 1} / ${filteredVocabulary.length}`;

  return (
    <div className="container mx-auto px-4 py-8 mb-20 md:mb-0 md:pt-24">
      <h1 className="text-2xl font-bold text-center mb-6">Szókincs</h1>

      <div className="max-w-md mx-auto mb-8">
        <Select
          value={filter}
          onValueChange={(value) => {
            setFilter(value);
            setCurrentIndex(0);
            setIsFlipped(false);
          }}
        >
          <SelectTrigger className="w-full">
            <SelectValue placeholder="Válassz kategóriát" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">Összes szó</SelectItem>
            <SelectItem value="beginner">Kezdő szint</SelectItem>
            <SelectItem value="intermediate">Középhaladó szint</SelectItem>
            <SelectItem value="advanced">Haladó szint</SelectItem>
            {categories.map(category => (
              <SelectItem key={category} value={category}>
                {category.charAt(0).toUpperCase() + category.slice(1)}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>

      {filteredVocabulary.length > 0 ? (
        <>
          <div className="max-w-md mx-auto mb-6 h-64">
            <div
              className={`flashcard ${isFlipped ? 'flipped' : ''}`}
              onClick={() => setIsFlipped(!isFlipped)}
            >
              <div className="flashcard-front bg-white">
                <div className="text-center">
                  <p className="text-gray-500 mb-2 text-sm">Magyar</p>
                  <h2 className="text-3xl font-bold">{currentWord.hungarian}</h2>
                  <Button
                    variant="ghost"
                    size="sm"
                    className="mt-2"
                    onClick={(e) => {
                      e.stopPropagation(); // Megakadályozza, hogy a kártya megforduljon
                      handleSpeech(currentWord.hungarian, 'hu-HU');
                    }}
                    disabled={isPlayingHungarian || isPlayingGerman}
                  >
                    {isPlayingHungarian ? (
                      <>
                        <Loader2 className="h-4 w-4 mr-1 animate-spin" />
                        <span className="text-xs">Lejátszás...</span>
                      </>
                    ) : (
                      <>
                        <Volume2 className="h-4 w-4 mr-1" />
                        <span className="text-xs">Hallgasd meg</span>
                      </>
                    )}
                  </Button>
                  <p className="text-gray-400 text-xs mt-2">Kattints a kártyára a fordításhoz</p>
                </div>
              </div>
              <div className="flashcard-back bg-language-primary text-white">
                <div className="text-center">
                  <p className="text-gray-200 mb-2 text-sm">Német</p>
                  <h2 className="text-3xl font-bold">{currentWord.german}</h2>
                  <Button
                    variant="ghost"
                    size="sm"
                    className="mt-2 text-white hover:bg-white/20"
                    onClick={(e) => {
                      e.stopPropagation(); // Megakadályozza, hogy a kártya megforduljon
                      handleSpeech(currentWord.german, 'de-DE');
                    }}
                    disabled={isPlayingHungarian || isPlayingGerman}
                  >
                    {isPlayingGerman ? (
                      <>
                        <Loader2 className="h-4 w-4 mr-1 animate-spin" />
                        <span className="text-xs">Lejátszás...</span>
                      </>
                    ) : (
                      <>
                        <Volume2 className="h-4 w-4 mr-1" />
                        <span className="text-xs">Hallgasd meg</span>
                      </>
                    )}
                  </Button>
                  <p className="mt-2 text-xs text-gray-200">Kategória: {currentWord.category}</p>
                </div>
              </div>
            </div>
          </div>

          <div className="flex justify-between items-center max-w-md mx-auto">
            <Button
              variant="outline"
              onClick={goToPrevious}
              disabled={currentIndex === 0}
            >
              <ArrowLeftCircle className="h-5 w-5 mr-1" />
              Előző
            </Button>

            <Button variant="ghost" onClick={goToRandom}>
              <RotateCcw className="h-4 w-4 mr-1" />
              Véletlen
            </Button>

            <Button
              variant="outline"
              onClick={goToNext}
              disabled={currentIndex === filteredVocabulary.length - 1}
            >
              Következő
              <ArrowRightCircle className="h-5 w-5 ml-1" />
            </Button>
          </div>

          <div className="text-center mt-4 text-sm text-gray-500">
            {currentPosition}
          </div>
        </>
      ) : (
        <Card className="max-w-md mx-auto p-6 text-center">
          <p>Nincs találat a megadott feltételekkel.</p>
        </Card>
      )}
    </div>
  );
};

export default Vocabulary;
