import React, { useState, useEffect } from 'react';
import { useParams, useNavigate, Link } from 'react-router-dom';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Label } from '@/components/ui/label';
import { toast } from 'sonner';
import { motion } from 'framer-motion';
import { Lock, ArrowLeft, CheckCircle } from 'lucide-react';
import apiService from '@/services/apiService';

const ResetPassword: React.FC = () => {
  const [password, setPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [isSuccess, setIsSuccess] = useState(false);
  const [isTokenValid, setIsTokenValid] = useState(true);
  
  const { token } = useParams<{ token: string }>();
  const navigate = useNavigate();

  // Jelszó visszaállítás kezelése
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    // Validáció
    if (!password || !confirmPassword) {
      toast.error('Kérjük, töltsd ki mindkét mezőt!');
      return;
    }
    
    if (password !== confirmPassword) {
      toast.error('A jelszavak nem egyeznek!');
      return;
    }
    
    if (password.length < 6) {
      toast.error('A jelszónak legalább 6 karakter hosszúnak kell lennie!');
      return;
    }
    
    try {
      setIsLoading(true);
      
      // Jelszó visszaállítás
      const response = await apiService.post(`/auth/reset-password/${token}`, { password });
      
      if (response.data.success) {
        setIsSuccess(true);
        toast.success('A jelszavad sikeresen frissítve lett!');
        
        // 3 másodperc múlva átirányítás a bejelentkezési oldalra
        setTimeout(() => {
          navigate('/login');
        }, 3000);
      } else {
        toast.error(response.data.message || 'Hiba történt a jelszó visszaállítása során.');
      }
    } catch (error: any) {
      console.error('Jelszó visszaállítási hiba:', error);
      
      if (error.response && error.response.status === 400) {
        setIsTokenValid(false);
        toast.error('Érvénytelen vagy lejárt token. Kérjük, kérj új jelszó visszaállítási linket.');
      } else {
        toast.error('Hiba történt a jelszó visszaállítása során. Kérjük, próbáld újra később.');
      }
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="container mx-auto px-4 py-8 mb-20 md:mb-0 md:pt-24">
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
        className="max-w-md mx-auto"
      >
        <Card>
          <CardHeader>
            <CardTitle className="text-2xl font-bold text-center">Jelszó visszaállítása</CardTitle>
            <CardDescription className="text-center">
              Add meg az új jelszavad
            </CardDescription>
          </CardHeader>
          <CardContent>
            {isSuccess ? (
              <div className="text-center space-y-4">
                <div className="flex justify-center">
                  <CheckCircle className="h-16 w-16 text-green-500" />
                </div>
                <h3 className="text-xl font-bold">Jelszó sikeresen frissítve!</h3>
                <p className="text-gray-500">
                  A jelszavad sikeresen frissítve lett. Átirányítunk a bejelentkezési oldalra...
                </p>
              </div>
            ) : !isTokenValid ? (
              <div className="text-center space-y-4">
                <div className="bg-red-50 text-red-700 p-4 rounded-md">
                  <p className="font-medium">Érvénytelen vagy lejárt token</p>
                  <p className="text-sm mt-1">
                    A jelszó visszaállítási link érvénytelen vagy lejárt. Kérjük, kérj új jelszó visszaállítási linket.
                  </p>
                </div>
                <Button
                  variant="outline"
                  className="mt-4"
                  onClick={() => navigate('/forgot-password')}
                >
                  Új jelszó visszaállítási link kérése
                </Button>
              </div>
            ) : (
              <form onSubmit={handleSubmit} className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="password">Új jelszó</Label>
                  <div className="relative">
                    <Lock className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                    <Input
                      id="password"
                      type="password"
                      placeholder="********"
                      value={password}
                      onChange={(e) => setPassword(e.target.value)}
                      className="pl-10"
                      required
                    />
                  </div>
                </div>
                
                <div className="space-y-2">
                  <Label htmlFor="confirmPassword">Új jelszó megerősítése</Label>
                  <div className="relative">
                    <Lock className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                    <Input
                      id="confirmPassword"
                      type="password"
                      placeholder="********"
                      value={confirmPassword}
                      onChange={(e) => setConfirmPassword(e.target.value)}
                      className="pl-10"
                      required
                    />
                  </div>
                </div>
                
                <Button type="submit" className="w-full" disabled={isLoading}>
                  {isLoading ? 'Feldolgozás...' : 'Jelszó frissítése'}
                </Button>
              </form>
            )}
          </CardContent>
          <CardFooter className="flex flex-col space-y-2">
            <div className="text-sm text-center text-gray-500">
              <Link to="/login" className="text-language-primary hover:underline inline-flex items-center">
                <ArrowLeft className="h-4 w-4 mr-1" />
                Vissza a bejelentkezéshez
              </Link>
            </div>
          </CardFooter>
        </Card>
      </motion.div>
    </div>
  );
};

export default ResetPassword;
