import React, { useState } from "react";
import {
  HelpCircle,
  ChevronDown,
  ChevronUp,
  Search,
  BookOpen,
  Gamepad2,
  CreditCard,
  Coins,
  Bot,
  Mail
} from "lucide-react";
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { motion } from "framer-motion";
import { Link } from "react-router-dom";

// FAQ kategóriák és kérdések
const faqCategories = [
  {
    id: "general",
    name: "<PERSON><PERSON><PERSON><PERSON><PERSON>",
    icon: <BookOpen className="h-5 w-5" />,
    color: "language-primary",
    questions: [
      {
        id: "whatIs",
        question: "Mi a Magyar-Német Nyelvtanuló alkalmazás?",
        answer: "A Magyar-Német Nyelvtanuló egy interaktív platform, amely segít a németül tanulóknak szókincsüket, nyelvtanukat és beszédkészségüket fejleszteni különböző interaktív módszerekkel, mint például flashcardok, kvízek és játékok."
      },
      {
        id: "forWhom",
        question: "Kinek készült az alkalmazás?",
        answer: "Az alkalmazás kezdő és középhaladó német nyelvtanulóknak készült (A1-B1 szint), akik interaktív és szórakoztató módon szeretnék fejleszteni a tudásukat."
      },
      {
        id: "devices",
        question: "Milyen eszközökön érhető el?",
        answer: "Az alkalmazás böngészőn keresztül érhető el, így számítógépen, tableten és okostelefonokon egyaránt használható. Elég megnyitni a weboldalt és már kezdődhet is a tanulás."
      }
    ]
  },
  {
    id: "features",
    name: "Funkciók",
    icon: <Gamepad2 className="h-5 w-5" />,
    color: "language-accent",
    questions: [
      {
        id: "features",
        question: "Milyen funkciók érhetők el az alkalmazásban?",
        answer: "Az alkalmazás számos funkciót kínál, például: szókincs flashcardokat, mondatok gyakorlását kiejtéssel, kvízeket, interaktív játékokat, és AI asszisztenst a beszédgyakorláshoz."
      },
      {
        id: "games",
        question: "Milyen játékok találhatók az alkalmazásban?",
        answer: "Az alkalmazásban több játéktípus is megtalálható, mint például szókereső, párosító, mondatkiegészítő és mondatrendezés. Mindegyik játék segít a nyelvtani struktúrák és a szókincs elsajátításában."
      },
      {
        id: "ai",
        question: "Hogyan működik az AI asszisztens?",
        answer: "Az AI asszisztens egy mesterséges intelligencia, amely segít a nyelvtanulásban. Beszélgethetsz vele németül, kérdezhetsz tőle nyelvtani szabályokról, kérhetsz fordítást, vagy gyakorolhatod a társalgást. Az asszisztens a pontrendszeren keresztül érhető el."
      }
    ]
  },
  {
    id: "subscription",
    name: "Előfizetés",
    icon: <CreditCard className="h-5 w-5" />,
    color: "language-quaternary",
    questions: [
      {
        id: "subscriptionTypes",
        question: "Milyen előfizetési lehetőségek vannak?",
        answer: "Az alkalmazás alapfunkciói ingyenesen elérhetők. Az Előfizetés menüpontban található havi előfizetés pedig korlátlan hozzáférést biztosít minden funkcióhoz, beleértve az összes játékot, tematikus szókincset és egyéb exkluzív tartalmakat."
      },
      {
        id: "cancel",
        question: "Hogyan mondhatom le az előfizetésem?",
        answer: "Az előfizetést bármikor lemondhatod a Profil → Előfizetés menüpontban. A lemondás után az aktuális előfizetési periódus végéig még hozzáférsz az összes funkcióhoz."
      }
    ]
  },
  {
    id: "points",
    name: "Pontrendszer",
    icon: <Coins className="h-5 w-5" />,
    color: "language-tertiary",
    questions: [
      {
        id: "pointsSystem",
        question: "Hogyan működik a pontrendszer?",
        answer: "A pontrendszer lehetővé teszi, hogy egyszeri fizetéssel pontokat vásárolj, amelyeket az AI asszisztens használatára költhetsz. Minden beszélgetés az asszisztenssel meghatározott számú pontba kerül, a beszélgetés hosszától függően."
      },
      {
        id: "getPoints",
        question: "Hogyan szerezhetek pontokat?",
        answer: "Pontokat a Pontjaim menüpontban vásárolhatsz különböző csomagokban. Alkalmanként promóciók keretében ingyenes pontokat is kaphatsz, például barátok meghívásával vagy aktivitásért."
      }
    ]
  },
  {
    id: "assistant",
    name: "Asszisztens",
    icon: <Bot className="h-5 w-5" />,
    color: "language-secondary",
    questions: [
      {
        id: "assistantUse",
        question: "Mire használhatom az asszisztenst?",
        answer: "Az asszisztens segítségével gyakorolhatod a német beszédet, kérdezhetsz nyelvtani szabályokról, fordíthatsz szavakat és kifejezéseket, vagy akár teljes beszélgetést folytathatsz németül, hogy fejleszd a kommunikációs készségeidet."
      },
      {
        id: "assistantLimit",
        question: "Van korlátja az asszisztens használatának?",
        answer: "Az asszisztens használata a pontrendszeren keresztül érhető el. Minden beszélgetés pontokba kerül, így addig használhatod, amíg van elegendő pontod. Előfizetéssel kedvezményes pontfelhasználást biztosítunk."
      }
    ]
  }
];

const FAQ = () => {
  const [activeCategory, setActiveCategory] = useState("general");
  const [searchTerm, setSearchTerm] = useState("");
  const [filteredQuestions, setFilteredQuestions] = useState<any[]>([]);
  const [isSearching, setIsSearching] = useState(false);

  // Keresés a kérdésekben és válaszokban
  const handleSearch = (e: React.ChangeEvent<HTMLInputElement>) => {
    const term = e.target.value.toLowerCase();
    setSearchTerm(term);

    if (term.length > 2) {
      setIsSearching(true);
      const results = faqCategories.flatMap(category =>
        category.questions.filter(q =>
          q.question.toLowerCase().includes(term) ||
          q.answer.toLowerCase().includes(term)
        ).map(q => ({
          ...q,
          categoryId: category.id,
          categoryName: category.name,
          categoryColor: category.color
        }))
      );
      setFilteredQuestions(results);
    } else {
      setIsSearching(false);
    }
  };

  return (
    <div className="container mx-auto px-4 py-12 max-w-5xl">
      <motion.div
        className="mb-10 text-center"
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
      >
        <Badge variant="plain" className="bg-white text-language-primary border-language-primary/20 mb-3 py-1.5 px-3 mx-auto shadow-sm">
          <HelpCircle className="h-3.5 w-3.5 mr-1.5" /> Segítség
        </Badge>
        <h1 className="text-4xl font-bold mb-4 bg-gradient-to-r from-language-primary via-language-secondary to-language-accent bg-clip-text text-transparent">
          Gyakran Ismételt Kérdések
        </h1>
        <p className="text-gray-600 max-w-2xl mx-auto">
          Válaszokat találsz a leggyakrabban felmerülő kérdésekre az alkalmazással kapcsolatban.
          Ha nem találod a válaszodat, vedd fel velünk a kapcsolatot!
        </p>

        {/* Kereső mező */}
        <div className="mt-8 max-w-xl mx-auto">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-5 w-5" />
            <Input
              type="text"
              placeholder="Keresés a kérdések között..."
              className="pl-10 pr-4 py-6 w-full rounded-full border-language-border focus:border-language-primary focus:ring-language-primary"
              value={searchTerm}
              onChange={handleSearch}
            />
          </div>
        </div>
      </motion.div>

      {/* Kategória választó */}
      {!isSearching && (
        <div className="flex flex-wrap gap-2 justify-center mb-8">
          {faqCategories.map((category) => {
            // Kategória-specifikus stílusok
            let buttonClassName = "rounded-full flex items-center gap-2 ";

            if (activeCategory === category.id) {
              // Aktív gomb stílusa
              if (category.color === "language-primary") {
                buttonClassName += "bg-language-primary hover:bg-language-primary/90 text-white";
              } else if (category.color === "language-secondary") {
                buttonClassName += "bg-language-secondary hover:bg-language-secondary/90 text-white";
              } else if (category.color === "language-accent") {
                buttonClassName += "bg-language-accent hover:bg-language-accent/90 text-white";
              } else if (category.color === "language-tertiary") {
                buttonClassName += "bg-language-tertiary hover:bg-language-tertiary/90 text-white";
              } else if (category.color === "language-quaternary") {
                buttonClassName += "bg-language-quaternary hover:bg-language-quaternary/90 text-white";
              }
            } else {
              // Inaktív gomb stílusa
              if (category.color === "language-primary") {
                buttonClassName += "border-language-primary/30 text-language-primary hover:bg-language-primary/10";
              } else if (category.color === "language-secondary") {
                buttonClassName += "border-language-secondary/30 text-language-secondary hover:bg-language-secondary/10";
              } else if (category.color === "language-accent") {
                buttonClassName += "border-language-accent/30 text-language-accent hover:bg-language-accent/10";
              } else if (category.color === "language-tertiary") {
                buttonClassName += "border-language-tertiary/30 text-language-tertiary hover:bg-language-tertiary/10";
              } else if (category.color === "language-quaternary") {
                buttonClassName += "border-language-quaternary/30 text-language-quaternary hover:bg-language-quaternary/10";
              }
            }

            return (
              <Button
                key={category.id}
                variant={activeCategory === category.id ? "default" : "outline"}
                className={buttonClassName}
                onClick={() => setActiveCategory(category.id)}
              >
                {category.icon}
                {category.name}
              </Button>
            );
          })}
        </div>
      )}

      {/* Kérdések és válaszok */}
      <div className="bg-white rounded-xl shadow-md p-6 border border-language-border/20">
        {isSearching ? (
          <>
            <h2 className="text-xl font-bold mb-4 flex items-center">
              <Search className="mr-2 h-5 w-5 text-language-primary" />
              Keresési találatok: {filteredQuestions.length}
            </h2>

            {filteredQuestions.length === 0 ? (
              <div className="text-center py-8">
                <p className="text-gray-500 mb-4">Nem találtunk a keresési feltételnek megfelelő kérdést.</p>
                <Link to="/support">
                  <Button className="bg-language-primary hover:bg-language-primary/90 text-white rounded-full">
                    Kapcsolatfelvétel
                  </Button>
                </Link>
              </div>
            ) : (
              <Accordion type="single" collapsible className="w-full space-y-4">
                {filteredQuestions.map((question) => (
                  <AccordionItem
                    key={question.id}
                    value={question.id}
                    className="border border-gray-100 rounded-lg overflow-hidden shadow-sm"
                  >
                    <AccordionTrigger className="px-6 py-4 hover:bg-gray-50 group">
                      <div className="flex items-start text-left">
                        <Badge
                          variant="plain"
                          className={`mr-3 mt-1 whitespace-nowrap ${
                            question.categoryColor === "language-primary"
                              ? "bg-language-primary/10 text-language-primary"
                              : question.categoryColor === "language-secondary"
                                ? "bg-language-secondary/10 text-language-secondary"
                                : question.categoryColor === "language-accent"
                                  ? "bg-language-accent/10 text-language-accent"
                                  : question.categoryColor === "language-tertiary"
                                    ? "bg-language-tertiary/10 text-language-tertiary"
                                    : question.categoryColor === "language-quaternary"
                                      ? "bg-language-quaternary/10 text-language-quaternary"
                                      : "bg-gray-100 text-gray-700"
                          }`}
                        >
                          {question.categoryName}
                        </Badge>
                        <span className="font-medium text-gray-800 group-hover:text-language-primary transition-colors">
                          {question.question}
                        </span>
                      </div>
                    </AccordionTrigger>
                    <AccordionContent className="px-6 py-4 bg-gray-50 text-gray-600 leading-relaxed">
                      {question.answer}
                    </AccordionContent>
                  </AccordionItem>
                ))}
              </Accordion>
            )}
          </>
        ) : (
          <>
            <h2 className="text-xl font-bold mb-4 flex items-center">
              {(() => {
                const category = faqCategories.find(c => c.id === activeCategory);
                let iconClassName = "p-1.5 rounded-md mr-2 ";

                if (category?.color === "language-primary") {
                  iconClassName += "bg-language-primary/10 text-language-primary";
                } else if (category?.color === "language-secondary") {
                  iconClassName += "bg-language-secondary/10 text-language-secondary";
                } else if (category?.color === "language-accent") {
                  iconClassName += "bg-language-accent/10 text-language-accent";
                } else if (category?.color === "language-tertiary") {
                  iconClassName += "bg-language-tertiary/10 text-language-tertiary";
                } else if (category?.color === "language-quaternary") {
                  iconClassName += "bg-language-quaternary/10 text-language-quaternary";
                }

                return (
                  <>
                    <div className={iconClassName}>
                      {category?.icon}
                    </div>
                    {category?.name} kérdések
                  </>
                );
              })()}
            </h2>

            <Accordion type="single" collapsible className="w-full space-y-4">
              {faqCategories
                .find(c => c.id === activeCategory)
                ?.questions.map((question) => (
                  <AccordionItem
                    key={question.id}
                    value={question.id}
                    className="border border-gray-100 rounded-lg overflow-hidden shadow-sm"
                  >
                    <AccordionTrigger className="px-6 py-4 hover:bg-gray-50 group">
                      <span className="font-medium text-gray-800 text-left group-hover:text-language-primary transition-colors">
                        {question.question}
                      </span>
                    </AccordionTrigger>
                    <AccordionContent className="px-6 py-4 bg-gray-50 text-gray-600 leading-relaxed">
                      {question.answer}
                    </AccordionContent>
                  </AccordionItem>
                ))}
            </Accordion>
          </>
        )}
      </div>

      {/* További segítség */}
      <div className="mt-12 text-center">
        <h2 className="text-2xl font-bold mb-4">Nem találod a választ?</h2>
        <p className="text-gray-600 mb-6 max-w-2xl mx-auto">
          Ha a keresett információt nem találod a GYIK-ben, vedd fel velünk a kapcsolatot, és segítünk neked!
        </p>
        <div className="flex flex-wrap gap-4 justify-center">
          <Link to="/support">
            <Button className="bg-language-primary hover:bg-language-primary/90 text-white rounded-full px-6">
              Kapcsolatfelvétel
            </Button>
          </Link>
          <a href="mailto:<EMAIL>">
            <Button variant="outline" className="rounded-full px-6 border-language-border">
              <Mail className="mr-2 h-4 w-4" /> Email küldése
            </Button>
          </a>
        </div>
      </div>
    </div>
  );
};

export default FAQ;
