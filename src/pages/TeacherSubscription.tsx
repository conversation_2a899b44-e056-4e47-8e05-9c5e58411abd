import React, { useState, useEffect } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { useAuth } from '@/contexts/AuthContext';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { toast } from 'sonner';
import { motion } from 'framer-motion';
import { CreditCard, CheckCircle, Users, Coins, Gift, Calendar, RefreshCw } from 'lucide-react';
import { TEACHER_PACKAGE, FREE_TRIAL_PACKAGE } from '@/services/teacherService';

const TeacherSubscription: React.FC = () => {
  const [isLoading, setIsLoading] = useState(false);
  const [isActivating, setIsActivating] = useState(false);
  const { user, isTeacher, createTeacherSubscription, activateFreeTrialPackage } = useAuth();
  const navigate = useNavigate();
  const location = useLocation();

  // <PERSON><PERSON><PERSON> el<PERSON>s létrehozása
  const handleTeacherSubscribe = async () => {
    try {
      setIsLoading(true);

      if (!user) {
        toast.error('Nincs bejelentkezett felhasználó!');
        return;
      }

      // Előfizetés létrehozása
      const checkoutUrl = await createTeacherSubscription();

      if (checkoutUrl) {
        // Stripe Checkout oldal megnyitása
        window.location.href = checkoutUrl;
      } else {
        throw new Error('Nem sikerült létrehozni a fizetési munkamenetet');
      }
    } catch (error) {
      console.error('Előfizetési hiba:', error);
      toast.error('Hiba történt az előfizetés során. Kérjük, próbáld újra!');
      setIsLoading(false);
    }
  };

  // Ingyenes próba csomag aktiválása
  const handleActivateFreeTrialPackage = async () => {
    try {
      setIsActivating(true);

      if (!user) {
        toast.error('Nincs bejelentkezett felhasználó!');
        return;
      }

      // Ingyenes próba csomag aktiválása
      await activateFreeTrialPackage();
      toast.success('Ingyenes próba csomag sikeresen aktiválva!');
    } catch (error) {
      console.error('Aktiválási hiba:', error);
      toast.error('Hiba történt az aktiválás során. Kérjük, próbáld újra!');
    } finally {
      setIsActivating(false);
    }
  };

  // Sikeres előfizetés ellenőrzése
  useEffect(() => {
    const query = new URLSearchParams(location.search);
    const sessionId = query.get('session_id');

    if (sessionId) {
      // Átirányítás a sikeres előfizetés oldalra
      navigate(`/teacher/success?session_id=${sessionId}`, { replace: true });
    }
  }, [location.search, navigate]);

  return (
    <div className="container mx-auto px-4 py-8 mb-20 md:mb-0 md:pt-24">
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
        className="max-w-4xl mx-auto"
      >
        <h1 className="text-2xl font-bold text-center mb-6">Tanári csomag</h1>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {/* Ingyenes próba csomag */}
          <Card>
            <CardHeader>
              <CardTitle className="text-xl">Ingyenes próba csomag</CardTitle>
              <CardDescription>
                Ismerkedj meg az alkalmazással
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="text-2xl font-bold text-center mb-4">{FREE_TRIAL_PACKAGE.price} Ft</div>
              <ul className="space-y-2">
                <li className="flex items-center">
                  <CheckCircle className="h-4 w-4 text-green-500 mr-2" />
                  <span>{FREE_TRIAL_PACKAGE.points} pont az alapfunkciók használatához</span>
                </li>
                <li className="flex items-center">
                  <CheckCircle className="h-4 w-4 text-green-500 mr-2" />
                  <span>Bármikor vásárolhatsz további pontcsomagokat</span>
                </li>
                <li className="flex items-center">
                  <CheckCircle className="h-4 w-4 text-green-500 mr-2" />
                  <span>Korlátlan hozzáférés az alapfunkciókhoz</span>
                </li>
              </ul>
            </CardContent>
            <CardFooter>
              <Button
                onClick={handleActivateFreeTrialPackage}
                className="w-full"
                disabled={isActivating}
              >
                {isActivating ? (
                  <>
                    <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                    Aktiválás...
                  </>
                ) : (
                  'Aktiválás most'
                )}
              </Button>
            </CardFooter>
          </Card>

          {/* Tanári csomag */}
          <Card className="border-primary/50">
            <CardHeader className="bg-primary/5">
              <div className="bg-primary text-white text-xs font-bold px-3 py-1 rounded-full w-fit mb-2">
                AJÁNLOTT
              </div>
              <CardTitle className="text-xl">Tanári csomag</CardTitle>
              <CardDescription>
                Ideális tanároknak, akik diákjaikkal szeretnének dolgozni
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="text-2xl font-bold text-center mb-4">{TEACHER_PACKAGE.price} Ft / hó</div>
              <ul className="space-y-2">
                <li className="flex items-center">
                  <Coins className="h-4 w-4 text-primary mr-2" />
                  <span>{TEACHER_PACKAGE.monthlyPoints} pont havonta</span>
                </li>
                <li className="flex items-center">
                  <Users className="h-4 w-4 text-primary mr-2" />
                  <span>Maximum {TEACHER_PACKAGE.maxStudents} diák meghívása</span>
                </li>
                <li className="flex items-center">
                  <Gift className="h-4 w-4 text-primary mr-2" />
                  <span>{TEACHER_PACKAGE.studentGiftPoints} pont ajándék minden meghívott diáknak</span>
                </li>
                <li className="flex items-center">
                  <Calendar className="h-4 w-4 text-primary mr-2" />
                  <span>Havi automatikus pontfeltöltés</span>
                </li>
                <li className="flex items-center">
                  <CheckCircle className="h-4 w-4 text-green-500 mr-2" />
                  <span>Haladáskövetés diákonként</span>
                </li>
                <li className="flex items-center">
                  <CheckCircle className="h-4 w-4 text-green-500 mr-2" />
                  <span>Csoportos feladatok létrehozása és kiosztása</span>
                </li>
                <li className="flex items-center">
                  <CheckCircle className="h-4 w-4 text-green-500 mr-2" />
                  <span>Pontok átutalása a tanártól a diákoknak</span>
                </li>
              </ul>
            </CardContent>
            <CardFooter>
              <Button
                onClick={handleTeacherSubscribe}
                className="w-full bg-primary hover:bg-primary/90"
                disabled={isLoading}
              >
                {isLoading ? (
                  <>
                    <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                    Feldolgozás...
                  </>
                ) : (
                  <>
                    <CreditCard className="h-4 w-4 mr-2" />
                    Előfizetés most
                  </>
                )}
              </Button>
            </CardFooter>
          </Card>
        </div>
      </motion.div>
    </div>
  );
};

export default TeacherSubscription;
