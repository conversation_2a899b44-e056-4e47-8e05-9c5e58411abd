import React, { useState, useEffect } from "react";
import { Card, CardContent, CardDescription, <PERSON><PERSON><PERSON>er, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { getUserPoints, purchasePoints, verifyPointPurchase, POINT_PACKAGES, PointTransaction } from "@/services/pointService";
import { useNavigate, useLocation } from "react-router-dom";
import { toast } from "sonner";
import { Loader2, CreditCard, History, Coins, ArrowUpRight, RefreshCw, FileText } from "lucide-react";
import { formatDistanceToNow } from "date-fns";
import { hu } from "date-fns/locale";
import { useAuth } from "@/contexts/AuthContext";
import PointInvoicesList from "@/components/points/PointInvoicesList";

const Points = () => {
  const [points, setPoints] = useState<number>(0);
  const [transactions, setTransactions] = useState<PointTransaction[]>([]);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [isPurchasing, setIsPurchasing] = useState<boolean>(false);
  const [isRefreshing, setIsRefreshing] = useState<boolean>(false);
  const [activeTab, setActiveTab] = useState<string>("overview");

  const navigate = useNavigate();
  const location = useLocation();
  const { checkUserPoints } = useAuth();

  // Pontok lekérdezése
  const fetchPoints = async (showLoadingIndicator = true) => {
    try {
      if (showLoadingIndicator) {
        setIsLoading(true);
      } else {
        setIsRefreshing(true);
      }

      const data = await getUserPoints();
      setPoints(data.points);
      setTransactions(data.transactions);

      // Frissítsük a globális állapotot is
      await checkUserPoints();

      if (!showLoadingIndicator) {
        toast.success("Pontok sikeresen frissítve!");
      }
    } catch (error) {
      console.error("Hiba a pontok lekérdezésekor:", error);
      toast.error("Nem sikerült lekérdezni a pontjaidat. Kérlek, próbáld újra később!");
    } finally {
      setIsLoading(false);
      setIsRefreshing(false);
    }
  };

  // Manuális frissítés
  const handleRefresh = () => {
    fetchPoints(false);
  };

  // Pontcsomag vásárlása
  const handlePurchase = async (packageId: string) => {
    try {
      setIsPurchasing(true);
      const { url } = await purchasePoints(packageId);
      window.location.href = url;
    } catch (error) {
      console.error("Hiba a pontcsomag vásárlásakor:", error);
      toast.error("Nem sikerült elindítani a vásárlást. Kérlek, próbáld újra később!");
      setIsPurchasing(false);
    }
  };

  // Sikeres vásárlás ellenőrzése
  const checkPurchaseSuccess = async () => {
    const query = new URLSearchParams(location.search);
    const sessionId = query.get("session_id");

    if (sessionId) {
      try {
        setIsLoading(true);
        await verifyPointPurchase(sessionId);
        toast.success("A pontok sikeresen hozzá lettek adva a fiókodhoz!");
        // Eltávolítjuk a session_id paramétert az URL-ből
        navigate("/points", { replace: true });
        // Frissítjük a pontokat a komponensben és a globális állapotban is
        await fetchPoints();
        await checkUserPoints();
        console.log("Pontok frissítve a sikeres vásárlás után");
      } catch (error) {
        console.error("Hiba a vásárlás ellenőrzésekor:", error);
        toast.error("Nem sikerült ellenőrizni a vásárlást. Kérlek, próbáld újra később!");
      } finally {
        setIsLoading(false);
      }
    }
  };

  // Tranzakció típus formázása
  const formatTransactionType = (type: string) => {
    switch (type) {
      case "purchase":
        return "Vásárlás";
      case "usage":
        return "Felhasználás";
      case "subscription":
        return "Előfizetés";
      default:
        return type;
    }
  };

  // Komponens betöltésekor lekérdezzük a pontokat, de csak egyszer
  useEffect(() => {
    // Csak egyszer hívjuk meg a betöltéskor
    fetchPoints();

    // Frissítési időzítő beállítása (30 másodpercenként)
    const intervalId = setInterval(() => {
      fetchPoints();
    }, 30000); // 30 másodpercenként frissítük

    // Cleanup függvény, amely leállítja az időzítőt
    return () => {
      clearInterval(intervalId);
    };
  }, []);

  // Ha van session_id paraméter, ellenőrizzük a vásárlást
  useEffect(() => {
    checkPurchaseSuccess();
  }, [location.search]);

  return (
    <div className="container mx-auto px-4 py-8 mb-20 md:mb-0 md:pt-24">
      <h1 className="text-2xl font-bold text-center mb-6">Pontjaim</h1>

      <Tabs defaultValue="overview" value={activeTab} onValueChange={setActiveTab} className="max-w-4xl mx-auto">
        <TabsList className="grid grid-cols-3 mb-6">
          <TabsTrigger value="overview" className="flex items-center gap-2">
            <Coins className="h-4 w-4" />
            <span>Áttekintés</span>
          </TabsTrigger>
          <TabsTrigger value="purchase" className="flex items-center gap-2">
            <CreditCard className="h-4 w-4" />
            <span>Pontok vásárlása</span>
          </TabsTrigger>
          <TabsTrigger value="invoices" className="flex items-center gap-2">
            <FileText className="h-4 w-4" />
            <span>Számlák</span>
          </TabsTrigger>
        </TabsList>

        {/* Áttekintés fül */}
        <TabsContent value="overview">
          <Card>
            <CardHeader>
              <CardTitle>Pontjaim</CardTitle>
              <CardDescription>
                Az API hívások és a szövegfelolvasás pontokba kerül. Minden API hívás 1 pontot használ fel.
              </CardDescription>
            </CardHeader>
            <CardContent>
              {isLoading ? (
                <div className="flex justify-center py-8">
                  <Loader2 className="h-8 w-8 animate-spin text-primary" />
                </div>
              ) : (
                <div className="space-y-6">
                  <div className="bg-primary/10 p-6 rounded-lg text-center relative">
                    <Button
                      variant="ghost"
                      size="icon"
                      className="absolute top-2 right-2 h-8 w-8"
                      onClick={handleRefresh}
                      disabled={isRefreshing}
                    >
                      <RefreshCw className={`h-4 w-4 ${isRefreshing ? 'animate-spin' : ''}`} />
                    </Button>
                    <h3 className="text-lg font-medium mb-2">Elérhető pontok</h3>
                    <p className="text-4xl font-bold text-primary">{points}</p>
                  </div>

                  <div>
                    <h3 className="text-lg font-medium mb-4 flex items-center gap-2">
                      <History className="h-5 w-5" />
                      Legutóbbi tranzakciók
                    </h3>

                    {transactions.length === 0 ? (
                      <p className="text-muted-foreground text-center py-4">Még nincsenek tranzakcióid.</p>
                    ) : (
                      <div className="space-y-3">
                        {transactions.map((transaction) => (
                          <div
                            key={transaction.id}
                            className="flex justify-between items-center p-3 border rounded-lg"
                          >
                            <div>
                              <p className="font-medium">{transaction.description || formatTransactionType(transaction.type)}</p>
                              <p className="text-sm text-muted-foreground">
                                {formatDistanceToNow(new Date(transaction.createdAt), { addSuffix: true, locale: hu })}
                              </p>
                            </div>
                            <div className={`font-bold ${transaction.amount > 0 ? 'text-green-600' : 'text-red-600'}`}>
                              {transaction.amount > 0 ? '+' : ''}{transaction.amount} pont
                            </div>
                          </div>
                        ))}
                      </div>
                    )}
                  </div>
                </div>
              )}
            </CardContent>
            <CardFooter className="flex justify-center">
              <Button onClick={() => setActiveTab("purchase")} variant="outline" className="w-full md:w-auto">
                Pontok vásárlása
              </Button>
            </CardFooter>
          </Card>
        </TabsContent>

        {/* Vásárlás fül */}
        <TabsContent value="purchase">
          <Card>
            <CardHeader>
              <CardTitle>Pontok vásárlása</CardTitle>
              <CardDescription>
                Válassz az alábbi csomagok közül a pontjaid feltöltéséhez.
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {POINT_PACKAGES.map((pkg) => (
                  <Card key={pkg.id} className="overflow-hidden">
                    <CardHeader className="bg-primary/5 pb-2">
                      <CardTitle className="text-xl">{pkg.name}</CardTitle>
                      <CardDescription>
                        {pkg.price.toLocaleString()} Ft
                      </CardDescription>
                    </CardHeader>
                    <CardContent className="pt-4">
                      <p className="text-center text-2xl font-bold text-primary mb-2">{pkg.points} pont</p>
                      <p className="text-center text-sm text-muted-foreground">
                        {Math.round((pkg.points / pkg.price) * 1000)} pont / 1000 Ft
                      </p>
                    </CardContent>
                    <CardFooter>
                      <Button
                        onClick={() => handlePurchase(pkg.id)}
                        className="w-full"
                        disabled={isPurchasing}
                      >
                        {isPurchasing ? (
                          <Loader2 className="h-4 w-4 animate-spin mr-2" />
                        ) : (
                          <CreditCard className="h-4 w-4 mr-2" />
                        )}
                        Vásárlás
                      </Button>
                    </CardFooter>
                  </Card>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Számlák fül */}
        <TabsContent value="invoices">
          <Card>
            <CardHeader>
              <CardTitle>Pontfeltöltési számlák</CardTitle>
              <CardDescription>
                Itt tekintheted meg a pontfeltöltésekhez kapcsolódó számláidat és fizetési előzményeidet
              </CardDescription>
            </CardHeader>
            <CardContent>
              <PointInvoicesList />
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default Points;
