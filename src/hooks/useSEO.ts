import { useLocation } from 'react-router-dom';
import { getSEOConfig, SEOConfig } from '@/utils/seoConfig';

interface UseSEOReturn {
  seoConfig: SEOConfig;
  updateSEO: (customConfig: Partial<SEOConfig>) => SEOConfig;
  generateCanonicalUrl: () => string;
  generateBreadcrumbs: () => Array<{ name: string; url: string }>;
}

export const useSEO = (): UseSEOReturn => {
  const location = useLocation();
  
  const seoConfig = getSEOConfig(location.pathname);
  
  const updateSEO = (customConfig: Partial<SEOConfig>): SEOConfig => {
    return {
      ...seoConfig,
      ...customConfig
    };
  };
  
  const generateCanonicalUrl = (): string => {
    const baseUrl = 'https://digitalisnemet.hu';
    const path = location.pathname.replace(/\/$/, '') || '/';
    return `${baseUrl}${path}`;
  };
  
  const generateBreadcrumbs = (): Array<{ name: string; url: string }> => {
    const pathSegments = location.pathname.split('/').filter(Boolean);
    const breadcrumbs = [{ name: 'Főoldal', url: '/' }];
    
    const pathMap: Record<string, string> = {
      'vocabulary': 'Szókincs',
      'phrases': 'Mondatok',
      'quiz': 'Kvíz',
      'games': 'Játékok',
      'assistant': 'Asszisztens',
      'pronunciation': 'Kiejtés',
      'grammar': 'Nyelvtan',
      'professional': 'Szakmai',
      'vocational': 'Szakmai',
      'retail': 'Kiskereskedelem',
      'freizeit': 'Szabadidő',
      'a1': 'A1 Szint',
      'login': 'Bejelentkezés',
      'register': 'Regisztráció',
      'subscription': 'Előfizetés',
      'support': 'Ügyfélszolgálat',
      'faq': 'GYIK',
      'privacy': 'Adatvédelem',
      'terms': 'ÁSZF',
      'teacher': 'Tanári',
      'admin': 'Admin'
    };
    
    let currentPath = '';
    pathSegments.forEach((segment, index) => {
      currentPath += `/${segment}`;
      const name = pathMap[segment] || segment.charAt(0).toUpperCase() + segment.slice(1);
      breadcrumbs.push({
        name,
        url: currentPath
      });
    });
    
    return breadcrumbs;
  };
  
  return {
    seoConfig,
    updateSEO,
    generateCanonicalUrl,
    generateBreadcrumbs
  };
};
