// SEO konfigurációk az alkalmazás különböző oldalaihoz

export interface SEOConfig {
  title: string;
  description: string;
  keywords: string;
  url: string;
  type?: string;
  image?: string;
}

export const seoConfigs: Record<string, SEOConfig> = {
  home: {
    title: "Magyar-Német Nyelvtanuló - Interaktív Német Nyelvtanulás",
    description: "Tanulj németül interaktív módszerekkel! Szókincs, mondatok, kvízek, játékok és kiejtésgyakorlás. Kezdő és középhaladó szintű német nyelvtanulás magyarul.",
    keywords: "német nyelvtanulás, német szókincs, német mondatok, német kvíz, német játékok, német kiejtés, nyelvtanulás, német nyelv, A1 német, B1 német, interaktív nyelvtanulás",
    url: "https://digitalisnemet.hu/"
  },
  
  vocabulary: {
    title: "Német Szókincs - Interaktív Szótanulás",
    description: "Fejleszd a német szókincsedet interaktív flashcard-okkal! Több mint 1000 német szó és kifejezés magyar fordítással, kiejtéssel és példamondatokkal.",
    keywords: "német szókincs, német szavak, német flashcard, német szótár, német kifejezések, német szótanulás, német vocabulary",
    url: "https://digitalisnemet.hu/vocabulary"
  },
  
  phrases: {
    title: "Német Mondatok és Párbeszédek - Gyakorlati Nyelvtanulás",
    description: "Tanulj hasznos német mondatokat és párbeszédeket! Mindennapi szituációk, utazás, munka és társalgás témakörökben.",
    keywords: "német mondatok, német párbeszédek, német beszélgetés, német phrases, német kommunikáció, német társalgás",
    url: "https://digitalisnemet.hu/phrases"
  },
  
  quiz: {
    title: "Német Kvíz - Teszteld a Tudásod",
    description: "Teszteld a német nyelvtudásod interaktív kvízekkel! Szókincs, nyelvtan és kiejtés kvízek különböző nehézségi szinteken.",
    keywords: "német kvíz, német teszt, német nyelvvizsga, német gyakorlás, német quiz, német tudásfelmérés",
    url: "https://digitalisnemet.hu/quiz"
  },
  
  games: {
    title: "Német Nyelvtanuló Játékok - Szórakoztató Tanulás",
    description: "Tanulj németül játékosan! Memóriajátékok, szókereső, puzzle és egyéb interaktív játékok a német nyelvtanuláshoz.",
    keywords: "német játékok, német nyelvtanuló játékok, német memóriajáték, német szókereső, német puzzle, játékos nyelvtanulás",
    url: "https://digitalisnemet.hu/games"
  },
  
  assistant: {
    title: "AI Német Nyelvtanuló Asszisztens - Személyre Szabott Tanulás",
    description: "Beszélgess AI asszisztenssel németül! Gyakorold a beszédet, kérdezz nyelvtani kérdéseket és kapj személyre szabott tanácsokat.",
    keywords: "német AI asszisztens, német chatbot, német beszédgyakorlás, német nyelvtanár, AI nyelvtanulás",
    url: "https://digitalisnemet.hu/assistant"
  },
  
  pronunciation: {
    title: "Német Kiejtésgyakorlás - Beszédfelismerés Technológiával",
    description: "Fejleszd a német kiejtésedet beszédfelismerés technológiával! Gyakorold a helyes kiejtést és kapj azonnali visszajelzést.",
    keywords: "német kiejtés, német beszédgyakorlás, német pronunciation, német beszédfelismerés, német kiejtésjavítás",
    url: "https://digitalisnemet.hu/pronunciation"
  },
  
  grammar: {
    title: "Német Nyelvtan Gyakorlatok - A1 Szint",
    description: "Tanulj német nyelvtant A1 szinten! Interaktív gyakorlatok a német nyelvtan alapjaihoz: igék, főnevek, melléknevek és mondatszerkezet.",
    keywords: "német nyelvtan, német grammar, A1 német, német nyelvtan gyakorlatok, német igék, német főnevek",
    url: "https://digitalisnemet.hu/grammar/a1"
  },
  
  professional: {
    title: "Szakmai Német Nyelvtanulás - Munkahelyi Kommunikáció",
    description: "Fejleszd a szakmai német nyelvtudásod! Munkahelyi kommunikáció, üzleti német és szakmai kifejezések tanulása.",
    keywords: "szakmai német, üzleti német, munkahelyi német, német szaknyelv, professional german, business german",
    url: "https://digitalisnemet.hu/professional"
  },
  
  retail: {
    title: "Kiskereskedelmi Német - Szakmai Nyelvtanulás",
    description: "Tanulj kiskereskedelmi német kifejezéseket! Vásárlás, eladás, ügyfélszolgálat és bolti kommunikáció németül.",
    keywords: "kiskereskedelmi német, bolti német, eladó német, vásárlás németül, retail german, német ügyfélszolgálat",
    url: "https://digitalisnemet.hu/vocational/retail"
  },
  
  freizeit: {
    title: "Szabadidő Témakör Németül - Hobbik és Szórakozás",
    description: "Tanulj szabadidős tevékenységekről németül! Sport, hobbik, szórakozás és kulturális programok német szókincse.",
    keywords: "német szabadidő, német hobbik, német sport, német szórakozás, freizeit deutsch, német kultúra",
    url: "https://digitalisnemet.hu/vocabulary/freizeit"
  },
  
  login: {
    title: "Bejelentkezés - Magyar-Német Nyelvtanuló",
    description: "Jelentkezz be a Magyar-Német Nyelvtanuló alkalmazásba és folytasd a német nyelvtanulást!",
    keywords: "bejelentkezés, login, német nyelvtanulás, felhasználói fiók",
    url: "https://digitalisnemet.hu/login"
  },
  
  register: {
    title: "Regisztráció - Magyar-Német Nyelvtanuló",
    description: "Regisztrálj a Magyar-Német Nyelvtanuló alkalmazásba és kezdd el a német nyelvtanulást még ma!",
    keywords: "regisztráció, register, német nyelvtanulás, új fiók, ingyenes regisztráció",
    url: "https://digitalisnemet.hu/register"
  },
  
  subscription: {
    title: "Előfizetési Csomagok - Magyar-Német Nyelvtanuló",
    description: "Válaszd ki a számodra megfelelő előfizetési csomagot! Prémium funkciók és korlátlan hozzáférés a német nyelvtanuláshoz.",
    keywords: "előfizetés, prémium, német nyelvtanulás csomag, subscription, nyelvtanulás ár",
    url: "https://digitalisnemet.hu/subscription"
  },
  
  faq: {
    title: "Gyakran Ismételt Kérdések - Magyar-Német Nyelvtanuló",
    description: "Válaszok a leggyakrabban feltett kérdésekre a Magyar-Német Nyelvtanuló alkalmazással kapcsolatban.",
    keywords: "GYIK, FAQ, kérdések, válaszok, segítség, német nyelvtanulás kérdések",
    url: "https://digitalisnemet.hu/faq"
  },
  
  support: {
    title: "Ügyfélszolgálat - Magyar-Német Nyelvtanuló",
    description: "Segítségre van szükséged? Vedd fel a kapcsolatot az ügyfélszolgálatunkkal! Gyors és szakértő segítség a német nyelvtanulásban.",
    keywords: "ügyfélszolgálat, segítség, support, kapcsolat, német nyelvtanulás segítség",
    url: "https://digitalisnemet.hu/support"
  },
  
  privacy: {
    title: "Adatvédelmi Szabályzat - Magyar-Német Nyelvtanuló",
    description: "Olvassa el adatvédelmi szabályzatunkat és tudja meg, hogyan kezeljük az Ön személyes adatait.",
    keywords: "adatvédelem, privacy policy, személyes adatok, GDPR, adatkezelés",
    url: "https://digitalisnemet.hu/privacy"
  },
  
  terms: {
    title: "Általános Szerződési Feltételek - Magyar-Német Nyelvtanuló",
    description: "Olvassa el általános szerződési feltételeinket a Magyar-Német Nyelvtanuló szolgáltatás használatához.",
    keywords: "ÁSZF, szerződési feltételek, terms of service, felhasználási feltételek",
    url: "https://digitalisnemet.hu/terms"
  }
};

// Alapértelmezett SEO konfiguráció
export const defaultSEO: SEOConfig = {
  title: "Magyar-Német Nyelvtanuló - Interaktív Német Nyelvtanulás",
  description: "Tanulj németül interaktív módszerekkel! Szókincs, mondatok, kvízek, játékok és kiejtésgyakorlás. Kezdő és középhaladó szintű német nyelvtanulás magyarul.",
  keywords: "német nyelvtanulás, német szókincs, német mondatok, német kvíz, német játékok, német kiejtés, nyelvtanulás, német nyelv, A1 német, B1 német, interaktív nyelvtanulás",
  url: "https://digitalisnemet.hu/"
};

// SEO konfiguráció lekérése útvonal alapján
export const getSEOConfig = (path: string): SEOConfig => {
  // Útvonal normalizálása
  const normalizedPath = path.replace(/^\//, '').replace(/\/$/, '');
  
  // Speciális útvonalak kezelése
  if (normalizedPath === '' || normalizedPath === 'home') {
    return seoConfigs.home;
  }
  
  if (normalizedPath.startsWith('vocabulary/freizeit')) {
    return seoConfigs.freizeit;
  }
  
  if (normalizedPath.startsWith('grammar/a1')) {
    return seoConfigs.grammar;
  }
  
  if (normalizedPath.startsWith('vocational/retail')) {
    return seoConfigs.retail;
  }
  
  // Alapértelmezett konfiguráció keresése
  const configKey = normalizedPath.split('/')[0];
  return seoConfigs[configKey] || defaultSEO;
};
