import React, { createContext, useContext, useState, useEffect } from 'react';
import { authService, subscriptionService, teacherService } from '@/services/apiService';
import { getUserPoints, PointTransaction } from '@/services/pointService';
import { toast } from 'sonner';

// Felhasználói típus definíció
export interface User {
  id: string;
  name: string;
  email: string;
  stripeCustomerId?: string;
  points?: number;
  isAdmin?: boolean;
  isTeacher?: boolean;
  maxStudents?: number;
  monthlyPoints?: number;
}

// Kontextus típus definíció
interface AuthContextType {
  user: User | null;
  isLoading: boolean;
  isAuthenticated: boolean;
  hasSubscription: boolean;
  isTeacher: boolean;
  userPoints: number;
  recentTransactions: PointTransaction[];
  subscriptionStatus: {
    isActive: boolean;
    expiryDate: Date | null;
    cancelAtPeriodEnd: boolean;
  };
  login: (email: string, password: string) => Promise<void>;
  register: (email: string, password: string, name: string, invitationToken?: string) => Promise<void>;
  logout: () => void;
  updateUser: (user: User) => void;
  checkSubscription: (currentUser?: User | null) => Promise<void>;
  checkUserPoints: () => Promise<void>;
  createSubscription: () => Promise<string>;
  cancelSubscription: () => Promise<void>;
  reactivateSubscription: () => Promise<void>;
  createTeacherSubscription: () => Promise<string>;
  activateFreeTrialPackage: () => Promise<void>;
}

// Kontextus létrehozása
const AuthContext = createContext<AuthContextType | undefined>(undefined);

// Kontextus provider komponens
export const AuthProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [user, setUser] = useState<User | null>(null);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [hasSubscription, setHasSubscription] = useState<boolean>(false);
  const [isTeacher, setIsTeacher] = useState<boolean>(false);
  const [userPoints, setUserPoints] = useState<number>(0);
  const [recentTransactions, setRecentTransactions] = useState<PointTransaction[]>([]);
  const [subscriptionStatus, setSubscriptionStatus] = useState<{
    isActive: boolean;
    expiryDate: Date | null;
    cancelAtPeriodEnd: boolean;
  }>({ isActive: false, expiryDate: null, cancelAtPeriodEnd: false });

  // Felhasználó betöltése a localStorage-ból és API-ról
  useEffect(() => {
    const loadUser = async () => {
      try {
        setIsLoading(true);
        // Token ellenőrzése
        const token = localStorage.getItem('token');
        if (!token) {
          setIsLoading(false);
          return;
        }

        // Felhasználó lekérése az API-ról
        const response = await authService.getCurrentUser();
        const userData = response.data;

        // Felhasználó beállítása
        setUser(userData);

        // Tanári státusz beállítása
        setIsTeacher(userData.isTeacher || false);

        // Előfizetés és pontok ellenőrzése a frissen betöltött felhasználóval
        await checkSubscription(userData);
        await checkUserPoints();
        console.log('Felhasználó, előfizetés és pontok sikeresen betöltve');
      } catch (error) {
        console.error('Hiba a felhasználó betöltésekor:', error);
        // Hiba esetén töröljük a tokent
        localStorage.removeItem('token');
        localStorage.removeItem('currentUser');
      } finally {
        setIsLoading(false);
      }
    };

    loadUser();
  }, []);

  // Előfizetés állapotának ellenőrzése
  // Throttling változók a túl gyakori API hívások elkerülésére
  const [lastSubscriptionCheck, setLastSubscriptionCheck] = useState<number>(0);
  const SUBSCRIPTION_THROTTLE_TIME = 5000; // 5 másodperc throttling idő

  const checkSubscription = async (currentUser: User | null = null) => {
    try {
      // Használjuk a paraméterként kapott felhasználót, vagy a state-ben lévőt
      const userToCheck = currentUser || user;

      if (!userToCheck) {
        console.log('Nincs bejelentkezett felhasználó, nem tudjuk ellenőrizni az előfizetést');
        return;
      }

      // Ellenőrizzük, hogy eltelt-e elég idő az utolsó lekérdezés óta
      const now = Date.now();
      if (now - lastSubscriptionCheck < SUBSCRIPTION_THROTTLE_TIME) {
        // console.log('Túl gyakori előfizetés lekérdezés, várunk még:', (SUBSCRIPTION_THROTTLE_TIME - (now - lastSubscriptionCheck)) / 1000, 'másodpercet');
        return;
      }

      // Frissítjük az utolsó lekérdezés idejét
      setLastSubscriptionCheck(now);

      console.log('Előfizetés ellenőrzése a következő felhasználónak:', userToCheck.email);
      const response = await subscriptionService.getSubscriptionStatus();
      const { data } = response;
      console.log('Előfizetés állapota:', data);

      // Ellenőrizzük, hogy van-e adat és aktív-e az előfizetés
      if (data && data.isActive) {
        console.log('Előfizetés aktív:', data);
        setHasSubscription(true);
        setSubscriptionStatus({
          isActive: data.isActive,
          expiryDate: data.currentPeriodEnd ? new Date(data.currentPeriodEnd) : null,
          cancelAtPeriodEnd: data.cancelAtPeriodEnd
        });
      } else {
        console.log('Előfizetés inaktív:', data);
        setHasSubscription(false);
        setSubscriptionStatus({
          isActive: false,
          expiryDate: data?.currentPeriodEnd ? new Date(data.currentPeriodEnd) : null,
          cancelAtPeriodEnd: data?.cancelAtPeriodEnd || false
        });
      }
    } catch (error) {
      console.error('Hiba az előfizetés ellenőrzésekor:', error);
      setHasSubscription(false);
    }
  };

  // Bejelentkezés
  const login = async (email: string, password: string) => {
    try {
      setIsLoading(true);
      const response = await authService.login(email, password);
      const userData = response.user;

      // Felhasználó beállítása
      setUser(userData);

      // Tanári státusz beállítása
      setIsTeacher(userData.isTeacher || false);

      toast.success('Sikeres bejelentkezés!');

      // Előfizetés és pontok ellenőrzése a frissen bejelentkezett felhasználóval
      await checkSubscription(userData);
      await checkUserPoints();
    } catch (error) {
      console.error('Bejelentkezési hiba:', error);
      toast.error('Bejelentkezési hiba: ' + (error instanceof Error ? error.message : 'Ismeretlen hiba'));
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  // Regisztráció
  const register = async (email: string, password: string, name: string, invitationToken?: string) => {
    try {
      setIsLoading(true);
      // Ha van meghívó token, akkor azt is küldje el
      const response = await authService.register(name, email, password, invitationToken);
      const userData = response.user;

      // Felhasználó beállítása
      setUser(userData);

      // Tanári státusz beállítása
      setIsTeacher(userData.isTeacher || false);

      toast.success('Sikeres regisztráció!');

      // Előfizetés és pontok ellenőrzése a frissen regisztrált felhasználóval
      await checkSubscription(userData);
      await checkUserPoints();
    } catch (error) {
      console.error('Regisztrációs hiba:', error);
      toast.error('Regisztrációs hiba: ' + (error instanceof Error ? error.message : 'Ismeretlen hiba'));
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  // Kijelentkezés
  const logout = async () => {
    try {
      await authService.logout();
      setUser(null);
      setHasSubscription(false);
      setIsTeacher(false);
      setUserPoints(0);
      setRecentTransactions([]);
      setSubscriptionStatus({ isActive: false, expiryDate: null, cancelAtPeriodEnd: false });
      toast.success('Sikeres kijelentkezés!');
    } catch (error) {
      console.error('Kijelentkezési hiba:', error);
    }
  };

  // Felhasználó frissítése
  const updateUser = (updatedUser: User) => {
    setUser(updatedUser);
  };

  // Előfizetés létrehozása
  const createSubscription = async (): Promise<string> => {
    try {
      const response = await subscriptionService.createSubscription();
      return response.url;
    } catch (error) {
      console.error('Hiba az előfizetés létrehozásakor:', error);
      toast.error('Hiba az előfizetés létrehozásakor. Kérjük, próbáld újra!');
      throw error;
    }
  };

  // Előfizetés lemondása
  const cancelSubscription = async (): Promise<void> => {
    try {
      await subscriptionService.cancelSubscription();
      // Ellenőrizzük az előfizetés állapotát a jelenlegi felhasználóval
      await checkSubscription(user);
      toast.success('Előfizetésed sikeresen lemondva!');
    } catch (error) {
      console.error('Hiba az előfizetés lemondásakor:', error);
      toast.error('Hiba az előfizetés lemondásakor. Kérjük, próbáld újra!');
      throw error;
    }
  };

  // Előfizetés újraaktiválása
  const reactivateSubscription = async (): Promise<void> => {
    try {
      await subscriptionService.reactivateSubscription();
      // Ellenőrizzük az előfizetés állapotát a jelenlegi felhasználóval
      await checkSubscription(user);
      toast.success('Előfizetésed sikeresen újraaktiválva!');
    } catch (error) {
      console.error('Hiba az előfizetés újraaktiválásakor:', error);
      toast.error('Hiba az előfizetés újraaktiválásakor. Kérjük, próbáld újra!');
      throw error;
    }
  };

  // Felhasználói pontok ellenőrzése
  // Throttling változók a túl gyakori API hívások elkerülésére
  const [lastPointCheck, setLastPointCheck] = useState<number>(0);
  const THROTTLE_TIME = 5000; // 5 másodperc throttling idő

  const checkUserPoints = async () => {
    try {
      // Ellenőrizzük, hogy van-e bejelentkezett felhasználó
      if (!user) {
        console.log('Nincs bejelentkezett felhasználó, nem tudjuk lekérdezni a pontokat');
        return;
      }

      // Ellenőrizzük, hogy eltelt-e elég idő az utolsó lekérdezés óta
      const now = Date.now();
      if (now - lastPointCheck < THROTTLE_TIME) {
        // console.log('Túl gyakori pontlekérdezés, várunk még:', (THROTTLE_TIME - (now - lastPointCheck)) / 1000, 'másodpercet');
        return;
      }

      // Frissítjük az utolsó lekérdezés idejét
      setLastPointCheck(now);

      console.log('Pontok lekérdezése a következő felhasználónak:', user.email);
      const data = await getUserPoints();

      // Pontok beállítása
      setUserPoints(data.points);
      setRecentTransactions(data.transactions);

      // Frissítsük a felhasználó objektumot is a pontokkal
      if (user) {
        setUser({ ...user, points: data.points });
      }

      console.log('Pontok sikeresen lekérdezve:', data.points);
    } catch (error) {
      console.error('Hiba a pontok lekérdezésekor:', error);
    }
  };

  // Tanári előfizetés létrehozása
  const createTeacherSubscription = async (): Promise<string> => {
    try {
      const response = await teacherService.createTeacherSubscription();
      return response.url;
    } catch (error) {
      console.error('Hiba a tanári előfizetés létrehozásakor:', error);
      toast.error('Hiba a tanári előfizetés létrehozásakor. Kérjük, próbáld újra!');
      throw error;
    }
  };

  // Ingyenes próba csomag aktiválása
  const activateFreeTrialPackage = async (): Promise<void> => {
    try {
      await teacherService.activateFreeTrialPackage();
      // Frissítsük a pontokat
      await checkUserPoints();
      toast.success('Ingyenes próba csomag sikeresen aktiválva!');
    } catch (error) {
      console.error('Hiba az ingyenes próba csomag aktiválásakor:', error);
      toast.error('Hiba az ingyenes próba csomag aktiválásakor. Kérjük, próbáld újra!');
      throw error;
    }
  };

  // Kontextus értékek
  const value = {
    user,
    isLoading,
    isAuthenticated: !!user,
    hasSubscription,
    isTeacher,
    userPoints,
    recentTransactions,
    subscriptionStatus,
    login,
    register,
    logout,
    updateUser,
    checkSubscription,
    checkUserPoints,
    createSubscription,
    cancelSubscription,
    reactivateSubscription,
    createTeacherSubscription,
    activateFreeTrialPackage
  };

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;
};

// Hook a kontextus használatához
export const useAuth = (): AuthContextType => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};
