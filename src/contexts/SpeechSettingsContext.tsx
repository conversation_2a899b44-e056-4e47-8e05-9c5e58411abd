import React, { createContext, useContext, useState, useEffect } from 'react';

// Elérhető hangok az OpenAI API-ban
export type VoiceOption = 'alloy' | 'echo' | 'fable' | 'onyx' | 'nova' | 'shimmer';

// Beszédbeállítások típus
export interface SpeechSettings {
  speed: number;
  defaultVoice: VoiceOption;
  hungarianVoice: VoiceOption;
  germanVoice: VoiceOption;
}

// Alapértelmezett beállítások
const defaultSettings: SpeechSettings = {
  speed: 1.0,
  defaultVoice: 'alloy',
  hungarianVoice: 'alloy',
  germanVoice: 'onyx'
};

// Kontextus típus
interface SpeechSettingsContextType {
  settings: SpeechSettings;
  updateSpeed: (speed: number) => void;
  updateDefaultVoice: (voice: VoiceOption) => void;
  updateHungarianVoice: (voice: VoiceOption) => void;
  updateGermanVoice: (voice: VoiceOption) => void;
  resetSettings: () => void;
}

// Kontextus létrehozása
const SpeechSettingsContext = createContext<SpeechSettingsContextType | undefined>(undefined);

// LocalStorage kulcs
const STORAGE_KEY = 'speechSettings';

// Provider komponens
export const SpeechSettingsProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  // Beállítások betöltése a localStorage-ból vagy alapértelmezett értékek használata
  const [settings, setSettings] = useState<SpeechSettings>(() => {
    const savedSettings = localStorage.getItem(STORAGE_KEY);
    return savedSettings ? JSON.parse(savedSettings) : defaultSettings;
  });

  // Beállítások mentése a localStorage-ba, amikor változnak
  useEffect(() => {
    localStorage.setItem(STORAGE_KEY, JSON.stringify(settings));
  }, [settings]);

  // Beszédsebesség frissítése
  const updateSpeed = (speed: number) => {
    setSettings(prev => ({ ...prev, speed }));
  };

  // Alapértelmezett hang frissítése
  const updateDefaultVoice = (defaultVoice: VoiceOption) => {
    setSettings(prev => ({ ...prev, defaultVoice }));
  };

  // Magyar hang frissítése
  const updateHungarianVoice = (hungarianVoice: VoiceOption) => {
    setSettings(prev => ({ ...prev, hungarianVoice }));
  };

  // Német hang frissítése
  const updateGermanVoice = (germanVoice: VoiceOption) => {
    setSettings(prev => ({ ...prev, germanVoice }));
  };

  // Beállítások visszaállítása alapértelmezettre
  const resetSettings = () => {
    setSettings(defaultSettings);
  };

  // Kontextus értékek
  const value = {
    settings,
    updateSpeed,
    updateDefaultVoice,
    updateHungarianVoice,
    updateGermanVoice,
    resetSettings
  };

  return (
    <SpeechSettingsContext.Provider value={value}>
      {children}
    </SpeechSettingsContext.Provider>
  );
};

// Hook a kontextus használatához
export const useSpeechSettings = (): SpeechSettingsContextType => {
  const context = useContext(SpeechSettingsContext);
  if (context === undefined) {
    throw new Error('useSpeechSettings must be used within a SpeechSettingsProvider');
  }
  return context;
};
