export interface VocabularyItem {
  id: number;
  hungarian: string;
  german: string;
  category: string;
  difficulty: "beginner" | "intermediate" | "advanced";
}

export interface PhraseItem {
  id: number;
  hungarian: string;
  german: string;
  category: string;
  difficulty: "beginner" | "intermediate" | "advanced";
}

export interface QuizQuestion {
  id: number;
  question: string;
  type: "multiple-choice" | "fill-blank";
  options?: string[];
  answer: string;
  difficulty: "beginner" | "intermediate" | "advanced";
}

// Basic vocabulary data for a language learning app
export const vocabularyData: VocabularyItem[] = [
  // Beginner level words
  { id: 1, hungarian: "szia", german: "hallo", category: "greetings", difficulty: "beginner" },
  { id: 2, hungarian: "viszontlátásra", german: "auf Wiedersehen", category: "greetings", difficulty: "beginner" },
  { id: 3, hungarian: "köszönöm", german: "danke", category: "greetings", difficulty: "beginner" },
  { id: 4, hungarian: "kérem", german: "bitte", category: "greetings", difficulty: "beginner" },
  { id: 5, hungarian: "igen", german: "ja", category: "basics", difficulty: "beginner" },
  { id: 6, hungarian: "nem", german: "nein", category: "basics", difficulty: "beginner" },
  { id: 7, hungarian: "víz", german: "das Wasser", category: "food", difficulty: "beginner" },
  { id: 8, hungarian: "kenyér", german: "das Brot", category: "food", difficulty: "beginner" },
  { id: 9, hungarian: "egy", german: "eins", category: "numbers", difficulty: "beginner" },
  { id: 10, hungarian: "kettő", german: "zwei", category: "numbers", difficulty: "beginner" },
  { id: 11, hungarian: "három", german: "drei", category: "numbers", difficulty: "beginner" },
  { id: 12, hungarian: "négy", german: "vier", category: "numbers", difficulty: "beginner" },
  { id: 13, hungarian: "öt", german: "fünf", category: "numbers", difficulty: "beginner" },
  { id: 14, hungarian: "hat", german: "sechs", category: "numbers", difficulty: "beginner" },
  { id: 15, hungarian: "hét", german: "sieben", category: "numbers", difficulty: "beginner" },
  { id: 16, hungarian: "nyolc", german: "acht", category: "numbers", difficulty: "beginner" },
  { id: 17, hungarian: "kilenc", german: "neun", category: "numbers", difficulty: "beginner" },
  { id: 18, hungarian: "tíz", german: "zehn", category: "numbers", difficulty: "beginner" },
  { id: 19, hungarian: "húsz", german: "zwanzig", category: "numbers", difficulty: "beginner" },
  { id: 20, hungarian: "harminc", german: "dreißig", category: "numbers", difficulty: "beginner" },
  { id: 21, hungarian: "negyven", german: "vierzig", category: "numbers", difficulty: "beginner" },
  { id: 22, hungarian: "ötven", german: "fünfzig", category: "numbers", difficulty: "beginner" },
  { id: 23, hungarian: "száz", german: "hundert", category: "numbers", difficulty: "beginner" },
  { id: 24, hungarian: "ezer", german: "tausend", category: "numbers", difficulty: "beginner" },

  // Family words - beginner level
  { id: 25, hungarian: "család", german: "die Familie", category: "family", difficulty: "beginner" },
  { id: 26, hungarian: "apa", german: "der Vater", category: "family", difficulty: "beginner" },
  { id: 27, hungarian: "anya", german: "die Mutter", category: "family", difficulty: "beginner" },
  { id: 28, hungarian: "szülők", german: "die Eltern", category: "family", difficulty: "beginner" },
  { id: 29, hungarian: "gyerek", german: "das Kind", category: "family", difficulty: "beginner" },
  { id: 30, hungarian: "fiú", german: "der Sohn", category: "family", difficulty: "beginner" },
  { id: 31, hungarian: "lány", german: "die Tochter", category: "family", difficulty: "beginner" },
  { id: 32, hungarian: "testvér", german: "die Geschwister", category: "family", difficulty: "beginner" },
  { id: 33, hungarian: "báty", german: "der ältere Bruder", category: "family", difficulty: "beginner" },
  { id: 34, hungarian: "öcs", german: "der jüngere Bruder", category: "family", difficulty: "beginner" },
  { id: 35, hungarian: "nővér", german: "die ältere Schwester", category: "family", difficulty: "beginner" },
  { id: 36, hungarian: "húg", german: "die jüngere Schwester", category: "family", difficulty: "beginner" },
  { id: 37, hungarian: "nagyszülők", german: "die Großeltern", category: "family", difficulty: "beginner" },
  { id: 38, hungarian: "nagypapa", german: "der Großvater", category: "family", difficulty: "beginner" },
  { id: 39, hungarian: "nagymama", german: "die Großmutter", category: "family", difficulty: "beginner" },
  { id: 40, hungarian: "unoka (fiú)", german: "der Enkel", category: "family", difficulty: "beginner" },
  { id: 1040, hungarian: "unoka (lány)", german: "die Enkelin", category: "family", difficulty: "beginner" },

  // Colors - beginner level
  { id: 41, hungarian: "szín", german: "die Farbe", category: "colors", difficulty: "beginner" },
  { id: 42, hungarian: "piros", german: "rot", category: "colors", difficulty: "beginner" },
  { id: 43, hungarian: "kék", german: "blau", category: "colors", difficulty: "beginner" },
  { id: 44, hungarian: "zöld", german: "grün", category: "colors", difficulty: "beginner" },
  { id: 45, hungarian: "sárga", german: "gelb", category: "colors", difficulty: "beginner" },
  { id: 46, hungarian: "fehér", german: "weiß", category: "colors", difficulty: "beginner" },
  { id: 47, hungarian: "fekete", german: "schwarz", category: "colors", difficulty: "beginner" },
  { id: 48, hungarian: "barna", german: "braun", category: "colors", difficulty: "beginner" },
  { id: 49, hungarian: "szürke", german: "grau", category: "colors", difficulty: "beginner" },
  { id: 50, hungarian: "narancs", german: "orange", category: "colors", difficulty: "beginner" },
  { id: 51, hungarian: "lila", german: "lila", category: "colors", difficulty: "beginner" },
  { id: 52, hungarian: "rózsaszín", german: "rosa", category: "colors", difficulty: "beginner" },
  { id: 53, hungarian: "világos", german: "hell", category: "colors", difficulty: "beginner" },
  { id: 54, hungarian: "sötét", german: "dunkel", category: "colors", difficulty: "beginner" },

  // Hobbies - beginner and intermediate level
  { id: 151, hungarian: "hobbi", german: "das Hobby", category: "hobbies", difficulty: "beginner" },
  { id: 152, hungarian: "olvasás", german: "das Lesen", category: "hobbies", difficulty: "beginner" },
  { id: 153, hungarian: "sport", german: "der Sport", category: "hobbies", difficulty: "beginner" },
  { id: 154, hungarian: "főzés", german: "das Kochen", category: "hobbies", difficulty: "beginner" },
  { id: 155, hungarian: "kertészkedés", german: "das Gärtnern", category: "hobbies", difficulty: "beginner" },
  { id: 156, hungarian: "zenélés", german: "das Musizieren", category: "hobbies", difficulty: "beginner" },
  { id: 157, hungarian: "festés", german: "das Malen", category: "hobbies", difficulty: "beginner" },
  { id: 158, hungarian: "tánc", german: "das Tanzen", category: "hobbies", difficulty: "beginner" },
  { id: 159, hungarian: "futás", german: "das Laufen", category: "hobbies", difficulty: "beginner" },
  { id: 160, hungarian: "úszás", german: "das Schwimmen", category: "hobbies", difficulty: "beginner" },
  { id: 161, hungarian: "kerékpározás", german: "das Radfahren", category: "hobbies", difficulty: "beginner" },
  { id: 162, hungarian: "túrázás", german: "das Wandern", category: "hobbies", difficulty: "beginner" },
  { id: 163, hungarian: "jóga", german: "das Yoga", category: "hobbies", difficulty: "beginner" },
  { id: 164, hungarian: "filmnézés", german: "das Filme schauen", category: "hobbies", difficulty: "beginner" },
  { id: 165, hungarian: "zenét hallgatni", german: "das Musik hören", category: "hobbies", difficulty: "beginner" },
  { id: 166, hungarian: "fényképezés", german: "das Fotografieren", category: "hobbies", difficulty: "intermediate" },
  { id: 167, hungarian: "sütés", german: "das Backen", category: "hobbies", difficulty: "beginner" },
  { id: 168, hungarian: "kézimánkázás", german: "die Handarbeit", category: "hobbies", difficulty: "intermediate" },
  { id: 169, hungarian: "színházba járás", german: "ins Theater gehen", category: "hobbies", difficulty: "intermediate" },
  { id: 170, hungarian: "koncertre járás", german: "Konzerte besuchen", category: "hobbies", difficulty: "intermediate" },
  { id: 171, hungarian: "gyűjtés", german: "das Sammeln", category: "hobbies", difficulty: "intermediate" },
  { id: 172, hungarian: "sárkányeregetés", german: "das Drachensteigen", category: "hobbies", difficulty: "intermediate" },
  { id: 173, hungarian: "horgászás", german: "das Angeln", category: "hobbies", difficulty: "intermediate" },
  { id: 174, hungarian: "vadászat", german: "die Jagd", category: "hobbies", difficulty: "intermediate" },
  { id: 175, hungarian: "síelés", german: "das Skifahren", category: "hobbies", difficulty: "intermediate" },
  { id: 176, hungarian: "snowboardozás", german: "das Snowboarden", category: "hobbies", difficulty: "intermediate" },
  { id: 177, hungarian: "tenisz", german: "das Tennis", category: "hobbies", difficulty: "intermediate" },
  { id: 178, hungarian: "golf", german: "das Golf", category: "hobbies", difficulty: "intermediate" },
  { id: 179, hungarian: "lovaglás", german: "das Reiten", category: "hobbies", difficulty: "intermediate" },
  { id: 180, hungarian: "szörfözés", german: "das Surfen", category: "hobbies", difficulty: "intermediate" },

  // Clothing - beginner and intermediate level
  { id: 181, hungarian: "ruha", german: "die Kleidung", category: "clothing", difficulty: "beginner" },
  { id: 182, hungarian: "ing", german: "das Hemd", category: "clothing", difficulty: "beginner" },
  { id: 183, hungarian: "póló", german: "das T-Shirt", category: "clothing", difficulty: "beginner" },
  { id: 184, hungarian: "nadrág", german: "die Hose", category: "clothing", difficulty: "beginner" },
  { id: 185, hungarian: "szoknya", german: "der Rock", category: "clothing", difficulty: "beginner" },
  { id: 186, hungarian: "ruha (női)", german: "das Kleid", category: "clothing", difficulty: "beginner" },
  { id: 187, hungarian: "cipő", german: "der Schuh", category: "clothing", difficulty: "beginner" },
  { id: 188, hungarian: "zokni", german: "die Socke", category: "clothing", difficulty: "beginner" },
  { id: 189, hungarian: "alsónemű", german: "die Unterwäsche", category: "clothing", difficulty: "beginner" },
  { id: 190, hungarian: "pulóver", german: "der Pullover", category: "clothing", difficulty: "beginner" },
  { id: 191, hungarian: "kabát", german: "der Mantel", category: "clothing", difficulty: "beginner" },
  { id: 192, hungarian: "dzeki", german: "die Jacke", category: "clothing", difficulty: "beginner" },
  { id: 193, hungarian: "sapka", german: "die Mütze", category: "clothing", difficulty: "beginner" },
  { id: 194, hungarian: "kalap", german: "der Hut", category: "clothing", difficulty: "beginner" },
  { id: 195, hungarian: "sál", german: "der Schal", category: "clothing", difficulty: "beginner" },
  { id: 196, hungarian: "kesztyű", german: "der Handschuh", category: "clothing", difficulty: "beginner" },
  { id: 197, hungarian: "fürdőruha", german: "der Badeanzug", category: "clothing", difficulty: "beginner" },
  { id: 198, hungarian: "fürdőnadrág", german: "die Badehose", category: "clothing", difficulty: "beginner" },
  { id: 199, hungarian: "pizsama", german: "der Schlafanzug", category: "clothing", difficulty: "beginner" },
  { id: 200, hungarian: "hálóing", german: "das Nachthemd", category: "clothing", difficulty: "beginner" },
  { id: 201, hungarian: "farmer", german: "die Jeans", category: "clothing", difficulty: "beginner" },
  { id: 202, hungarian: "rövidnadrág", german: "die kurze Hose", category: "clothing", difficulty: "beginner" },
  { id: 203, hungarian: "nyakkendő", german: "die Krawatte", category: "clothing", difficulty: "intermediate" },
  { id: 204, hungarian: "csokornyakkendő", german: "die Fliege", category: "clothing", difficulty: "intermediate" },
  { id: 205, hungarian: "mellény", german: "die Weste", category: "clothing", difficulty: "intermediate" },
  { id: 206, hungarian: "öltöny", german: "der Anzug", category: "clothing", difficulty: "intermediate" },
  { id: 207, hungarian: "kosztüm", german: "das Kostüm", category: "clothing", difficulty: "intermediate" },
  { id: 208, hungarian: "harisnya", german: "die Strumpfhose", category: "clothing", difficulty: "intermediate" },
  { id: 209, hungarian: "papucs", german: "der Hausschuh", category: "clothing", difficulty: "intermediate" },
  { id: 210, hungarian: "szandál", german: "die Sandale", category: "clothing", difficulty: "intermediate" },

  // Emotions - beginner and intermediate level
  { id: 211, hungarian: "érzelem", german: "das Gefühl", category: "emotions", difficulty: "beginner" },
  { id: 212, hungarian: "boldog", german: "glücklich", category: "emotions", difficulty: "beginner" },
  { id: 213, hungarian: "szomorú", german: "traurig", category: "emotions", difficulty: "beginner" },
  { id: 214, hungarian: "mérges", german: "wütend", category: "emotions", difficulty: "beginner" },
  { id: 215, hungarian: "félős", german: "ängstlich", category: "emotions", difficulty: "beginner" },
  { id: 216, hungarian: "izgatott", german: "aufgeregt", category: "emotions", difficulty: "beginner" },
  { id: 217, hungarian: "fáradt", german: "müde", category: "emotions", difficulty: "beginner" },
  { id: 218, hungarian: "unott", german: "gelangweilt", category: "emotions", difficulty: "beginner" },
  { id: 219, hungarian: "meglepett", german: "überrascht", category: "emotions", difficulty: "beginner" },
  { id: 220, hungarian: "elégedett", german: "zufrieden", category: "emotions", difficulty: "beginner" },
  { id: 221, hungarian: "nyugodt", german: "ruhig", category: "emotions", difficulty: "beginner" },
  { id: 222, hungarian: "ideges", german: "nervös", category: "emotions", difficulty: "beginner" },
  { id: 223, hungarian: "zavart", german: "verwirrt", category: "emotions", difficulty: "beginner" },
  { id: 224, hungarian: "büszke", german: "stolz", category: "emotions", difficulty: "beginner" },
  { id: 225, hungarian: "szégyenlős", german: "schüchtern", category: "emotions", difficulty: "beginner" },
  { id: 226, hungarian: "magabiztos", german: "selbstbewusst", category: "emotions", difficulty: "intermediate" },
  { id: 227, hungarian: "féltékeny", german: "eifersüchtig", category: "emotions", difficulty: "intermediate" },
  { id: 228, hungarian: "csalódott", german: "enttäuscht", category: "emotions", difficulty: "intermediate" },
  { id: 229, hungarian: "kíváncsi", german: "neugierig", category: "emotions", difficulty: "intermediate" },
  { id: 230, hungarian: "aggódó", german: "besorgt", category: "emotions", difficulty: "intermediate" },
  { id: 231, hungarian: "hálás", german: "dankbar", category: "emotions", difficulty: "intermediate" },
  { id: 232, hungarian: "bűntudat", german: "das Schuldgefühl", category: "emotions", difficulty: "intermediate" },
  { id: 233, hungarian: "remény", german: "die Hoffnung", category: "emotions", difficulty: "intermediate" },
  { id: 234, hungarian: "szeretet", german: "die Liebe", category: "emotions", difficulty: "intermediate" },
  { id: 235, hungarian: "gyűlölet", german: "der Hass", category: "emotions", difficulty: "intermediate" },
  { id: 236, hungarian: "félelem", german: "die Angst", category: "emotions", difficulty: "intermediate" },
  { id: 237, hungarian: "öröm", german: "die Freude", category: "emotions", difficulty: "intermediate" },
  { id: 238, hungarian: "bánat", german: "die Trauer", category: "emotions", difficulty: "intermediate" },
  { id: 239, hungarian: "harag", german: "der Zorn", category: "emotions", difficulty: "intermediate" },
  { id: 240, hungarian: "megkönnyebbülés", german: "die Erleichterung", category: "emotions", difficulty: "intermediate" },

  // Food words
  { id: 55, hungarian: "tej", german: "die Milch", category: "food", difficulty: "beginner" },
  { id: 56, hungarian: "hús", german: "das Fleisch", category: "food", difficulty: "beginner" },
  { id: 57, hungarian: "gyümölcs", german: "das Obst", category: "food", difficulty: "beginner" },
  { id: 58, hungarian: "zöldség", german: "das Gemüse", category: "food", difficulty: "beginner" },
  { id: 59, hungarian: "alma", german: "der Apfel", category: "food", difficulty: "beginner" },
  { id: 60, hungarian: "banán", german: "die Banane", category: "food", difficulty: "beginner" },
  { id: 61, hungarian: "narancs", german: "die Orange", category: "food", difficulty: "beginner" },
  { id: 62, hungarian: "eper", german: "die Erdbeere", category: "food", difficulty: "beginner" },
  { id: 63, hungarian: "szőlő", german: "die Traube", category: "food", difficulty: "beginner" },
  { id: 64, hungarian: "sajt", german: "der Käse", category: "food", difficulty: "beginner" },
  { id: 65, hungarian: "tojás", german: "das Ei", category: "food", difficulty: "beginner" },
  { id: 66, hungarian: "reggeli", german: "das Frühstück", category: "food", difficulty: "beginner" },
  { id: 67, hungarian: "ebéd", german: "das Mittagessen", category: "food", difficulty: "beginner" },
  { id: 68, hungarian: "vacsora", german: "das Abendessen", category: "food", difficulty: "beginner" },

  // Places
  { id: 69, hungarian: "ház", german: "das Haus", category: "places", difficulty: "beginner" },
  { id: 70, hungarian: "lakás", german: "die Wohnung", category: "places", difficulty: "beginner" },
  { id: 71, hungarian: "szoba", german: "das Zimmer", category: "places", difficulty: "beginner" },
  { id: 72, hungarian: "konyha", german: "die Küche", category: "places", difficulty: "beginner" },
  { id: 73, hungarian: "fürdőszoba", german: "das Badezimmer", category: "places", difficulty: "beginner" },
  { id: 74, hungarian: "hálószoba", german: "das Schlafzimmer", category: "places", difficulty: "beginner" },
  { id: 75, hungarian: "nappali", german: "das Wohnzimmer", category: "places", difficulty: "beginner" },
  { id: 76, hungarian: "iskola", german: "die Schule", category: "places", difficulty: "beginner" },
  { id: 77, hungarian: "iroda", german: "das Büro", category: "places", difficulty: "beginner" },
  { id: 78, hungarian: "bolt", german: "das Geschäft", category: "places", difficulty: "beginner" },
  { id: 79, hungarian: "piac", german: "der Markt", category: "places", difficulty: "beginner" },
  { id: 80, hungarian: "kórház", german: "das Krankenhaus", category: "places", difficulty: "beginner" },

  // Intermediate level words
  { id: 81, hungarian: "számítógép", german: "der Computer", category: "technology", difficulty: "intermediate" },
  { id: 82, hungarian: "barátság", german: "die Freundschaft", category: "relationships", difficulty: "intermediate" },
  { id: 83, hungarian: "épület", german: "das Gebäude", category: "places", difficulty: "intermediate" },
  { id: 84, hungarian: "találkozás", german: "das Treffen", category: "events", difficulty: "intermediate" },
  { id: 85, hungarian: "tudomány", german: "die Wissenschaft", category: "education", difficulty: "intermediate" },
  { id: 86, hungarian: "repülőgép", german: "das Flugzeug", category: "travel", difficulty: "intermediate" },
  { id: 87, hungarian: "bevásárlóközpont", german: "das Einkaufszentrum", category: "places", difficulty: "intermediate" },
  { id: 88, hungarian: "napsütés", german: "der Sonnenschein", category: "nature", difficulty: "intermediate" },
  { id: 89, hungarian: "hóesés", german: "der Schneefall", category: "nature", difficulty: "intermediate" },
  { id: 90, hungarian: "környezetvédelem", german: "der Umweltschutz", category: "environment", difficulty: "intermediate" },
  { id: 91, hungarian: "újrahasznosítás", german: "das Recycling", category: "environment", difficulty: "intermediate" },
  { id: 92, hungarian: "szennyezés", german: "die Verschmutzung", category: "environment", difficulty: "intermediate" },
  { id: 93, hungarian: "klímaváltozás", german: "der Klimawandel", category: "environment", difficulty: "intermediate" },
  { id: 94, hungarian: "energiatakarékosság", german: "das Energiesparen", category: "environment", difficulty: "intermediate" },
  { id: 95, hungarian: "megújuló energia", german: "die erneuerbare Energie", category: "environment", difficulty: "intermediate" },
  { id: 96, hungarian: "munkanélküliség", german: "die Arbeitslosigkeit", category: "work", difficulty: "intermediate" },
  { id: 97, hungarian: "állásinterjú", german: "das Vorstellungsgespräch", category: "work", difficulty: "intermediate" },
  { id: 98, hungarian: "fizetés", german: "das Gehalt", category: "work", difficulty: "intermediate" },
  { id: 99, hungarian: "munkatapasztalat", german: "die Arbeitserfahrung", category: "work", difficulty: "intermediate" },
  { id: 100, hungarian: "önéletrajz", german: "der Lebenslauf", category: "work", difficulty: "intermediate" },
  { id: 101, hungarian: "motivációs levél", german: "das Motivationsschreiben", category: "work", difficulty: "intermediate" },
  { id: 102, hungarian: "egészségbiztosítás", german: "die Krankenversicherung", category: "health", difficulty: "intermediate" },
  { id: 103, hungarian: "orvosi vizsgálat", german: "die ärztliche Untersuchung", category: "health", difficulty: "intermediate" },
  { id: 104, hungarian: "gyógyszer", german: "das Medikament", category: "health", difficulty: "intermediate" },
  { id: 105, hungarian: "recept", german: "das Rezept", category: "health", difficulty: "intermediate" },
  { id: 106, hungarian: "allergia", german: "die Allergie", category: "health", difficulty: "intermediate" },
  { id: 107, hungarian: "fejfájás", german: "die Kopfschmerzen", category: "health", difficulty: "intermediate" },
  { id: 108, hungarian: "láz", german: "das Fieber", category: "health", difficulty: "intermediate" },
  { id: 109, hungarian: "megfázás", german: "die Erkältung", category: "health", difficulty: "intermediate" },
  { id: 110, hungarian: "influenza", german: "die Grippe", category: "health", difficulty: "intermediate" },
  { id: 111, hungarian: "bankszámla", german: "das Bankkonto", category: "finance", difficulty: "intermediate" },
  { id: 112, hungarian: "hitelkártya", german: "die Kreditkarte", category: "finance", difficulty: "intermediate" },
  { id: 113, hungarian: "megtakarítás", german: "die Ersparnis", category: "finance", difficulty: "intermediate" },
  { id: 114, hungarian: "kölcsön", german: "das Darlehen", category: "finance", difficulty: "intermediate" },
  { id: 115, hungarian: "kamat", german: "die Zinsen", category: "finance", difficulty: "intermediate" },
  { id: 116, hungarian: "biztosítás", german: "die Versicherung", category: "finance", difficulty: "intermediate" },
  { id: 117, hungarian: "adó", german: "die Steuer", category: "finance", difficulty: "intermediate" },
  { id: 118, hungarian: "befektetés", german: "die Investition", category: "finance", difficulty: "intermediate" },
  { id: 119, hungarian: "költségvetés", german: "das Budget", category: "finance", difficulty: "intermediate" },
  { id: 120, hungarian: "számla", german: "die Rechnung", category: "finance", difficulty: "intermediate" },

  // Advanced level words
  { id: 121, hungarian: "fenntarthatóság", german: "die Nachhaltigkeit", category: "environment", difficulty: "advanced" },
  { id: 122, hungarian: "következtetés", german: "die Schlussfolgerung", category: "logic", difficulty: "advanced" },
  { id: 123, hungarian: "megvalósítás", german: "die Umsetzung", category: "business", difficulty: "advanced" },
  { id: 124, hungarian: "felelősség", german: "die Verantwortung", category: "ethics", difficulty: "advanced" },
  { id: 125, hungarian: "alkalmazás", german: "die Anwendung", category: "technology", difficulty: "advanced" },
  { id: 126, hungarian: "társadalmi egyenlőtlenség", german: "die soziale Ungleichheit", category: "society", difficulty: "advanced" },
  { id: 127, hungarian: "gazdasági növekedés", german: "das Wirtschaftswachstum", category: "economics", difficulty: "advanced" },
  { id: 128, hungarian: "globalizáció", german: "die Globalisierung", category: "society", difficulty: "advanced" },
  { id: 129, hungarian: "digitalizáció", german: "die Digitalisierung", category: "technology", difficulty: "advanced" },
  { id: 130, hungarian: "mesterséges intelligencia", german: "die künstliche Intelligenz", category: "technology", difficulty: "advanced" },
  { id: 131, hungarian: "adatvédelem", german: "der Datenschutz", category: "technology", difficulty: "advanced" },
  { id: 132, hungarian: "kiberbiztonság", german: "die Cybersicherheit", category: "technology", difficulty: "advanced" },
  { id: 133, hungarian: "felhőalapú számítástechnika", german: "das Cloud-Computing", category: "technology", difficulty: "advanced" },
  { id: 134, hungarian: "virtuális valóság", german: "die virtuelle Realität", category: "technology", difficulty: "advanced" },
  { id: 135, hungarian: "kiterjesztett valóság", german: "die erweiterte Realität", category: "technology", difficulty: "advanced" },
  { id: 136, hungarian: "fenntartható fejlődés", german: "die nachhaltige Entwicklung", category: "environment", difficulty: "advanced" },
  { id: 137, hungarian: "megújuló energiaforrások", german: "die erneuerbaren Energiequellen", category: "environment", difficulty: "advanced" },
  { id: 138, hungarian: "szén-dioxid-kibocsátás", german: "die Kohlendioxidemission", category: "environment", difficulty: "advanced" },
  { id: 139, hungarian: "éghajlatváltozás", german: "die Klimaveränderung", category: "environment", difficulty: "advanced" },
  { id: 140, hungarian: "biodiverzitás", german: "die Biodiversität", category: "environment", difficulty: "advanced" },
  { id: 141, hungarian: "ökoszisztéma", german: "das Ökosystem", category: "environment", difficulty: "advanced" },
  { id: 142, hungarian: "társadalmi felelősségvállalás", german: "die soziale Verantwortung", category: "society", difficulty: "advanced" },
  { id: 143, hungarian: "emberi jogok", german: "die Menschenrechte", category: "society", difficulty: "advanced" },
  { id: 144, hungarian: "demokrácia", german: "die Demokratie", category: "politics", difficulty: "advanced" },
  { id: 145, hungarian: "jogállamiság", german: "die Rechtsstaatlichkeit", category: "politics", difficulty: "advanced" },
  { id: 146, hungarian: "alkotmány", german: "die Verfassung", category: "politics", difficulty: "advanced" },
  { id: 147, hungarian: "törvényhozás", german: "die Gesetzgebung", category: "politics", difficulty: "advanced" },
  { id: 148, hungarian: "végrehajtó hatalom", german: "die vollziehende Gewalt", category: "politics", difficulty: "advanced" },
  { id: 149, hungarian: "igazságszolgáltatás", german: "die Rechtsprechung", category: "politics", difficulty: "advanced" },
  { id: 150, hungarian: "választások", german: "die Wahlen", category: "politics", difficulty: "advanced" }
];

// Common phrases and expressions
export const phrasesData: PhraseItem[] = [
  // Beginner level phrases
  { id: 1, hungarian: "Hogy vagy?", german: "Wie geht es dir?", category: "greetings", difficulty: "beginner" },
  { id: 2, hungarian: "A nevem...", german: "Mein Name ist...", category: "introductions", difficulty: "beginner" },
  { id: 3, hungarian: "Hol van a mosdó?", german: "Wo ist die Toilette?", category: "questions", difficulty: "beginner" },
  { id: 4, hungarian: "Mennyibe kerül?", german: "Wie viel kostet das?", category: "shopping", difficulty: "beginner" },
  { id: 5, hungarian: "Nem értem.", german: "Ich verstehe nicht.", category: "communication", difficulty: "beginner" },
  { id: 6, hungarian: "Beszélsz angolul?", german: "Sprichst du Englisch?", category: "communication", difficulty: "beginner" },
  { id: 7, hungarian: "Honnan jöttél?", german: "Woher kommst du?", category: "introductions", difficulty: "beginner" },
  { id: 8, hungarian: "Hol laksz?", german: "Wo wohnst du?", category: "introductions", difficulty: "beginner" },
  { id: 9, hungarian: "Mi a foglalkozásod?", german: "Was ist dein Beruf?", category: "introductions", difficulty: "beginner" },
  { id: 10, hungarian: "Hol találkozunk?", german: "Wo treffen wir uns?", category: "meeting", difficulty: "beginner" },
  { id: 11, hungarian: "Mikor találkozunk?", german: "Wann treffen wir uns?", category: "meeting", difficulty: "beginner" },
  { id: 12, hungarian: "Milyen idő van ma?", german: "Wie ist das Wetter heute?", category: "weather", difficulty: "beginner" },
  { id: 13, hungarian: "Hol van a legközelebbi étterem?", german: "Wo ist das nächste Restaurant?", category: "directions", difficulty: "beginner" },
  { id: 14, hungarian: "Kérem a számlát.", german: "Die Rechnung, bitte.", category: "restaurant", difficulty: "beginner" },
  { id: 15, hungarian: "Egy asztalt szeretnék foglalni.", german: "Ich möchte einen Tisch reservieren.", category: "restaurant", difficulty: "beginner" },
  { id: 16, hungarian: "Van szabad szobájuk?", german: "Haben Sie ein freies Zimmer?", category: "accommodation", difficulty: "beginner" },
  { id: 17, hungarian: "Szeretnék bejelentkezni.", german: "Ich möchte einchecken.", category: "accommodation", difficulty: "beginner" },
  { id: 18, hungarian: "Szeretnék kijelentkezni.", german: "Ich möchte auschecken.", category: "accommodation", difficulty: "beginner" },
  { id: 19, hungarian: "Hol van a pályaudvar?", german: "Wo ist der Bahnhof?", category: "directions", difficulty: "beginner" },
  { id: 20, hungarian: "Merre van a buszállomás?", german: "Wo ist die Bushaltestelle?", category: "directions", difficulty: "beginner" },
  { id: 21, hungarian: "Hogyan juthatok el a városba?", german: "Wie komme ich in die Stadt?", category: "directions", difficulty: "beginner" },
  { id: 22, hungarian: "Mennyibe kerül a jegy?", german: "Wie viel kostet die Fahrkarte?", category: "travel", difficulty: "beginner" },
  { id: 23, hungarian: "Mikor indul a következő vonat?", german: "Wann fährt der nächste Zug?", category: "travel", difficulty: "beginner" },
  { id: 24, hungarian: "Hol válthatok pénzt?", german: "Wo kann ich Geld wechseln?", category: "finance", difficulty: "beginner" },
  { id: 25, hungarian: "Hol találok bankautomatat?", german: "Wo finde ich einen Geldautomaten?", category: "finance", difficulty: "beginner" },

  // Intermediate level phrases
  { id: 26, hungarian: "Szívesen segítek neked.", german: "Ich helfe dir gerne.", category: "offers", difficulty: "intermediate" },
  { id: 27, hungarian: "Mit csinálsz a szabadidődben?", german: "Was machst du in deiner Freizeit?", category: "conversation", difficulty: "intermediate" },
  { id: 28, hungarian: "Tudnál lassabban beszélni?", german: "Könntest du bitte langsamer sprechen?", category: "communication", difficulty: "intermediate" },
  { id: 29, hungarian: "Már régóta tanulok németül.", german: "Ich lerne schon lange Deutsch.", category: "language learning", difficulty: "intermediate" },
  { id: 30, hungarian: "Szeretném lefoglalni ezt a szobát.", german: "Ich möchte dieses Zimmer buchen.", category: "travel", difficulty: "intermediate" },
  { id: 31, hungarian: "Nagyon ízlett az étel.", german: "Das Essen hat sehr gut geschmeckt.", category: "restaurant", difficulty: "intermediate" },
  { id: 32, hungarian: "Tudna ajánlani egy jó éttermet?", german: "Könnten Sie ein gutes Restaurant empfehlen?", category: "restaurant", difficulty: "intermediate" },
  { id: 33, hungarian: "Milyen specialitásaik vannak?", german: "Welche Spezialitäten haben Sie?", category: "restaurant", difficulty: "intermediate" },
  { id: 34, hungarian: "Van valamilyen allergiám.", german: "Ich habe eine Allergie.", category: "health", difficulty: "intermediate" },
  { id: 35, hungarian: "Nem érzem jól magam.", german: "Ich fühle mich nicht wohl.", category: "health", difficulty: "intermediate" },
  { id: 36, hungarian: "Orvosra van szükségem.", german: "Ich brauche einen Arzt.", category: "health", difficulty: "intermediate" },
  { id: 37, hungarian: "Van időpontom az orvoshoz.", german: "Ich habe einen Termin beim Arzt.", category: "health", difficulty: "intermediate" },
  { id: 38, hungarian: "Szeretnék időpontot foglalni.", german: "Ich möchte einen Termin vereinbaren.", category: "health", difficulty: "intermediate" },
  { id: 39, hungarian: "Elvesztettem a tárcámat.", german: "Ich habe meine Brieftasche verloren.", category: "problems", difficulty: "intermediate" },
  { id: 40, hungarian: "Elloptak a telefonomat.", german: "Mein Handy wurde gestohlen.", category: "problems", difficulty: "intermediate" },
  { id: 41, hungarian: "Hol van a legközelebbi rendőrőrs?", german: "Wo ist die nächste Polizeistation?", category: "problems", difficulty: "intermediate" },
  { id: 42, hungarian: "Feljelentést szeretnék tenni.", german: "Ich möchte eine Anzeige erstatten.", category: "problems", difficulty: "intermediate" },
  { id: 43, hungarian: "Elterveztem a nyári szabadságomat.", german: "Ich habe meinen Sommerurlaub geplant.", category: "travel", difficulty: "intermediate" },
  { id: 44, hungarian: "Szeretnék autót bérelni.", german: "Ich möchte ein Auto mieten.", category: "travel", difficulty: "intermediate" },
  { id: 45, hungarian: "Milyen dokumentumokra van szükségem?", german: "Welche Dokumente brauche ich?", category: "travel", difficulty: "intermediate" },
  { id: 46, hungarian: "Szeretnék bankszámlát nyitni.", german: "Ich möchte ein Bankkonto eröffnen.", category: "finance", difficulty: "intermediate" },
  { id: 47, hungarian: "Milyen költségekkel jár ez?", german: "Welche Kosten fallen dafür an?", category: "finance", difficulty: "intermediate" },
  { id: 48, hungarian: "Szeretnék pénzt utalni.", german: "Ich möchte Geld überweisen.", category: "finance", difficulty: "intermediate" },
  { id: 49, hungarian: "Milyen kamatot kínalnak?", german: "Welchen Zinssatz bieten Sie an?", category: "finance", difficulty: "intermediate" },
  { id: 50, hungarian: "Szeretnék állást változtatni.", german: "Ich möchte meinen Job wechseln.", category: "work", difficulty: "intermediate" },

  // Advanced level phrases
  { id: 51, hungarian: "A globális felmelegedés komoly probléma.", german: "Der Klimawandel ist ein ernstes Problem.", category: "environment", difficulty: "advanced" },
  { id: 52, hungarian: "Az új törvény jelentős változásokat hoz.", german: "Das neue Gesetz bringt bedeutende Änderungen.", category: "politics", difficulty: "advanced" },
  { id: 53, hungarian: "A technológia fejlődése átalakította az életünket.", german: "Die Entwicklung der Technologie hat unser Leben verändert.", category: "technology", difficulty: "advanced" },
  { id: 54, hungarian: "Azon gondolkodtam, hogy külföldön folytatom a tanulmányaimat.", german: "Ich habe darüber nachgedacht, mein Studium im Ausland fortzusetzen.", category: "education", difficulty: "advanced" },
  { id: 55, hungarian: "Fontos, hogy meghallgassuk egymás véleményét.", german: "Es ist wichtig, die Meinungen voneinander zu hören.", category: "communication", difficulty: "advanced" },
  { id: 56, hungarian: "A fenntartható fejlődés kulcsfontosságú a jövőnk szempontjából.", german: "Nachhaltige Entwicklung ist entscheidend für unsere Zukunft.", category: "environment", difficulty: "advanced" },
  { id: 57, hungarian: "A megújuló energiaforrások használata csökkenti a szén-dioxid-kibocsátást.", german: "Die Nutzung erneuerbarer Energiequellen reduziert den CO2-Ausstoß.", category: "environment", difficulty: "advanced" },
  { id: 58, hungarian: "A biodiverzitás megőrzése nélkülözhetetlen az ökoszisztémák egészséges működéséhez.", german: "Die Erhaltung der Biodiversität ist für das gesunde Funktionieren der Ökosysteme unentbehrlich.", category: "environment", difficulty: "advanced" },
  { id: 59, hungarian: "A digitalizáció átalakítja a munkaerőpiacot és új készségeket igényel.", german: "Die Digitalisierung verändert den Arbeitsmarkt und erfordert neue Fähigkeiten.", category: "work", difficulty: "advanced" },
  { id: 60, hungarian: "A mesterséges intelligencia fejlődése etikai kérdéseket vet fel.", german: "Die Entwicklung der künstlichen Intelligenz wirft ethische Fragen auf.", category: "technology", difficulty: "advanced" },
  { id: 61, hungarian: "Az adatvédelem egyre fontosabbá válik a digitális korban.", german: "Der Datenschutz wird im digitalen Zeitalter immer wichtiger.", category: "technology", difficulty: "advanced" },
  { id: 62, hungarian: "A kiberbiztonság nélkülözhetetlen a modern társadalomban.", german: "Cybersicherheit ist in der modernen Gesellschaft unentbehrlich.", category: "technology", difficulty: "advanced" },
  { id: 63, hungarian: "A társadalmi egyenlőtlenségek csökkentése globális kihivás.", german: "Die Verringerung sozialer Ungleichheiten ist eine globale Herausforderung.", category: "society", difficulty: "advanced" },
  { id: 64, hungarian: "Az emberi jogok védelme minden demokratikus társadalom alapja.", german: "Der Schutz der Menschenrechte ist die Grundlage jeder demokratischen Gesellschaft.", category: "society", difficulty: "advanced" },
  { id: 65, hungarian: "A jogállamiság elve biztosítja, hogy senki sem áll a törvények felett.", german: "Das Prinzip der Rechtsstaatlichkeit stellt sicher, dass niemand über dem Gesetz steht.", category: "politics", difficulty: "advanced" },
  { id: 66, hungarian: "A hatalmi ágak szétválasztása a demokratikus rendszerek alapelve.", german: "Die Gewaltenteilung ist ein Grundprinzip demokratischer Systeme.", category: "politics", difficulty: "advanced" },
  { id: 67, hungarian: "A gazdasági növekedésnek fenntarthatónak kell lennie.", german: "Das Wirtschaftswachstum muss nachhaltig sein.", category: "economics", difficulty: "advanced" },
  { id: 68, hungarian: "A globalizáció előnyei és hátrányai vitatottak.", german: "Die Vor- und Nachteile der Globalisierung sind umstritten.", category: "economics", difficulty: "advanced" },
  { id: 69, hungarian: "A nemzetközi együttműködés elengedhetetlen a globális kihivások kezeléséhez.", german: "Die internationale Zusammenarbeit ist für die Bewältigung globaler Herausforderungen unentbehrlich.", category: "politics", difficulty: "advanced" },
  { id: 70, hungarian: "Az oktatási rendszereknek alkalmazkodniuk kell a változó munkaerőpiaci igényekhez.", german: "Bildungssysteme müssen sich an die veränderten Anforderungen des Arbeitsmarktes anpassen.", category: "education", difficulty: "advanced" },
  { id: 71, hungarian: "Az egész életen át tartó tanulás egyre fontosabbá válik.", german: "Lebenslanges Lernen wird immer wichtiger.", category: "education", difficulty: "advanced" },
  { id: 72, hungarian: "A kulturális sokféleség gazdagabbá teszi a társadalmat.", german: "Kulturelle Vielfalt bereichert die Gesellschaft.", category: "society", difficulty: "advanced" },
  { id: 73, hungarian: "A városépítészet jelentősen befolyásolja az életminőséget.", german: "Die Stadtarchitektur beeinflusst die Lebensqualität erheblich.", category: "society", difficulty: "advanced" },
  { id: 74, hungarian: "A közösségi média megváltoztatta a kommunikációs szokásainkat.", german: "Soziale Medien haben unsere Kommunikationsgewohnheiten verändert.", category: "technology", difficulty: "advanced" },
  { id: 75, hungarian: "A kritikus gondolkodás képessége nélkülözhetetlen a modern világban.", german: "Die Fähigkeit zum kritischen Denken ist in der modernen Welt unentbehrlich.", category: "education", difficulty: "advanced" },

  // Hobby related phrases
  { id: 76, hungarian: "Mi a hobbid?", german: "Was ist dein Hobby?", category: "hobbies", difficulty: "beginner" },
  { id: 77, hungarian: "Szeretek olvasni.", german: "Ich lese gerne.", category: "hobbies", difficulty: "beginner" },
  { id: 78, hungarian: "Szeretsz sportolni?", german: "Treibst du gerne Sport?", category: "hobbies", difficulty: "beginner" },
  { id: 79, hungarian: "Hetente kétszer járok úszni.", german: "Ich gehe zweimal pro Woche schwimmen.", category: "hobbies", difficulty: "intermediate" },
  { id: 80, hungarian: "A szabadidőmben szeretek zenét hallgatni.", german: "In meiner Freizeit höre ich gerne Musik.", category: "hobbies", difficulty: "intermediate" },
  { id: 81, hungarian: "Milyen könyveket szeretsz olvasni?", german: "Welche Bücher liest du gerne?", category: "hobbies", difficulty: "intermediate" },
  { id: 82, hungarian: "Szeretek a természetben túrázni.", german: "Ich wandere gerne in der Natur.", category: "hobbies", difficulty: "intermediate" },
  { id: 83, hungarian: "Milyen filmeket nézel szívesen?", german: "Welche Filme schaust du gerne?", category: "hobbies", difficulty: "intermediate" },
  { id: 84, hungarian: "Szeretek főzni és új recepteket kipróbálni.", german: "Ich koche gerne und probiere neue Rezepte aus.", category: "hobbies", difficulty: "intermediate" },
  { id: 85, hungarian: "A kertészkedés nagyon megnyugtató hobbi.", german: "Gärtnern ist ein sehr beruhigendes Hobby.", category: "hobbies", difficulty: "intermediate" },
  { id: 86, hungarian: "A festés lehetőséget ad az önkifejezésre.", german: "Malen bietet die Möglichkeit zum Selbstausdruck.", category: "hobbies", difficulty: "advanced" },
  { id: 87, hungarian: "A rendszeres testmozgás fontos az egészség megőrzéséhez.", german: "Regelmäßige Bewegung ist wichtig für die Erhaltung der Gesundheit.", category: "hobbies", difficulty: "advanced" },
  { id: 88, hungarian: "A zenélés fejleszti a kreativitást és a koncentrációt.", german: "Musizieren fördert die Kreativität und Konzentration.", category: "hobbies", difficulty: "advanced" },
  { id: 89, hungarian: "A fényképezés segít megörökíteni a pillanatokat.", german: "Fotografieren hilft, Momente festzuhalten.", category: "hobbies", difficulty: "advanced" },
  { id: 90, hungarian: "A tánc nemcsak hobbi, hanem életforma is lehet.", german: "Tanzen kann nicht nur ein Hobby sein, sondern auch eine Lebensform.", category: "hobbies", difficulty: "advanced" },

  // Clothing related phrases
  { id: 91, hungarian: "Milyen ruhát viselsz szivesen?", german: "Welche Kleidung trägst du gerne?", category: "clothing", difficulty: "beginner" },
  { id: 92, hungarian: "Szeretem a kényelmes ruhákat.", german: "Ich mag bequeme Kleidung.", category: "clothing", difficulty: "beginner" },
  { id: 93, hungarian: "Ez a ruha nagyon jól áll neked.", german: "Dieses Kleid steht dir sehr gut.", category: "clothing", difficulty: "beginner" },
  { id: 94, hungarian: "Milyen méretet hordasz?", german: "Welche Größe trägst du?", category: "clothing", difficulty: "beginner" },
  { id: 95, hungarian: "Felprobálhatom ezt a cipőt?", german: "Kann ich diese Schuhe anprobieren?", category: "clothing", difficulty: "beginner" },
  { id: 96, hungarian: "Hol van a próbafülke?", german: "Wo ist die Umkleidekabine?", category: "clothing", difficulty: "intermediate" },
  { id: 97, hungarian: "Ez a nadrág túl szűk.", german: "Diese Hose ist zu eng.", category: "clothing", difficulty: "intermediate" },
  { id: 98, hungarian: "Ez a pulóver túl bő.", german: "Dieser Pullover ist zu weit.", category: "clothing", difficulty: "intermediate" },
  { id: 99, hungarian: "Milyen színű ruhát keresel?", german: "Welche Farbe suchst du für die Kleidung?", category: "clothing", difficulty: "intermediate" },
  { id: 100, hungarian: "Szeretnék egy új kabátot venni.", german: "Ich möchte einen neuen Mantel kaufen.", category: "clothing", difficulty: "intermediate" },
  { id: 101, hungarian: "A divat állandóan változik, de a stílus örök.", german: "Mode ändert sich ständig, aber Stil ist ewig.", category: "clothing", difficulty: "advanced" },
  { id: 102, hungarian: "A fenntartható divat egyre fontosabbá válik napjainkban.", german: "Nachhaltige Mode wird heutzutage immer wichtiger.", category: "clothing", difficulty: "advanced" },
  { id: 103, hungarian: "Az öltözködés az önkifejezés egyik formája.", german: "Kleidung ist eine Form des Selbstausdrucks.", category: "clothing", difficulty: "advanced" },
  { id: 104, hungarian: "A minőségi ruhák hosszabb ideig tartanak és jobb befektetést jelentenek.", german: "Qualitätskleidung hält länger und ist eine bessere Investition.", category: "clothing", difficulty: "advanced" },
  { id: 105, hungarian: "Az időjárásnak megfelelő öltözködés fontos az egészség megőrzéséhez.", german: "Wettergerechte Kleidung ist wichtig für die Erhaltung der Gesundheit.", category: "clothing", difficulty: "advanced" },

  // Emotions related phrases
  { id: 106, hungarian: "Hogy érzed magad?", german: "Wie fühlst du dich?", category: "emotions", difficulty: "beginner" },
  { id: 107, hungarian: "Jól érzem magam.", german: "Ich fühle mich gut.", category: "emotions", difficulty: "beginner" },
  { id: 108, hungarian: "Rosszul érzem magam.", german: "Ich fühle mich schlecht.", category: "emotions", difficulty: "beginner" },
  { id: 109, hungarian: "Boldog vagyok.", german: "Ich bin glücklich.", category: "emotions", difficulty: "beginner" },
  { id: 110, hungarian: "Szomorú vagyok.", german: "Ich bin traurig.", category: "emotions", difficulty: "beginner" },
  { id: 111, hungarian: "Fáradt vagyok.", german: "Ich bin müde.", category: "emotions", difficulty: "beginner" },
  { id: 112, hungarian: "Ideges vagyok.", german: "Ich bin nervös.", category: "emotions", difficulty: "beginner" },
  { id: 113, hungarian: "Miért vagy szomorú?", german: "Warum bist du traurig?", category: "emotions", difficulty: "intermediate" },
  { id: 114, hungarian: "Nagyon izgatott vagyok a holnapi nap miatt.", german: "Ich bin sehr aufgeregt wegen morgen.", category: "emotions", difficulty: "intermediate" },
  { id: 115, hungarian: "Ne aggódj, minden rendben lesz.", german: "Mach dir keine Sorgen, alles wird gut sein.", category: "emotions", difficulty: "intermediate" },
  { id: 116, hungarian: "Nagyon csalódott vagyok az eredmények miatt.", german: "Ich bin sehr enttäuscht über die Ergebnisse.", category: "emotions", difficulty: "intermediate" },
  { id: 117, hungarian: "Büszke vagyok rád.", german: "Ich bin stolz auf dich.", category: "emotions", difficulty: "intermediate" },
  { id: 118, hungarian: "Az érzelmek kifejezése fontos a lelki egészség szempontjából.", german: "Der Ausdruck von Gefühlen ist wichtig für die psychische Gesundheit.", category: "emotions", difficulty: "advanced" },
  { id: 119, hungarian: "Az érzelmi intelligencia segít megérteni mások érzéseit.", german: "Emotionale Intelligenz hilft, die Gefühle anderer zu verstehen.", category: "emotions", difficulty: "advanced" },
  { id: 120, hungarian: "A pozitív gondolkodás javíthatja a hangulatát.", german: "Positives Denken kann die Stimmung verbessern.", category: "emotions", difficulty: "advanced" },
  { id: 121, hungarian: "A stressz kezelése alapvető készség a modern életben.", german: "Stressbewältigung ist eine grundlegende Fähigkeit im modernen Leben.", category: "emotions", difficulty: "advanced" },
  { id: 122, hungarian: "Az érzelmek szabályozása segít a konfliktusok kezelésében.", german: "Die Regulierung von Emotionen hilft bei der Bewältigung von Konflikten.", category: "emotions", difficulty: "advanced" }
];

// Quiz questions for testing knowledge
export const quizData: QuizQuestion[] = [
  // Beginner level questions
  {
    id: 1,
    question: "Mi a német megfelelője a 'köszönöm' szónak?",
    type: "multiple-choice",
    options: ["danke", "bitte", "hallo", "tschüss"],
    answer: "danke",
    difficulty: "beginner"
  },
  {
    id: 2,
    question: "Egészítsd ki: 'Ich heiße... (A nevem...)'",
    type: "fill-blank",
    answer: "Ich heiße",
    difficulty: "beginner"
  },
  {
    id: 3,
    question: "Mit jelent németül: 'Wasser'?",
    type: "multiple-choice",
    options: ["víz", "kenyér", "tej", "bor"],
    answer: "víz",
    difficulty: "beginner"
  },
  {
    id: 4,
    question: "Melyik a helyes kérdés: 'Hogy hívnak?'",
    type: "multiple-choice",
    options: ["Wie heißt du?", "Wer bist du?", "Was ist dein?", "Wie alt bist du?"],
    answer: "Wie heißt du?",
    difficulty: "beginner"
  },
  {
    id: 5,
    question: "Egészítsd ki: 'Ich ... aus Ungarn.' (Magyarországról jövök.)",
    type: "fill-blank",
    answer: "komme",
    difficulty: "beginner"
  },
  {
    id: 6,
    question: "Mi a német megfelelője: 'Beszélsz angolul?'",
    type: "multiple-choice",
    options: ["Sprichst du Englisch?", "Kannst du Englisch?", "Hast du Englisch?", "Willst du Englisch?"],
    answer: "Sprichst du Englisch?",
    difficulty: "beginner"
  },
  {
    id: 7,
    question: "Melyik a helyes fordítás: 'Hol van a mosdó?'",
    type: "multiple-choice",
    options: ["Wo ist die Toilette?", "Wo ist das Bad?", "Wo ist der Waschraum?", "Wo ist das Wasser?"],
    answer: "Wo ist die Toilette?",
    difficulty: "beginner"
  },
  {
    id: 8,
    question: "Egészítsd ki: 'Ich möchte ein ... Wasser.' (Szeretnék egy pohár vizet.)",
    type: "fill-blank",
    answer: "Glas",
    difficulty: "beginner"
  },
  {
    id: 9,
    question: "Mi a német megfelelője: 'Kérem a számlát.'",
    type: "multiple-choice",
    options: ["Die Rechnung, bitte.", "Das Menu, bitte.", "Die Karte, bitte.", "Den Preis, bitte."],
    answer: "Die Rechnung, bitte.",
    difficulty: "beginner"
  },
  {
    id: 10,
    question: "Melyik a helyes fordítás: 'Merre van a pályaudvar?'",
    type: "multiple-choice",
    options: ["Wo ist der Bahnhof?", "Wo ist der Flughafen?", "Wo ist die Haltestelle?", "Wo ist die Station?"],
    answer: "Wo ist der Bahnhof?",
    difficulty: "beginner"
  },
  {
    id: 11,
    question: "Egészítsd ki: 'Ich möchte ein Zimmer ... .' (Szeretnék egy szobát foglalni.)",
    type: "fill-blank",
    answer: "reservieren",
    difficulty: "beginner"
  },
  {
    id: 12,
    question: "Mi a német megfelelője: 'Mennyibe kerül?'",
    type: "multiple-choice",
    options: ["Wie viel kostet das?", "Was ist der Preis?", "Wie teuer ist das?", "Was kostet?"],
    answer: "Wie viel kostet das?",
    difficulty: "beginner"
  },

  // Intermediate level questions
  {
    id: 13,
    question: "Mi a német megfelelője: 'Szeretnék kérdezni valamit.'",
    type: "multiple-choice",
    options: ["Ich möchte etwas trinken.", "Ich möchte etwas fragen.", "Ich möchte etwas essen.", "Ich möchte etwas kaufen."],
    answer: "Ich möchte etwas fragen.",
    difficulty: "intermediate"
  },
  {
    id: 14,
    question: "Egészítsd ki: '... du mir bitte helfen?' (Tudnál segíteni nekem?)",
    type: "fill-blank",
    answer: "Könntest",
    difficulty: "intermediate"
  },
  {
    id: 15,
    question: "Melyik a helyes fordítás: 'Már régóta tanulok németül.'",
    type: "multiple-choice",
    options: [
      "Ich lerne schon lange Deutsch.",
      "Ich habe Deutsch gelernt.",
      "Ich werde Deutsch lernen.",
      "Ich kann Deutsch lernen."
    ],
    answer: "Ich lerne schon lange Deutsch.",
    difficulty: "intermediate"
  },
  {
    id: 16,
    question: "Egészítsd ki: 'Ich habe meine Brieftasche ... .' (Elvesztettem a tárcámat.)",
    type: "fill-blank",
    answer: "verloren",
    difficulty: "intermediate"
  },
  {
    id: 17,
    question: "Mi a német megfelelője: 'Szeretnék autót bérelni.'",
    type: "multiple-choice",
    options: [
      "Ich möchte ein Auto mieten.",
      "Ich möchte ein Auto kaufen.",
      "Ich möchte ein Auto fahren.",
      "Ich möchte ein Auto haben."
    ],
    answer: "Ich möchte ein Auto mieten.",
    difficulty: "intermediate"
  },
  {
    id: 18,
    question: "Melyik a helyes fordítás: 'Nem érzem jól magam.'",
    type: "multiple-choice",
    options: [
      "Ich fühle mich nicht wohl.",
      "Ich bin nicht gut.",
      "Ich habe kein gutes Gefühl.",
      "Ich mag mich nicht."
    ],
    answer: "Ich fühle mich nicht wohl.",
    difficulty: "intermediate"
  },
  {
    id: 19,
    question: "Egészítsd ki: 'Ich möchte einen Termin ... .' (Szeretnék időpontot foglalni.)",
    type: "fill-blank",
    answer: "vereinbaren",
    difficulty: "intermediate"
  },
  {
    id: 20,
    question: "Mi a német megfelelője: 'Szeretnék bankszámlát nyitni.'",
    type: "multiple-choice",
    options: [
      "Ich möchte ein Bankkonto eröffnen.",
      "Ich möchte ein Bankkonto haben.",
      "Ich möchte zur Bank gehen.",
      "Ich möchte Geld einzahlen."
    ],
    answer: "Ich möchte ein Bankkonto eröffnen.",
    difficulty: "intermediate"
  },
  {
    id: 21,
    question: "Melyik a helyes fordítás: 'Milyen költségekkel jár ez?'",
    type: "multiple-choice",
    options: [
      "Welche Kosten fallen dafür an?",
      "Was kostet das?",
      "Wie teuer ist das?",
      "Wieviel muss ich bezahlen?"
    ],
    answer: "Welche Kosten fallen dafür an?",
    difficulty: "intermediate"
  },
  {
    id: 22,
    question: "Egészítsd ki: 'Ich möchte Geld ... .' (Szeretnék pénzt utalni.)",
    type: "fill-blank",
    answer: "überweisen",
    difficulty: "intermediate"
  },

  // Advanced level questions
  {
    id: 23,
    question: "Fordítsd le: 'A fenntartható fejlődés fontos a jövő generációi számára.'",
    type: "multiple-choice",
    options: [
      "Eine nachhaltige Entwicklung ist wichtig für zukünftige Generationen.",
      "Die Entwicklung ist nachhaltig für die Zukunft wichtig.",
      "Nachhaltige Generationen sind wichtig für die Zukunft.",
      "Die Zukunft ist wichtig für nachhaltige Entwicklung."
    ],
    answer: "Eine nachhaltige Entwicklung ist wichtig für zukünftige Generationen.",
    difficulty: "advanced"
  },
  {
    id: 24,
    question: "Egészítsd ki a mondatot: 'Es ist wichtig, dass wir ... Verantwortung für unsere Umwelt übernehmen.' (Fontos, hogy felelősséget vállaljunk a környezetünkért.)",
    type: "fill-blank",
    answer: "die",
    difficulty: "advanced"
  },
  {
    id: 25,
    question: "Melyik a helyes fordítás: 'A digitalizáció átalakítja a munkaerőpiacot és új készségeket igényel.'",
    type: "multiple-choice",
    options: [
      "Die Digitalisierung verändert den Arbeitsmarkt und erfordert neue Fähigkeiten.",
      "Die Digitalisierung macht den Arbeitsmarkt besser und braucht neue Fähigkeiten.",
      "Der Arbeitsmarkt wird durch die Digitalisierung verändert und benötigt neue Fähigkeiten.",
      "Neue Fähigkeiten sind wichtig für die Digitalisierung des Arbeitsmarktes."
    ],
    answer: "Die Digitalisierung verändert den Arbeitsmarkt und erfordert neue Fähigkeiten.",
    difficulty: "advanced"
  },
  {
    id: 26,
    question: "Egészítsd ki: 'Der Datenschutz wird im digitalen Zeitalter immer ... .' (Az adatvédelem egyre fontosabbá válik a digitális korban.)",
    type: "fill-blank",
    answer: "wichtiger",
    difficulty: "advanced"
  },
  {
    id: 27,
    question: "Mi a német megfelelője: 'A társadalmi egyenlőtlenségek csökkentése globális kihivás.'",
    type: "multiple-choice",
    options: [
      "Die Verringerung sozialer Ungleichheiten ist eine globale Herausforderung.",
      "Soziale Ungleichheiten zu reduzieren ist global wichtig.",
      "Die globale Herausforderung ist die Reduzierung sozialer Ungleichheiten.",
      "Soziale Ungleichheiten sind global zu verringern."
    ],
    answer: "Die Verringerung sozialer Ungleichheiten ist eine globale Herausforderung.",
    difficulty: "advanced"
  },
  {
    id: 28,
    question: "Melyik a helyes fordítás: 'Az emberi jogok védelme minden demokratikus társadalom alapja.'",
    type: "multiple-choice",
    options: [
      "Der Schutz der Menschenrechte ist die Grundlage jeder demokratischen Gesellschaft.",
      "Jede demokratische Gesellschaft schützt die Menschenrechte als Grundlage.",
      "Die Grundlage der Demokratie ist der Schutz der Menschenrechte in der Gesellschaft.",
      "Menschenrechte zu schützen ist demokratisch grundlegend."
    ],
    answer: "Der Schutz der Menschenrechte ist die Grundlage jeder demokratischen Gesellschaft.",
    difficulty: "advanced"
  },
  {
    id: 29,
    question: "Egészítsd ki: 'Lebenslanges Lernen wird immer ... .' (Az egész életen át tartó tanulás egyre fontosabbá válik.)",
    type: "fill-blank",
    answer: "wichtiger",
    difficulty: "advanced"
  },
  {
    id: 30,
    question: "Mi a német megfelelője: 'A kulturális sokféleség gazdagabbá teszi a társadalmat.'",
    type: "multiple-choice",
    options: [
      "Kulturelle Vielfalt bereichert die Gesellschaft.",
      "Die Gesellschaft wird durch kulturelle Vielfalt besser.",
      "Kultur macht die Gesellschaft vielfältiger.",
      "Vielfältige Kulturen sind gut für die Gesellschaft."
    ],
    answer: "Kulturelle Vielfalt bereichert die Gesellschaft.",
    difficulty: "advanced"
  },

  // Hobby related questions
  {
    id: 31,
    question: "Mi a német megfelelője: 'Mi a hobbid?'",
    type: "multiple-choice",
    options: [
      "Was ist dein Hobby?",
      "Was machst du gerne?",
      "Was tust du?",
      "Was liebst du?"
    ],
    answer: "Was ist dein Hobby?",
    difficulty: "beginner"
  },
  {
    id: 32,
    question: "Egészítsd ki: 'Ich ... gerne.' (Szeretek olvasni.)",
    type: "fill-blank",
    answer: "lese",
    difficulty: "beginner"
  },
  {
    id: 33,
    question: "Mi a német megfelelője: 'Szeretsz sportolni?'",
    type: "multiple-choice",
    options: [
      "Treibst du gerne Sport?",
      "Magst du Sport?",
      "Spielst du Sport?",
      "Machst du Sport?"
    ],
    answer: "Treibst du gerne Sport?",
    difficulty: "beginner"
  },
  {
    id: 34,
    question: "Melyik a helyes fordítás: 'Ich gehe zweimal pro Woche schwimmen.'",
    type: "multiple-choice",
    options: [
      "Hetente kétszer járok úszni.",
      "Kétszer voltam úszni a héten.",
      "Két héten egyszer járok úszni.",
      "Két alkalommal úsztam a héten."
    ],
    answer: "Hetente kétszer járok úszni.",
    difficulty: "intermediate"
  },
  {
    id: 35,
    question: "Egészítsd ki: 'In meiner Freizeit ... ich gerne Musik.' (A szabadidőmben szeretek zenét hallgatni.)",
    type: "fill-blank",
    answer: "höre",
    difficulty: "intermediate"
  },
  {
    id: 36,
    question: "Mi a német megfelelője: 'A rendszeres testmozgás fontos az egészség megőrzéséhez.'",
    type: "multiple-choice",
    options: [
      "Regelmäßige Bewegung ist wichtig für die Erhaltung der Gesundheit.",
      "Sport ist wichtig für die Gesundheit.",
      "Bewegung hilft der Gesundheit regelmäßig.",
      "Die Gesundheit braucht regelmäßige Bewegung."
    ],
    answer: "Regelmäßige Bewegung ist wichtig für die Erhaltung der Gesundheit.",
    difficulty: "advanced"
  },

  // Clothing related questions
  {
    id: 37,
    question: "Mi a német megfelelője: 'Milyen ruhát viselsz szivesen?'",
    type: "multiple-choice",
    options: [
      "Welche Kleidung trägst du gerne?",
      "Was trägst du?",
      "Welche Kleidung magst du?",
      "Was ziehst du an?"
    ],
    answer: "Welche Kleidung trägst du gerne?",
    difficulty: "beginner"
  },
  {
    id: 38,
    question: "Egészítsd ki: 'Ich mag ... Kleidung.' (Szeretem a kényelmes ruhákat.)",
    type: "fill-blank",
    answer: "bequeme",
    difficulty: "beginner"
  },
  {
    id: 39,
    question: "Mi a német megfelelője: 'Milyen méretet hordasz?'",
    type: "multiple-choice",
    options: [
      "Welche Größe trägst du?",
      "Wie groß bist du?",
      "Welche Nummer hast du?",
      "Was ist deine Größe?"
    ],
    answer: "Welche Größe trägst du?",
    difficulty: "beginner"
  },
  {
    id: 40,
    question: "Melyik a helyes fordítás: 'Kann ich diese Schuhe anprobieren?'",
    type: "multiple-choice",
    options: [
      "Felprobálhatom ezt a cipőt?",
      "Megvehetem ezt a cipőt?",
      "Hol találom ezt a cipőt?",
      "Mennyibe kerül ez a cipő?"
    ],
    answer: "Felprobálhatom ezt a cipőt?",
    difficulty: "beginner"
  },
  {
    id: 41,
    question: "Egészítsd ki: 'Diese Hose ist zu ... .' (Ez a nadrág túl szűk.)",
    type: "fill-blank",
    answer: "eng",
    difficulty: "intermediate"
  },
  {
    id: 42,
    question: "Mi a német megfelelője: 'A divat állandóan változik, de a stílus örök.'",
    type: "multiple-choice",
    options: [
      "Mode ändert sich ständig, aber Stil ist ewig.",
      "Die Mode ist veränderlich, der Stil bleibt.",
      "Mode wechselt, Stil ist immer da.",
      "Die Mode geht, der Stil kommt."
    ],
    answer: "Mode ändert sich ständig, aber Stil ist ewig.",
    difficulty: "advanced"
  },

  // Emotions related questions
  {
    id: 43,
    question: "Mi a német megfelelője: 'Hogy érzed magad?'",
    type: "multiple-choice",
    options: [
      "Wie fühlst du dich?",
      "Wie geht es dir?",
      "Wie bist du?",
      "Was fühlst du?"
    ],
    answer: "Wie fühlst du dich?",
    difficulty: "beginner"
  },
  {
    id: 44,
    question: "Egészítsd ki: 'Ich ... mich gut.' (Jól érzem magam.)",
    type: "fill-blank",
    answer: "fühle",
    difficulty: "beginner"
  },
  {
    id: 45,
    question: "Mi a német megfelelője: 'Boldog vagyok.'",
    type: "multiple-choice",
    options: [
      "Ich bin glücklich.",
      "Ich bin froh.",
      "Ich bin zufrieden.",
      "Ich bin fröhlich."
    ],
    answer: "Ich bin glücklich.",
    difficulty: "beginner"
  },
  {
    id: 46,
    question: "Melyik a helyes fordítás: 'Warum bist du traurig?'",
    type: "multiple-choice",
    options: [
      "Miért vagy szomorú?",
      "Miért vagy boldog?",
      "Miért vagy fáradt?",
      "Miért vagy ideges?"
    ],
    answer: "Miért vagy szomorú?",
    difficulty: "intermediate"
  },
  {
    id: 47,
    question: "Egészítsd ki: 'Mach dir keine ... , alles wird gut sein.' (Ne aggódj, minden rendben lesz.)",
    type: "fill-blank",
    answer: "Sorgen",
    difficulty: "intermediate"
  },
  {
    id: 48,
    question: "Mi a német megfelelője: 'Az érzelmi intelligencia segít megérteni mások érzéseit.'",
    type: "multiple-choice",
    options: [
      "Emotionale Intelligenz hilft, die Gefühle anderer zu verstehen.",
      "Intelligenz hilft, Emotionen zu verstehen.",
      "Emotionen helfen, andere zu verstehen.",
      "Intelligente Menschen verstehen Gefühle besser."
    ],
    answer: "Emotionale Intelligenz hilft, die Gefühle anderer zu verstehen.",
    difficulty: "advanced"
  }
];
