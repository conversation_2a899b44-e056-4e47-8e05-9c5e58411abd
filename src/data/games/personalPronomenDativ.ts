export interface PersonalPronomenDativItem {
  sentence: string;
  pronoun: string;
  position: number; // A helyettesítendő s<PERSON> pozíciója a mondatban
  hint?: string; // Segítség z<PERSON>ben, pl. "du", "ich" stb.
}

export const personalPronomenDativItems: PersonalPronomenDativItem[] = [
  { 
    sentence: "Wie gefällt _______ München?", 
    pronoun: "dir", 
    position: 2,
    hint: "du"
  },
  { 
    sentence: "Mein B<PERSON>er hilft _______ immer.", 
    pronoun: "mir", 
    position: 3,
    hint: "ich"
  },
  { 
    sentence: "Ich schicke _______ die Adresse.", 
    pronoun: "Ihnen", 
    position: 2,
    hint: "Sie"
  },
  { 
    sentence: "Mittwoch passt _______ gut.", 
    pronoun: "uns", 
    position: 2,
    hint: "wir"
  },
  { 
    sentence: "Der Rock steht _______ nicht.", 
    pronoun: "ihr", 
    position: 3,
    hint: "sie"
  },
  { 
    sentence: "<PERSON> geht mit _______ spazieren.", 
    pronoun: "ihm", 
    position: 3,
    hint: "der Hund"
  },
  { 
    sentence: "Die Bücher gefallen _______ nicht.", 
    pronoun: "ihnen", 
    position: 3,
    hint: "den Mädchen"
  },
  { 
    sentence: "Wartet, ich helfe _______!", 
    pronoun: "euch", 
    position: 4,
    hint: "ihr"
  },
  { 
    sentence: "Der Pullover passt _______.", 
    pronoun: "ihm", 
    position: 3,
    hint: "das Kind"
  },
  { 
    sentence: "Frau Meier, wie geht es _______?", 
    pronoun: "Ihnen", 
    position: 5,
    hint: "Sie"
  }
];