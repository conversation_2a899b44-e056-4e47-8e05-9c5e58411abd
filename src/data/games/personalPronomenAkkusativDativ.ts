export interface PersonalPronomenAkkusativDativItem {
  sentence: string;
  pronoun: string;
  position: number; // A helyettesítendő s<PERSON> pozíciója a mondatban
  hint?: string; // Segítség z<PERSON>, pl. "der Chef"
  case: 'akkusativ' | 'dativ'; // Melyik esetben van a névmás
}

export const personalPronomenAkkusativDativItems: PersonalPronomenAkkusativDativItem[] = [
  { 
    sentence: "Hast du _______ gefragt?",
    pronoun: "ihn", 
    position: 2,
    hint: "der Chef",
    case: 'akkusativ'
  },
  { 
    sentence: "<PERSON> holt _______ von der <PERSON>.",
    pronoun: "es", 
    position: 2,
    hint: "das Paket",
    case: 'akkusativ'
  },
  { 
    sentence: "Das Kleid steht _______ sehr gut.",
    pronoun: "dir", 
    position: 2,
    hint: "du",
    case: 'dativ'
  },
  { 
    sentence: "<PERSON> schickt _______ einen Brief.",
    pronoun: "ihr", 
    position: 2,
    hint: "<PERSON>",
    case: 'dativ'
  },
  { 
    sentence: "Besucht ihr _______?",
    pronoun: "uns", 
    position: 2,
    hint: "wir",
    case: 'akkusativ'
  },
  { 
    sentence: "Gibst du _______ das Buch?",
    pronoun: "mir", 
    position: 2,
    hint: "ich",
    case: 'dativ'
  },
  { 
    sentence: "Schmeckt _______ das Essen?",
    pronoun: "euch", 
    position: 1,
    hint: "ihr",
    case: 'dativ'
  },
  { 
    sentence: "Ich rufe _______ an.",
    pronoun: "sie", 
    position: 2,
    hint: "die Freunde",
    case: 'akkusativ'
  },
  { 
    sentence: "Herr Müller, kann ich mit _______ sprechen?",
    pronoun: "Ihnen", 
    position: 5,
    hint: "Sie",
    case: 'dativ'
  },
  { 
    sentence: "Karin bringt _______ mit.",
    pronoun: "es", 
    position: 2,
    hint: "das Brot",
    case: 'akkusativ'
  }
];