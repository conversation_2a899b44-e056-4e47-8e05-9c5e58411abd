export interface BestimmterArtikelAkkusativItem {
  sentence: string;
  article: 'den' | 'die' | 'das';
  position: number; // A helyettesítendő s<PERSON>ó p<PERSON>íciója (index) a mondatban
}

export const bestimmterArtikelAkkusativItems: BestimmterArtikelAkkusativItem[] = [
  { 
    sentence: "Wir brauchen _____ Brot.", 
    article: "das", 
    position: 2 
  },
  { 
    sentence: "Ich mache _____ Hausaufgabe.", 
    article: "die", 
    position: 2 
  },
  { 
    sentence: "Er kocht _____ Nudeln.", 
    article: "die", 
    position: 2 
  },
  { 
    sentence: "<PERSON> kauft _____ Käse.", 
    article: "den", 
    position: 2 
  },
  { 
    sentence: "Hannes trinkt _____ Kaffee.", 
    article: "den", 
    position: 2 
  },
  { 
    sentence: "Hast du _____ Zeitung?", 
    article: "die", 
    position: 2 
  },
  { 
    sentence: "Familie Meier bringt _____ Gemüse mit.", 
    article: "das", 
    position: 3 
  },
  { 
    sentence: "Der Vater hat _____ Schirm.", 
    article: "den", 
    position: 3 
  },
  { 
    sentence: "Lest ihr _____ Buch?", 
    article: "das", 
    position: 2 
  },
  { 
    sentence: "<PERSON>e kaufen _____ Schuhe.", 
    article: "die", 
    position: 2 
  }
];