export interface BestimmterArtikelItem {
  word: string;
  article: 'der' | 'die' | 'das';
}

export const bestimmterArtikelItems: BestimmterArtikelItem[] = [
  { word: 'Hobby', article: 'das' },
  { word: 'Mann', article: 'der' },
  { word: '<PERSON>ü<PERSON>', article: 'die' },
  { word: 'Computer', article: 'der' },
  { word: 'Fahrrad', article: 'das' },
  { word: 'Telefon', article: 'das' },
  { word: 'Milch', article: 'die' },
  { word: 'Restaurant', article: 'das' },
  { word: 'Arzt', article: 'der' },
  { word: 'Banane', article: 'die' },
  { word: 'Seife', article: 'die' },
  { word: 'Balkon', article: 'der' },
  { word: 'Ausweis', article: 'der' },
  { word: 'Verkäufer', article: 'der' },
  { word: 'Unterschrift', article: 'die' },
  { word: 'Mund', article: 'der' },
  { word: 'Bett', article: 'das' },
  { word: 'Kleid', article: 'das' },
  { word: 'Größe', article: 'die' },
  { word: 'Haar', article: 'das' },
];