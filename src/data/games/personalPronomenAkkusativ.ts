export interface PersonalPronomenAkkusativItem {
  sentence: string;
  pronoun: string;
  position: number; // A helyettesítendő s<PERSON> pozíciója a mondatban
  hint?: string; // Segítség zá<PERSON>ben, pl. "du", "ich" stb.
}

export const personalPronomenAkkusativItems: PersonalPronomenAkkusativItem[] = [
  { 
    sentence: "Das Auto ist schön. Ich kaufe _______.", 
    pronoun: "es", 
    position: 5 
  },
  { 
    sentence: "Der Wein schmeckt gut. Ich trinke _______ gern.", 
    pronoun: "ihn", 
    position: 5 
  },
  { 
    sentence: "Meine Freundin hat Geburtstag. Ich rufe _______ an.", 
    pronoun: "sie", 
    position: 6 
  },
  { 
    sentence: "Morgen habe ich frei. Ich freue _______ schon.", 
    pronoun: "mich", 
    position: 5 
  },
  { 
    sentence: "Unsere Eltern wohnen in Hamburg. <PERSON><PERSON> und ich besuchen _______ oft.", 
    pronoun: "sie", 
    position: 10 
  },
  { 
    sentence: "<PERSON><PERSON> und <PERSON>, es ist schön _______ zu sehen.", 
    pronoun: "euch", 
    position: 7 
  },
  { 
    sentence: "Ich liebe _______!", 
    pronoun: "dich", 
    position: 2,
    hint: "du"
  },
  { 
    sentence: "Julia und ich, wir treffen _______ morgen.", 
    pronoun: "uns", 
    position: 6 
  },
  { 
    sentence: "Wo sind Klara und Mario? Hast du _______ gesehen?", 
    pronoun: "sie", 
    position: 8 
  },
  { 
    sentence: "Das Hotel war gut. Ich kann _______ empfehlen!", 
    pronoun: "es", 
    position: 6 
  }
];