export interface BestimmterArtikelDativItem {
  sentence: string;
  article: 'dem' | 'der' | 'den';
  position: number; // A helyettesítend<PERSON> s<PERSON>í<PERSON> (index) a mondatban
}

export const bestimmterArtikelDativItems: BestimmterArtikelDativItem[] = [
  { 
    sentence: "Die Hose passt _____ Frau.", 
    article: "der", 
    position: 3 
  },
  { 
    sentence: "Das Auto gefällt _____ Mann.", 
    article: "dem", 
    position: 3 
  },
  { 
    sentence: "Die Schwester schenkt _____ Jungen das Fahrrad.", 
    article: "dem", 
    position: 3 
  },
  { 
    sentence: "Der Lehrer hilft _____ Schülern.", 
    article: "den", 
    position: 3 
  },
  { 
    sentence: "Der Pullover steht _____ Freundin.", 
    article: "der", 
    position: 3 
  },
  { 
    sentence: "Das Buch gefällt _____ Mädchen.", 
    article: "dem", 
    position: 3 
  },
  { 
    sentence: "<PERSON> hilft _____ Großmutter.", 
    article: "der", 
    position: 2 
  },
  { 
    sentence: "Der Hut passt _____ Verkäufer.", 
    article: "dem", 
    position: 3 
  },
  { 
    sentence: "Die Röcke stehen _____ Tänzerinnen.", 
    article: "den", 
    position: 3 
  },
  { 
    sentence: "Der Vater schenkt _____ Kind einen Ball.", 
    article: "dem", 
    position: 3 
  }
];