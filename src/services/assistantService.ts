// OpenAI Asszisztens API szolgáltatás

import { openai } from './openaiService';
import { usePoints } from './pointService';
import { toast } from 'sonner';

// Asszisztens üzenet típus
export interface AssistantMessage {
  role: 'user' | 'assistant' | 'system';
  content: string;
}

// Asszisztens válasz típus
export interface AssistantResponse {
  message: string;
  isLoading: boolean;
  error?: string;
}

/**
 * Kérdés küldése az OpenAI asszisztensnek
 * @param messages Az eddigi beszélgetés üzenetei
 * @param systemPrompt Rendszer prompt az asszisztens beállításához
 * @returns Az asszisztens válasza
 */
export const sendMessageToAssistant = async (
  messages: AssistantMessage[],
  systemPrompt: string = "Te egy segítőkész német nyelvtanár vagy. Segíts a felhasználónak a német nyelv tanulásában. Magyarázd el a nyelvtani szab<PERSON>, fordítsd le a szavakat és mondatokat, és adj példákat. Válaszolj magyarul, kivéve ha a felhasználó németül kérdez, akkor németül válaszolj. Használj markdown formázást a válaszaidban, hogy strukturált és könnyen olvasható legyen. Használj címsorokat (###), felsorolásokat, kiemeléseket és más markdown elemeket, ahol hasznos lehet. A német szavakat és kifejezéseket tedd dőlt betűssé vagy félkövérré, hogy kiemelkedjenek. Táblázatok esetén használj egyszerű markdown táblázatokat, és ne használj túl sok oszlopot. A táblázatok fejlécét mindig jelöld meg, és használj elválasztó sort a fejléc és a tartalom között. FONTOS: A táblázatok sorai között mindig legyen szóköz, és a fejléc elválasztó sorában mindig legyen legalább 3 kötőjel oszloponként. Példa a táblázatra: \n\n| Oszlop1 | Oszlop2 |\n|---------|---------|\n| Érték1 | Érték2 |\n| Érték3 | Érték4 |\n\nFigyelem: a táblázat előtt és után is hagyj üres sort!"
): Promise<string> => {
  try {
    // Pontok felhasználása az API hívás előtt
    try {
      await usePoints(1, 'Asszisztens válasz');
    } catch (pointError) {
      console.error('Hiba a pontok felhasználásakor:', pointError);
      // Ha nincs elég pont, jelezzük a felhasználónak
      if (pointError.response && pointError.response.status === 403) {
        toast.error('Nincs elegendő pontod az asszisztens használatához. Tölts fel pontokat a folytatáshoz!');
      } else {
        toast.error('Hiba történt a pontok felhasználásakor. Próbáld újra később!');
      }
      throw new Error('Nincs elegendő pont');
    }

    // Rendszer üzenet hozzáadása
    const formattedMessages = [
      { role: 'system', content: systemPrompt },
      ...messages.map(msg => ({
        role: msg.role,
        content: msg.content
      }))
    ];

    // Kérés küldése az OpenAI API-nak
    const response = await openai.chat.completions.create({
      model: "gpt-3.5-turbo",
      messages: formattedMessages,
      temperature: 0.7,
      max_tokens: 1000,
    });

    // Válasz kinyerése
    return response.choices[0]?.message?.content || "Sajnos nem tudtam választ generálni. Kérlek, próbáld újra.";
  } catch (error) {
    console.error('Hiba az asszisztens válaszának lekérésekor:', error);
    throw new Error(`Hiba az asszisztens válaszának lekérésekor: ${error}`);
  }
};

/**
 * Nyelvtani magyarázat kérése
 * @param topic A nyelvtani téma
 * @returns Magyarázat a témáról
 */
export const getGrammarExplanation = async (topic: string): Promise<string> => {
  const systemPrompt = `Te egy német nyelvtanár vagy, aki részletes és érthető magyarázatokat ad nyelvtani témákról.
  Magyarázd el a következő nyelvtani témát világosan, példákkal. Strukturáld a válaszod, használj felsorolásokat,
  és adj gyakorlati példákat. Válaszolj magyarul. Használj markdown formázást a válaszaidban, hogy strukturált és könnyen olvasható legyen.
  Használj címsorokat (###), felsorolásokat, kiemeléseket és más markdown elemeket, ahol hasznos lehet.
  A német szavakat és kifejezéseket tedd dőlt betűssé vagy félkövérré, hogy kiemelkedjenek.
  Táblázatok esetén használj egyszerű markdown táblázatokat, és ne használj túl sok oszlopot. A táblázatok fejlécét mindig jelöld meg, és használj elválasztó sort a fejléc és a tartalom között. FONTOS: A táblázatok sorai között mindig legyen szóköz, és a fejléc elválasztó sorában mindig legyen legalább 3 kötőjel oszloponként. Példa a táblázatra: \n\n| Oszlop1 | Oszlop2 |\n|---------|---------|\n| Érték1 | Érték2 |\n| Érték3 | Érték4 |\n\nFigyelem: a táblázat előtt és után is hagyj üres sort!`;

  const messages: AssistantMessage[] = [
    { role: 'user', content: `Kérlek, magyarázd el a következő német nyelvtani témát: ${topic}` }
  ];

  return sendMessageToAssistant(messages, systemPrompt);
};

/**
 * Fordítás kérése
 * @param text A fordítandó szöveg
 * @param targetLanguage A célnyelv (hu vagy de)
 * @returns A fordított szöveg
 */
export const getTranslation = async (text: string, targetLanguage: 'hu' | 'de'): Promise<string> => {
  const systemPrompt = `Te egy kiváló fordító vagy magyar és német nyelvek között.
  Fordítsd le a szöveget pontosan, a megfelelő nyelvhelyességgel és stílussal.
  Csak a fordítást add vissza, magyarázat nélkül.`;

  const direction = targetLanguage === 'hu' ? 'németről magyarra' : 'magyarról németre';

  const messages: AssistantMessage[] = [
    { role: 'user', content: `Fordítsd le a következő szöveget ${direction}: "${text}"` }
  ];

  return sendMessageToAssistant(messages, systemPrompt);
};

/**
 * Nyelvtani ellenőrzés kérése
 * @param text Az ellenőrizendő szöveg
 * @returns Javítások és magyarázatok
 */
export const getGrammarCheck = async (text: string): Promise<string> => {
  const systemPrompt = `Te egy német nyelvtanár vagy, aki segít a nyelvtanulóknak a hibáik javításában.
  Ellenőrizd a következő német szöveget nyelvtani hibák szempontjából.
  Javítsd ki a hibákat, és magyarázd el, mi volt a probléma. Válaszolj magyarul.
  Használj markdown formázást a válaszaidban, hogy strukturált és könnyen olvasható legyen.
  A hibás részeket jelöld ~~áthúzott~~ szöveggel, a helyes változatot pedig **félkövérrel**.
  Használj táblázatot a hibák összefoglalására, ahol az első oszlopban a hibás rész, a másodikban a helyes változat, a harmadikban pedig a magyarázat szerepel.
  Táblázatok esetén használj egyszerű markdown táblázatokat, és ne használj túl sok oszlopot. A táblázatok fejlécét mindig jelöld meg, és használj elválasztó sort a fejléc és a tartalom között. FONTOS: A táblázatok sorai között mindig legyen szóköz, és a fejléc elválasztó sorában mindig legyen legalább 3 kötőjel oszloponként. Példa a táblázatra: \n\n| Hibás | Helyes | Magyarázat |\n|---------|---------|------------|\n| Hiba1 | Javítás1 | Magyarázat1 |\n| Hiba2 | Javítás2 | Magyarázat2 |\n\nFigyelem: a táblázat előtt és után is hagyj üres sort!`;

  const messages: AssistantMessage[] = [
    { role: 'user', content: `Kérlek, ellenőrizd a következő német szöveget nyelvtani hibák szempontjából: "${text}"` }
  ];

  return sendMessageToAssistant(messages, systemPrompt);
};

/**
 * Példamondatok kérése
 * @param word A szó, amihez példamondatokat kérünk
 * @param count A kért példamondatok száma
 * @returns Példamondatok a szóval
 */
export const getExampleSentences = async (word: string, count: number = 3): Promise<string> => {
  const systemPrompt = `Te egy német nyelvtanár vagy, aki segít a nyelvtanulóknak a szókincs bővítésében.
  Adj példamondatokat a megadott német szóval. A mondatok legyenek egyszerűek, hétköznapi helyzetekben használhatók.
  Minden mondathoz adj magyar fordítást is.
  Használj markdown formázást a válaszaidban. A német mondatokat tedd **félkövérré**, a magyar fordítást pedig normál szöveggé.
  Minden példamondat legyen számozott listában. A megadott szót emeld ki *dőlt betűvel* a német mondatokban.`;

  const messages: AssistantMessage[] = [
    { role: 'user', content: `Kérlek, adj ${count} példamondatot a következő német szóval: "${word}"` }
  ];

  return sendMessageToAssistant(messages, systemPrompt);
};

/**
 * Beszélgetési gyakorlat
 * @param topic A beszélgetés témája
 * @param level A nyelvi szint (beginner, intermediate, advanced)
 * @returns Egy beszélgetés kezdeményezése a megadott témában
 */
export const startConversationPractice = async (topic: string, level: 'beginner' | 'intermediate' | 'advanced'): Promise<string> => {
  let complexity = "egyszerű";
  if (level === 'intermediate') complexity = "közepes nehézségű";
  if (level === 'advanced') complexity = "összetett";

  const systemPrompt = `Te egy német nyelvtanár vagy, aki beszélgetési gyakorlatot folytat a tanulóval.
  Kezdeményezz egy ${complexity} beszélgetést a megadott témában. Tegyél fel kérdéseket, és reagálj a tanuló válaszaira.
  A beszélgetés legyen természetes és interaktív. A válaszaidban használj ${level === 'beginner' ? 'egyszerű' : level === 'intermediate' ? 'közepes nehézségű' : 'összetett'}
  német mondatokat, és minden német mondathoz adj magyar fordítást zárójelben.
  Használj markdown formázást a válaszaidban. A német mondatokat tedd **félkövérré**, a magyar fordítást pedig normál szöveggé zárójelben.
  A kérdéseket számozott listában add meg. A kulcsfontosságú szavakat emeld ki *dőlt betűvel* a német mondatokban.`;

  const messages: AssistantMessage[] = [
    { role: 'user', content: `Kezdeményezz egy beszélgetést velem a következő témában: "${topic}". A nyelvi szintem: ${level}.` }
  ];

  return sendMessageToAssistant(messages, systemPrompt);
};
