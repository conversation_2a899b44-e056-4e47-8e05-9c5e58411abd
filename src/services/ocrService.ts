// OCR (Optical Character Recognition) szolgáltatás
import { usePoints } from './pointService';
import { toast } from 'sonner';
import { OPENAI_API_KEY } from './openaiService';

/**
 * Kép szövegének felismerése az OpenAI Vision API segítségével
 * @param imageBlob A kép blob formátumban
 * @param language A nyelv kódja (pl. 'hu' vagy 'de')
 * @returns A felismert szöveg
 */
export const recognizeTextFromImage = async (
  imageBlob: Blob,
  language: 'hu' | 'de' = 'de'
): Promise<string> => {
  try {
    // Pontok felhasználása az API hívás előtt
    try {
      await usePoints(3, 'OCR szövegfelismerés');
    } catch (pointError) {
      console.error('Hiba a pontok felhasználásakor:', pointError);
      // Ha nincs elég pont, j<PERSON><PERSON><PERSON><PERSON> a felhasználónak
      if (pointError.response && pointError.response.status === 403) {
        toast.error('Nincs elegend<PERSON> pontod a szövegfelismeréshez. Tölts fel pontokat a folytatáshoz!');
      } else {
        toast.error('Hiba történt a pontok felhasználásakor. Próbáld újra később!');
      }
      throw new Error('Nincs elegendő pont');
    }

    // Kép konvertálása base64 formátumba
    const base64Image = await blobToBase64(imageBlob);

    // API kérés összeállítása
    const response = await fetch('https://api.openai.com/v1/chat/completions', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${OPENAI_API_KEY}`,
      },
      body: JSON.stringify({
        model: 'gpt-4-vision-preview',
        messages: [
          {
            role: 'user',
            content: [
              {
                type: 'text',
                text: `Kérlek, olvasd ki az összes szöveget a képről. A szöveg valószínűleg ${language === 'hu' ? 'magyar' : 'német'} nyelvű. Csak a felismert szöveget add vissza, formázás és magyarázat nélkül.`
              },
              {
                type: 'image_url',
                image_url: {
                  url: base64Image,
                }
              }
            ]
          }
        ],
        max_tokens: 300,
      }),
    });

    if (!response.ok) {
      let errorMessage = `OpenAI API hiba: ${response.status}`;
      try {
        const errorData = await response.json();
        console.error('OpenAI API hiba:', errorData);
        errorMessage = errorData.error?.message || errorMessage;
      } catch (e) {
        // Ha nem JSON a válasz, akkor marad az eredeti hibaüzenet
      }
      throw new Error(errorMessage);
    }

    // A válasz feldolgozása
    const data = await response.json();
    const recognizedText = data.choices[0]?.message?.content || '';
    
    return recognizedText.trim();
  } catch (error) {
    console.error('Hiba a szövegfelismerés során:', error);
    throw error;
  }
};

/**
 * Blob konvertálása base64 formátumba
 * @param blob A konvertálandó blob
 * @returns A base64 formátumú kép
 */
const blobToBase64 = (blob: Blob): Promise<string> => {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();
    reader.onloadend = () => {
      const base64String = reader.result as string;
      resolve(base64String);
    };
    reader.onerror = reject;
    reader.readAsDataURL(blob);
  });
};
