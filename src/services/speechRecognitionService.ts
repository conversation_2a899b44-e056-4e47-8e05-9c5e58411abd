// OpenAI API szolgáltatás a hangfelismeréshez (speech-to-text)
import { usePoints } from './pointService';
import { toast } from 'sonner';
import { OPENAI_API_KEY } from './openaiService';

/**
 * Hangfelvétel felismerése az OpenAI Whisper API segítségével
 * @param audioBlob A hangfelvétel blob formátumban
 * @param language A nyelv kódja (pl. 'hu' vagy 'de')
 * @returns A felismert szöveg
 */
export const recognizeSpeech = async (
  audioBlob: Blob,
  language: 'hu' | 'de' = 'de'
): Promise<string> => {
  try {
    // Pontok felhasználása az API hívás előtt
    try {
      await usePoints(2, 'Hangfelismerés');
    } catch (pointError) {
      console.error('Hiba a pontok felhasználásakor:', pointError);
      // Ha nincs elég pont, j<PERSON><PERSON><PERSON>k a felhasználónak
      if (pointError.response && pointError.response.status === 403) {
        toast.error('Ninc<PERSON> elegend<PERSON> pontod a hangfelismeréshez. Tölts fel pontokat a folytatáshoz!');
      } else {
        toast.error('Hiba történt a pontok felhasználásakor. Próbáld újra később!');
      }
      throw new Error('Nincs elegendő pont');
    }

    // FormData létrehozása a hangfelvétel küldéséhez
    const formData = new FormData();
    formData.append('file', audioBlob, 'recording.webm');
    formData.append('model', 'whisper-1');
    formData.append('language', language);
    formData.append('response_format', 'text');

    // API hívás az OpenAI Whisper szolgáltatáshoz
    const response = await fetch('https://api.openai.com/v1/audio/transcriptions', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${OPENAI_API_KEY}`,
      },
      body: formData,
    });

    if (!response.ok) {
      let errorMessage = `OpenAI API hiba: ${response.status}`;
      try {
        const errorData = await response.json();
        console.error('OpenAI API hiba:', errorData);
        errorMessage = errorData.error?.message || errorMessage;
      } catch (e) {
        // Ha nem JSON a válasz, akkor marad az eredeti hibaüzenet
      }
      throw new Error(errorMessage);
    }

    // A válasz szövegként való feldolgozása
    const text = await response.text();
    return text;
  } catch (error) {
    console.error('Hiba a hangfelismerés során:', error);
    throw error;
  }
};

/**
 * Hangfelvétel összehasonlítása egy elvárt szöveggel
 * @param recognizedText A felismert szöveg
 * @param expectedText Az elvárt szöveg
 * @returns Értékelés az egyezésről (0-100%)
 */
export const evaluatePronunciation = (
  recognizedText: string,
  expectedText: string
): { score: number; feedback: string } => {
  // Szövegek normalizálása (kisbetűsítés, írásjelek eltávolítása)
  const normalize = (text: string) => {
    return text
      .toLowerCase()
      .replace(/[.,\/#!$%\^&\*;:{}=\-_`~()]/g, '')
      .replace(/\s{2,}/g, ' ')
      .trim();
  };

  const normalizedRecognized = normalize(recognizedText);
  const normalizedExpected = normalize(expectedText);

  // Egyszerű szóalapú összehasonlítás
  const recognizedWords = normalizedRecognized.split(' ');
  const expectedWords = normalizedExpected.split(' ');

  // Helyes szavak számolása
  let correctWords = 0;
  for (const word of recognizedWords) {
    if (expectedWords.includes(word)) {
      correctWords++;
    }
  }

  // Pontszám számítása (0-100%)
  const totalWords = Math.max(recognizedWords.length, expectedWords.length);
  const score = totalWords > 0 ? Math.round((correctWords / totalWords) * 100) : 0;

  // Visszajelzés generálása
  let feedback = '';
  if (score >= 90) {
    feedback = 'Kiváló! Tökéletes kiejtés.';
  } else if (score >= 75) {
    feedback = 'Nagyon jó! Majdnem tökéletes.';
  } else if (score >= 50) {
    feedback = 'Jó próbálkozás! Gyakorolj még egy kicsit.';
  } else {
    feedback = 'Próbáld újra! Figyelj a kiejtésre.';
  }

  return { score, feedback };
};
