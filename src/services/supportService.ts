import api from './apiService';

// Support ticket típus
export interface SupportTicket {
  id: string;
  subject: string;
  status: 'open' | 'in_progress' | 'closed';
  priority: 'low' | 'medium' | 'high';
  creatorId: string;
  creator?: {
    id: string;
    name: string;
    email: string;
  };
  messages?: SupportMessage[];
  createdAt: string;
  updatedAt: string;
  closedAt?: string;
  _count?: {
    messages: number;
  };
}

// Support üzenet típus
export interface SupportMessage {
  id: string;
  content: string;
  isFromAdmin: boolean;
  ticketId: string;
  userId: string;
  user?: {
    id: string;
    name: string;
    email: string;
  };
  aiSuggestion?: string;
  createdAt: string;
  updatedAt: string;
}

// Új ticket létrehozása
export const createTicket = async (subject: string, message: string, priority: string = 'medium'): Promise<SupportTicket> => {
  try {
    const response = await api.post('/support/tickets', { subject, message, priority });
    return response.data.data;
  } catch (error) {
    console.error('Hiba a support ticket létrehozásakor:', error);
    throw error;
  }
};

// Felhasználó saját ticketjeinek lekérdezése
export const getUserTickets = async (): Promise<SupportTicket[]> => {
  try {
    const response = await api.get('/support/tickets');
    return response.data.data;
  } catch (error) {
    console.error('Hiba a support ticketek lekérdezésekor:', error);
    throw error;
  }
};

// Összes ticket lekérdezése (csak adminoknak)
export const getAllTickets = async (): Promise<SupportTicket[]> => {
  try {
    const response = await api.get('/support/admin/tickets');
    return response.data.data;
  } catch (error) {
    console.error('Hiba az összes support ticket lekérdezésekor:', error);
    throw error;
  }
};

// Ticket részleteinek lekérdezése
export const getTicketDetails = async (ticketId: string): Promise<SupportTicket> => {
  try {
    const response = await api.get(`/support/tickets/${ticketId}`);
    return response.data.data;
  } catch (error) {
    console.error('Hiba a support ticket részleteinek lekérdezésekor:', error);
    throw error;
  }
};

// Üzenet küldése egy tickethez
export const addMessageToTicket = async (ticketId: string, content: string): Promise<SupportMessage> => {
  try {
    const response = await api.post(`/support/tickets/${ticketId}/messages`, { content });
    return response.data.data;
  } catch (error) {
    console.error('Hiba az üzenet küldésekor:', error);
    throw error;
  }
};

// Ticket státuszának módosítása (csak adminoknak)
export const updateTicketStatus = async (ticketId: string, status: string, priority?: string): Promise<SupportTicket> => {
  try {
    const response = await api.patch(`/support/admin/tickets/${ticketId}`, { status, priority });
    return response.data.data;
  } catch (error) {
    console.error('Hiba a ticket státuszának módosításakor:', error);
    throw error;
  }
};

// OpenAI válaszjavaslat kérése (csak adminoknak)
export const getAISuggestion = async (ticketId: string): Promise<{ suggestion: string }> => {
  try {
    const response = await api.post(`/support/admin/tickets/${ticketId}/ai-suggestion`);
    return response.data.data;
  } catch (error) {
    console.error('Hiba az AI válaszjavaslat kérésekor:', error);
    throw error;
  }
};
