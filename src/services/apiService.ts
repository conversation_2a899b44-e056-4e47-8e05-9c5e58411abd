import axios from 'axios';

// API alap URL - production környezetben az app.digitalisnemet.hu-ra mutat
const API_URL = import.meta.env.PROD ? 'http://app.digitalisnemet.hu:3001/api' : '/api';

// Axios instance létrehozása
const api = axios.create({
  baseURL: API_URL,
  headers: {
    'Content-Type': 'application/json'
  }
});

// Request interceptor a token hozzáadásához
api.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('token');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Response interceptor a hibák kezeléséhez
api.interceptors.response.use(
  (response) => {
    return response;
  },
  (error) => {
    // Unauthorized hiba esetén kijelentkeztetjük a felhasználót
    if (error.response && error.response.status === 401) {
      localStorage.removeItem('token');
      localStorage.removeItem('currentUser');
      window.location.href = '/login';
    }
    return Promise.reject(error);
  }
);

// Autentikációs szolgáltatások
export const authService = {
  // Regisztráció
  register: async (name: string, email: string, password: string, invitationToken?: string) => {
    const response = await api.post('/auth/register', { name, email, password, invitationToken });
    if (response.data.token) {
      localStorage.setItem('token', response.data.token);
      localStorage.setItem('currentUser', JSON.stringify(response.data.user));
    }
    return response.data;
  },

  // Bejelentkezés
  login: async (email: string, password: string) => {
    const response = await api.post('/auth/login', { email, password });
    if (response.data.token) {
      localStorage.setItem('token', response.data.token);
      localStorage.setItem('currentUser', JSON.stringify(response.data.user));
    }
    return response.data;
  },

  // Kijelentkezés
  logout: async () => {
    await api.get('/auth/logout');
    localStorage.removeItem('token');
    localStorage.removeItem('currentUser');
  },

  // Aktuális felhasználó lekérése
  getCurrentUser: async () => {
    const response = await api.get('/auth/me');
    return response.data;
  }
};

// Előfizetési szolgáltatások
export const subscriptionService = {
  // Előfizetés létrehozása
  createSubscription: async (priceId?: string) => {
    const response = await api.post('/subscriptions', { priceId });
    return response.data;
  },

  // Előfizetés állapotának lekérése
  getSubscriptionStatus: async () => {
    const response = await api.get('/subscriptions/status');
    return response.data;
  },

  // Session ellenőrzése
  verifySession: async (sessionId: string) => {
    const response = await api.get(`/subscriptions/verify-session/${sessionId}`);
    return response.data;
  },

  // Előfizetés lemondása
  cancelSubscription: async () => {
    const response = await api.post('/subscriptions/cancel');
    return response.data;
  },

  // Előfizetés újraaktiválása
  reactivateSubscription: async () => {
    const response = await api.post('/subscriptions/reactivate');
    return response.data;
  },

  // Számlák lekérése
  getInvoices: async () => {
    const response = await api.get('/subscriptions/invoices');
    return response.data;
  },

  // Egy számla lekérése
  getInvoice: async (invoiceId: string) => {
    const response = await api.get(`/subscriptions/invoices/${invoiceId}`);
    return response.data;
  }
};

// Meghívó szolgáltatások
export const invitationService = {
  // Meghívó létrehozása
  createInvitation: async (email: string) => {
    const response = await api.post('/invitations', { email });
    return response.data;
  },

  // Meghívó ellenőrzése
  verifyInvitation: async (token: string) => {
    const response = await api.get(`/invitations/verify/${token}`);
    return response.data;
  },

  // Meghívó elfogadása
  acceptInvitation: async (token: string, userId: string) => {
    const response = await api.post(`/invitations/accept/${token}`, { userId });
    return response.data;
  },

  // Meghívók listázása
  getInvitations: async () => {
    const response = await api.get('/invitations');
    return response.data;
  },

  // Meghívó törlése
  deleteInvitation: async (id: string) => {
    const response = await api.delete(`/invitations/${id}`);
    return response.data;
  },

  // Meghívó újraküldése
  resendInvitation: async (id: string) => {
    const response = await api.post(`/invitations/resend/${id}`);
    return response.data;
  }
};

// Tanári szolgáltatások
export const teacherService = {
  // Tanári előfizetés létrehozása
  createTeacherSubscription: async () => {
    const response = await api.post('/teacher/subscribe');
    return response.data;
  },

  // Tanári előfizetés ellenőrzése
  verifyTeacherSubscription: async (sessionId: string) => {
    const response = await api.get(`/teacher/verify-subscription/${sessionId}`);
    return response.data;
  },

  // Ingyenes próba csomag aktiválása
  activateFreeTrialPackage: async () => {
    const response = await api.post('/teacher/activate-free-trial');
    return response.data;
  },

  // Diák meghívása
  inviteStudent: async (email: string) => {
    const response = await api.post('/teacher/invite-student', { email });
    return response.data;
  },

  // Diákok listázása
  getStudents: async () => {
    const response = await api.get('/teacher/students');
    return response.data;
  }
};

export default api;
