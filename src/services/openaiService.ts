
// OpenAI API szolgáltatás a szövegfelolvasáshoz
import OpenAI from 'openai';
import { usePoints } from './pointService';
import { toast } from 'sonner';

// Az API kulcs
export const OPENAI_API_KEY = '********************************************************************************************************************************************************************';

// OpenAI kliens példány létrehozása
export const openai = new OpenAI({
  apiKey: OPENAI_API_KEY,
  dangerouslyAllowBrowser: true // Engedélyezzük a böngészőben való futtatást
});

// A modell neve
const MODEL = 'tts-1';

/**
 * Szöveg felolvasása az OpenAI API segítségével
 * @param text A felolvasandó szöveg
 * @param language A nyelv kódja (pl. 'hu-HU' vagy 'de-DE')
 * @param speed A beszéd sebessége (0.25 és 4.0 között, alapértelmezett: 1.0)
 * @param customVoice Egyedi hang beállítása (felülírja a nyelv alapján választott hangot)
 * @returns Az audio blob URL-je, amit közvetlenül le lehet játszani
 */
export const textToSpeech = async (
  text: string,
  language: 'hu-HU' | 'de-DE',
  speed: number = 1.0,
  customVoice?: string
): Promise<string> => {
  try {
    // Pontok felhasználása az API hívás előtt
    try {
      await usePoints(1, 'Szövegfelolvasás');
    } catch (pointError) {
      console.error('Hiba a pontok felhasználásakor:', pointError);
      // Ha nincs elég pont, jelezzük a felhasználónak
      if (pointError.response && pointError.response.status === 403) {
        toast.error('Nincs elegendő pontod a szövegfelolvasáshoz. Tölts fel pontokat a folytatáshoz!');
      } else {
        toast.error('Hiba történt a pontok felhasználásakor. Próbáld újra később!');
      }
      throw new Error('Nincs elegendő pont');
    }

    // Beszédbeállítások betöltése a localStorage-ból, ha vannak
    let speechSettings;
    try {
      const savedSettings = localStorage.getItem('speechSettings');
      if (savedSettings) {
        speechSettings = JSON.parse(savedSettings);
      }
    } catch (error) {
      console.error('Hiba a beszédbeállítások betöltésekor:', error);
    }

    // A voice paraméter beállítása a nyelv alapján vagy a beállításokból
    let voice = 'alloy';

    // Ha van egyedi hang paraméter, azt használjuk
    if (customVoice) {
      voice = customVoice;
    }
    // Különben a beállításokból vesszük a hangot, ha van
    else if (speechSettings) {
      if (language === 'hu-HU' && speechSettings.hungarianVoice) {
        voice = speechSettings.hungarianVoice;
      } else if (language === 'de-DE' && speechSettings.germanVoice) {
        voice = speechSettings.germanVoice;
      } else if (speechSettings.defaultVoice) {
        voice = speechSettings.defaultVoice;
      }
    }
    // Alapértelmezett hangok, ha nincs beállítás
    else {
      voice = language === 'hu-HU' ? 'alloy' : 'onyx';
    }

    // Beszédsebesség beállítása a beállításokból, ha van
    let validSpeed = speed;
    if (speechSettings && speechSettings.speed && !speed) {
      validSpeed = speechSettings.speed;
    }

    // Ellenőrizzük, hogy a sebesség a megengedett tartományban van-e (0.25 és 4.0 között)
    validSpeed = Math.max(0.25, Math.min(4.0, validSpeed));

    const response = await fetch('https://api.openai.com/v1/audio/speech', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${OPENAI_API_KEY}`,
      },
      body: JSON.stringify({
        model: MODEL,
        input: text,
        voice: voice,
        response_format: 'mp3',
        speed: validSpeed,
      }),
    });

    if (!response.ok) {
      const errorData = await response.json();
      console.error('OpenAI API hiba:', errorData);
      throw new Error(`OpenAI API hiba: ${response.status}`);
    }

    // Az audio adatok blob-bá alakítása
    const audioBlob = await response.blob();

    // Blob URL létrehozása, amit közvetlenül le lehet játszani
    return URL.createObjectURL(audioBlob);
  } catch (error) {
    console.error('Hiba a szövegfelolvasás során:', error);
    throw error;
  }
};
