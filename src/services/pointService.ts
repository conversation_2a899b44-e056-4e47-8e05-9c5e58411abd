import api from './apiService';

// Pont tranzakció típus
export interface PointTransaction {
  id: string;
  userId: string;
  amount: number;
  type: 'purchase' | 'usage' | 'subscription';
  description: string;
  stripePaymentIntentId?: string;
  createdAt: string;
  updatedAt: string;
}

// Pontok lekérdezése
export const getUserPoints = async (): Promise<{ points: number; transactions: PointTransaction[] }> => {
  try {
    const response = await api.get('/points');
    return response.data.data;
  } catch (error) {
    console.error('Hiba a pontok lekérdezésekor:', error);
    throw error;
  }
};

// Pontok felhasználása
export const usePoints = async (amount: number = 1, description: string = 'API hívás'): Promise<{ remainingPoints: number; transaction: PointTransaction }> => {
  try {
    const response = await api.post('/points/use', { amount, description });
    return response.data.data;
  } catch (error) {
    console.error('Hiba a pontok felhasználásakor:', error);
    throw error;
  }
};

// Pontcsomag vásárlása
export const purchasePoints = async (packageId: string): Promise<{ sessionId: string; url: string }> => {
  try {
    const response = await api.post('/points/purchase', { packageId });
    return {
      sessionId: response.data.sessionId,
      url: response.data.url
    };
  } catch (error) {
    console.error('Hiba a pontcsomag vásárlásakor:', error);
    throw error;
  }
};

// Pontcsomag vásárlás ellenőrzése
export const verifyPointPurchase = async (sessionId: string): Promise<{ points: number; transaction: PointTransaction }> => {
  try {
    const response = await api.get(`/points/verify-purchase/${sessionId}`);
    return response.data.data;
  } catch (error) {
    console.error('Hiba a pontcsomag vásárlás ellenőrzésekor:', error);
    throw error;
  }
};

// Pontcsomagok adatai Stripe termék azonosítókkal
export const POINT_PACKAGES = [
  { id: 'prod_SA9WopBkHjC4iO', name: '4000 pont', points: 4000, price: 5000 },
  { id: 'prod_SA9XQfqwr7PohC', name: '9000 pont', points: 9000, price: 10000 },
  { id: 'prod_SA9Xpdu6nzCR6y', name: '14000 pont', points: 14000, price: 15000 },
  { id: 'prod_SA9YwZAI9rOOC6', name: '18000 pont', points: 18000, price: 20000 },
  { id: 'prod_SA9ZDpDtKAguo0', name: '22000 pont', points: 22000, price: 25000 },
  { id: 'prod_SA9Zlo40dGcWFh', name: '25000 pont', points: 25000, price: 30000 },
];
