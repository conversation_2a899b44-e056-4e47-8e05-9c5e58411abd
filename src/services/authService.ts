import { toast } from "sonner";
import api from './apiService';

// Felhasználói típus definíció
export interface User {
  id: string;
  email: string;
  name: string;
  subscriptionStatus?: 'active' | 'inactive' | 'canceled';
  subscriptionEndDate?: string | null;
  stripeCustomerId?: string | null;
  stripeSubscriptionId?: string | null;
}

// <PERSON><PERSON><PERSON>, hogy a felhasználó be van-e jelentkezve
export const isAuthenticated = (): boolean => {
  return !!localStorage.getItem('token');
};

// <PERSON><PERSON><PERSON>, hogy a felhasználónak van-e aktív előfizetése
export const hasActiveSubscription = async (): Promise<boolean> => {
  try {
    const response = await api.get('/subscriptions/status');
    return response.data.data.isActive;
  } catch (error) {
    console.error('Hiba az előfizetés ellenőrzésekor:', error);
    return false;
  }
};

// Felhasználó regisztrálása
export const registerUser = async (
  email: string, 
  password: string, 
  name: string
): Promise<User> => {
  try {
    const response = await api.post('/auth/register', {
      name,
      email,
      password
    });
    
    // Token mentése
    if (response.data.token) {
      localStorage.setItem('token', response.data.token);
      localStorage.setItem('currentUser', JSON.stringify(response.data.user));
    }
    
    return response.data.user;
  } catch (error) {
    console.error('Regisztrációs hiba:', error);
    throw error;
  }
};

// Felhasználó bejelentkeztetése
export const loginUser = async (email: string, password: string): Promise<User> => {
  try {
    const response = await api.post('/auth/login', {
      email,
      password
    });
    
    // Token mentése
    if (response.data.token) {
      localStorage.setItem('token', response.data.token);
      localStorage.setItem('currentUser', JSON.stringify(response.data.user));
    }
    
    return response.data.user;
  } catch (error) {
    console.error('Bejelentkezési hiba:', error);
    throw error;
  }
};

// Felhasználó kijelentkeztetése
export const logoutUser = async (): Promise<void> => {
  try {
    await api.get('/auth/logout');
  } catch (error) {
    console.error('Kijelentkezési hiba:', error);
  } finally {
    localStorage.removeItem('token');
    localStorage.removeItem('currentUser');
  }
};

// Aktuális felhasználó lekérése
export const getCurrentUser = async (): Promise<User | null> => {
  try {
    // Ellenőrizzük, hogy van-e token
    const token = localStorage.getItem('token');
    if (!token) return null;
    
    // Felhasználó lekérése az API-ról
    const response = await api.get('/auth/me');
    
    // Felhasználó mentése a localStorage-ba
    localStorage.setItem('currentUser', JSON.stringify(response.data.data));
    
    return response.data.data;
  } catch (error) {
    console.error('Hiba a felhasználó lekérésekor:', error);
    localStorage.removeItem('token');
    localStorage.removeItem('currentUser');
    return null;
  }
};

// Inicializálás - ellenőrizzük a tokent
export const initAuth = (): void => {
  const token = localStorage.getItem('token');
  if (token) {
    // A token létezik, de érvényességét a getCurrentUser fogja ellenőrizni
    // amikor az AuthContext betöltődik
  }
};

// Inicializáljuk az autentikációt
initAuth();