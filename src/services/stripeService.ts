import { loadStripe } from '@stripe/stripe-js';
import api from './apiService';

// Stripe publikus kulcs
const STRIPE_PUBLIC_KEY = 'pk_test_51RFbaGPDb2KbDM6G13k9X8tBsrgmVJqEkTc9JDc6Td8UZ2rk5WO0jHsSJoLxzOsHNUz0XSSG3RWtNgT6dkPIK0bx00WPVo8oXQ';

// Stripe termék azonosító
export const PRODUCT_ID = 'prod_SIVdw7ceeWtlkJ';

// Stripe kliens betöltése
export const stripePromise = loadStripe(STRIPE_PUBLIC_KEY);

// Stripe checkout session létrehozása
export const createCheckoutSession = async (priceId?: string): Promise<string> => {
  try {
    // Előfizetés létrehozása API-n keresztül
    const response = await api.post('/subscriptions', { priceId });

    // Checkout URL visszaadása
    return response.data.url;
  } catch (error) {
    console.error('Hiba a Stripe checkout session létrehozásakor:', error);
    throw new Error('Nem sikerült létrehozni a fizetési munkamenetet');
  }
};

// Előfizetés állapotának lekérése
export const getSubscriptionStatus = async (): Promise<{
  isActive: boolean;
  status?: 'active' | 'inactive' | 'canceled';
  currentPeriodEnd?: Date | null;
  cancelAtPeriodEnd?: boolean;
}> => {
  try {
    // Előfizetés állapotának lekérése API-n keresztül
    const response = await api.get('/subscriptions/status');

    // Adatok visszaadása
    return response.data.data;
  } catch (error) {
    console.error('Hiba a Stripe előfizetés állapotának lekérésekor:', error);

    // Hiba esetén inaktív állapotot adunk vissza
    return {
      isActive: false
    };
  }
};

// Előfizetés lemondása
export const cancelSubscription = async (): Promise<void> => {
  try {
    // Előfizetés lemondása API-n keresztül
    await api.post('/subscriptions/cancel');
  } catch (error) {
    console.error('Hiba a Stripe előfizetés lemondásakor:', error);
    throw new Error('Nem sikerült lemondani az előfizetést');
  }
};

// Előfizetés újraaktiválása
export const reactivateSubscription = async (): Promise<void> => {
  try {
    // Előfizetés újraaktiválása API-n keresztül
    await api.post('/subscriptions/reactivate');
  } catch (error) {
    console.error('Hiba a Stripe előfizetés újraaktiválásakor:', error);
    throw new Error('Nem sikerült újraaktiválni az előfizetést');
  }
};