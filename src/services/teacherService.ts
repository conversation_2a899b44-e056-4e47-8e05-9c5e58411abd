import api from './apiService';

// Tan<PERSON>ri csomag adatai
export interface TeacherPackage {
  productId: string;
  name: string;
  price: number;
  monthlyPoints: number;
  maxStudents: number;
  studentGiftPoints: number;
}

// Ingyenes próba csomag adatai
export interface FreeTrialPackage {
  productId: string;
  name: string;
  price: number;
  points: number;
}

// Diák adatok
export interface Student {
  id: string;
  name: string;
  email: string;
  points: number;
  createdAt: string;
}

// Tanár-<PERSON><PERSON> kapcsolat
export interface TeacherStudentRelation {
  id: string;
  student: Student;
  status: string;
  createdAt: string;
}

// Tan<PERSON>ri előfizetés létrehozása
export const createTeacherSubscription = async (): Promise<{ sessionId: string; url: string }> => {
  try {
    const response = await api.post('/teacher/subscribe');
    return {
      sessionId: response.data.sessionId,
      url: response.data.url
    };
  } catch (error) {
    console.error('Hiba a tanári előfizetés létrehozásakor:', error);
    throw error;
  }
};

// <PERSON><PERSON><PERSON> előfi<PERSON>tés ellenőrzése
export const verifyTeacherSubscription = async (sessionId: string): Promise<{
  isTeacher: boolean;
  maxStudents: number;
  monthlyPoints: number;
}> => {
  try {
    const response = await api.get(`/teacher/verify-subscription/${sessionId}`);
    return response.data.data;
  } catch (error) {
    console.error('Hiba a tanári előfizetés ellenőrzésekor:', error);
    throw error;
  }
};

// Ingyenes próba csomag aktiválása
export const activateFreeTrialPackage = async (): Promise<{ points: number }> => {
  try {
    const response = await api.post('/teacher/activate-free-trial');
    return response.data.data;
  } catch (error) {
    console.error('Hiba az ingyenes próba csomag aktiválásakor:', error);
    throw error;
  }
};

// Diák meghívása
export const inviteStudent = async (email: string): Promise<{ invitation: { id: string; email: string; status: string; expiresAt: string } }> => {
  try {
    const response = await api.post('/teacher/invite-student', { email });
    return response.data.data;
  } catch (error) {
    console.error('Hiba a diák meghívása során:', error);
    throw error;
  }
};

// Diákok listázása
export const getStudents = async (): Promise<TeacherStudentRelation[]> => {
  try {
    const response = await api.get('/teacher/students');
    return response.data.data;
  } catch (error) {
    console.error('Hiba a diákok listázása során:', error);
    throw error;
  }
};

// Csomag adatok
export const TEACHER_PACKAGE: TeacherPackage = {
  productId: 'prod_SI7DHRY229d8SP',
  name: 'Tanári csomag',
  price: 7500,
  monthlyPoints: 10000,
  maxStudents: 20,
  studentGiftPoints: 500
};

export const FREE_TRIAL_PACKAGE: FreeTrialPackage = {
  productId: 'prod_SI7COuGhfmC6H9',
  name: 'Ingyenes próba csomag',
  price: 0,
  points: 15
};
