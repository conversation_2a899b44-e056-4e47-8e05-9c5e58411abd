// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model User {
  id              String         @id @default(uuid())
  name            String
  email           String         @unique
  password        String
  stripeCustomerId String?
  points          Int            @default(15) // Alapértelmezett 15 pont az új felhas<PERSON>nak (ingyenes próba csomag)
  isA<PERSON><PERSON>        @default(false) // Admin jogosultság
  isTeacher       <PERSON>        @default(false) // Tan<PERSON>ri jogosultság
  teacherSubscriptionId String?  // Tan<PERSON><PERSON> előfizetés azonosítója
  maxStudents     Int?           // Maximum diákok száma (tanári előfi<PERSON>)
  monthlyPoints   Int?           // Havi pontok száma (taná<PERSON> el<PERSON>)
  resetPasswordToken String?     // <PERSON><PERSON><PERSON><PERSON>tási token
  resetPasswordExpire DateTime?  // <PERSON><PERSON><PERSON><PERSON> v<PERSON>llítási token lejárati ideje
  createdAt       DateTime       @default(now())
  updatedAt       DateTime       @updatedAt
  subscriptions   Subscription[]
  payments        Payment[]
  sentInvitations Invitation[]   @relation("SentInvitations")
  receivedInvitations Invitation[] @relation("ReceivedInvitations")
  pointTransactions PointTransaction[]
  createdTickets  SupportTicket[] @relation("TicketCreator")
  supportMessages SupportMessage[]
  loginLogs       LoginLog[]     // Bejelentkezési naplók

  // Tanári kapcsolatok
  teacherOf       TeacherStudent[] @relation("Teacher")
  studentOf       TeacherStudent[] @relation("Student")
  createdTasks    GroupTask[]     // Tanár által létrehozott feladatok
  assignedTasks   StudentTask[]   // Diákhoz rendelt feladatok
  sentPoints      PointTransfer[] @relation("PointSender")
  receivedPoints  PointTransfer[] @relation("PointReceiver")
}

// Bejelentkezési napló modell
model LoginLog {
  id              String         @id @default(uuid())
  userId          String
  ipAddress       String?
  userAgent       String?
  success         Boolean        @default(true)
  createdAt       DateTime       @default(now())
  user            User           @relation(fields: [userId], references: [id], onDelete: Cascade)
}

model Subscription {
  id                    String   @id @default(uuid())
  userId                String
  user                  User     @relation(fields: [userId], references: [id])
  stripeSubscriptionId  String   @unique
  stripePriceId         String
  stripeCurrentPeriodEnd DateTime
  status                String   @default("inactive") // active, canceled, inactive
  cancelAtPeriodEnd     Boolean  @default(false)
  createdAt             DateTime @default(now())
  updatedAt             DateTime @updatedAt
}

model Payment {
  id                  String   @id @default(uuid())
  userId              String
  user                User     @relation(fields: [userId], references: [id])
  stripePaymentIntentId String  @unique
  stripeCustomerId    String
  amount              Int
  currency            String   @default("huf")
  status              String   @default("processing") // succeeded, processing, failed, canceled
  paymentMethod       String?
  description         String?
  createdAt           DateTime @default(now())
  updatedAt           DateTime @updatedAt
}

model Invitation {
  id                  String   @id @default(uuid())
  email               String
  token               String   @unique
  status              String   @default("pending") // pending, accepted, expired
  inviterId           String
  inviter             User     @relation("SentInvitations", fields: [inviterId], references: [id])
  inviteeId           String?  // Null, ha még nem fogadták el
  invitee             User?    @relation("ReceivedInvitations", fields: [inviteeId], references: [id])
  expiresAt           DateTime
  createdAt           DateTime @default(now())
  updatedAt           DateTime @updatedAt
}

model PointTransaction {
  id                  String   @id @default(uuid())
  userId              String
  user                User     @relation(fields: [userId], references: [id])
  amount              Int      // Pozitív érték feltöltés, negatív érték felhasználás
  type                String   // "purchase" (vásárlás), "usage" (felhasználás), "subscription" (előfizetés), "teacher_subscription", "teacher_transfer", "teacher_gift"
  description         String?
  stripePaymentIntentId String?  // Csak vásárlás esetén
  createdAt           DateTime @default(now())
  updatedAt           DateTime @updatedAt
}

model SupportTicket {
  id                  String   @id @default(uuid())
  subject             String
  status              String   @default("open") // open, in_progress, closed
  priority            String   @default("medium") // low, medium, high
  creatorId           String
  creator             User     @relation("TicketCreator", fields: [creatorId], references: [id])
  messages            SupportMessage[]
  createdAt           DateTime @default(now())
  updatedAt           DateTime @updatedAt
  closedAt            DateTime?
}

model SupportMessage {
  id                  String   @id @default(uuid())
  content             String
  isFromAdmin         Boolean  @default(false)
  ticketId            String
  ticket              SupportTicket @relation(fields: [ticketId], references: [id])
  userId              String
  user                User     @relation(fields: [userId], references: [id])
  aiSuggestion        String?  // OpenAI által javasolt válasz
  createdAt           DateTime @default(now())
  updatedAt           DateTime @updatedAt
}

// Tanár-diák kapcsolat modell
model TeacherStudent {
  id                  String   @id @default(uuid())
  teacherId           String
  teacher             User     @relation("Teacher", fields: [teacherId], references: [id], onDelete: Cascade)
  studentId           String
  student             User     @relation("Student", fields: [studentId], references: [id], onDelete: Cascade)
  status              String   @default("active") // active, inactive
  createdAt           DateTime @default(now())
  updatedAt           DateTime @updatedAt

  @@unique([teacherId, studentId])
}

// Csoportos feladat modell
model GroupTask {
  id                  String   @id @default(uuid())
  title               String
  description         String?
  teacherId           String
  teacher             User     @relation(fields: [teacherId], references: [id], onDelete: Cascade)
  dueDate             DateTime?
  createdAt           DateTime @default(now())
  updatedAt           DateTime @updatedAt
  studentTasks        StudentTask[]
}

// Diák feladat modell
model StudentTask {
  id                  String   @id @default(uuid())
  groupTaskId         String
  groupTask           GroupTask @relation(fields: [groupTaskId], references: [id], onDelete: Cascade)
  studentId           String
  student             User     @relation(fields: [studentId], references: [id], onDelete: Cascade)
  status              String   @default("assigned") // assigned, in_progress, completed
  completedAt         DateTime?
  score               Int?
  feedback            String?
  createdAt           DateTime @default(now())
  updatedAt           DateTime @updatedAt

  @@unique([groupTaskId, studentId])
}

// Pont átutalás modell
model PointTransfer {
  id                  String   @id @default(uuid())
  fromUserId          String
  fromUser            User     @relation("PointSender", fields: [fromUserId], references: [id], onDelete: Cascade)
  toUserId            String
  toUser              User     @relation("PointReceiver", fields: [toUserId], references: [id], onDelete: Cascade)
  amount              Int
  description         String?
  createdAt           DateTime @default(now())
}