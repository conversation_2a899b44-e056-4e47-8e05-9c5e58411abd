-- <PERSON><PERSON> me<PERSON> hozz<PERSON>adása a User táblá<PERSON>z
ALTER TABLE "User" ADD COLUMN "isTeacher" BOOLEAN NOT NULL DEFAULT false;
ALTER TABLE "User" ADD COLUMN "teacherSubscriptionId" TEXT;
ALTER TABLE "User" ADD COLUMN "maxStudents" INTEGER DEFAULT 0;
ALTER TABLE "User" ADD COLUMN "monthlyPoints" INTEGER DEFAULT 0;

-- Tan<PERSON>r-<PERSON><PERSON> kapcsolat tábla létrehozása
CREATE TABLE "TeacherStudent" (
    "id" TEXT NOT NULL,
    "teacherId" TEXT NOT NULL,
    "studentId" TEXT NOT NULL,
    "status" TEXT NOT NULL DEFAULT 'active',
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "TeacherStudent_pkey" PRIMARY KEY ("id")
);

-- Egyedi index a tanár-<PERSON><PERSON> kapcsolatra
CREATE UNIQUE INDEX "TeacherStudent_teacherId_studentId_key" ON "TeacherStudent"("teacherId", "studentId");

-- Külső kulcs a tanár-diák kapcsolathoz
ALTER TABLE "TeacherStudent" ADD CONSTRAINT "TeacherStudent_teacherId_fkey" FOREIGN KEY ("teacherId") REFERENCES "User"("id") ON DELETE CASCADE ON UPDATE CASCADE;
ALTER TABLE "TeacherStudent" ADD CONSTRAINT "TeacherStudent_studentId_fkey" FOREIGN KEY ("studentId") REFERENCES "User"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- Csoportos feladat tábla létrehozása
CREATE TABLE "GroupTask" (
    "id" TEXT NOT NULL,
    "title" TEXT NOT NULL,
    "description" TEXT,
    "teacherId" TEXT NOT NULL,
    "dueDate" TIMESTAMP(3),
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "GroupTask_pkey" PRIMARY KEY ("id")
);

-- Külső kulcs a csoportos feladathoz
ALTER TABLE "GroupTask" ADD CONSTRAINT "GroupTask_teacherId_fkey" FOREIGN KEY ("teacherId") REFERENCES "User"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- Diák feladat tábla létrehozása
CREATE TABLE "StudentTask" (
    "id" TEXT NOT NULL,
    "groupTaskId" TEXT NOT NULL,
    "studentId" TEXT NOT NULL,
    "status" TEXT NOT NULL DEFAULT 'assigned', -- assigned, in_progress, completed
    "completedAt" TIMESTAMP(3),
    "score" INTEGER,
    "feedback" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "StudentTask_pkey" PRIMARY KEY ("id")
);

-- Egyedi index a diák-feladat kapcsolatra
CREATE UNIQUE INDEX "StudentTask_groupTaskId_studentId_key" ON "StudentTask"("groupTaskId", "studentId");

-- Külső kulcsok a diák feladathoz
ALTER TABLE "StudentTask" ADD CONSTRAINT "StudentTask_groupTaskId_fkey" FOREIGN KEY ("groupTaskId") REFERENCES "GroupTask"("id") ON DELETE CASCADE ON UPDATE CASCADE;
ALTER TABLE "StudentTask" ADD CONSTRAINT "StudentTask_studentId_fkey" FOREIGN KEY ("studentId") REFERENCES "User"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- Pont átutalás tábla létrehozása
CREATE TABLE "PointTransfer" (
    "id" TEXT NOT NULL,
    "fromUserId" TEXT NOT NULL,
    "toUserId" TEXT NOT NULL,
    "amount" INTEGER NOT NULL,
    "description" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "PointTransfer_pkey" PRIMARY KEY ("id")
);

-- Külső kulcsok a pont átutaláshoz
ALTER TABLE "PointTransfer" ADD CONSTRAINT "PointTransfer_fromUserId_fkey" FOREIGN KEY ("fromUserId") REFERENCES "User"("id") ON DELETE CASCADE ON UPDATE CASCADE;
ALTER TABLE "PointTransfer" ADD CONSTRAINT "PointTransfer_toUserId_fkey" FOREIGN KEY ("toUserId") REFERENCES "User"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- Új típus hozzáadása a PointTransaction táblához
-- Megjegyzés: PostgreSQL-ben nem lehet módosítani a típus értékeit, ezért csak dokumentációs célból van itt
-- A kódban kell kezelni az új típusokat: "teacher_subscription", "teacher_transfer", "teacher_gift"
