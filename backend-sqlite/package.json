{"name": "backend-sqlite", "version": "1.0.0", "description": "Magyar-Német Nyelvtanuló Backend with SQLite", "main": "src/server.js", "scripts": {"start": "node src/server.js", "dev": "nodemon src/server.js", "test": "echo \"Error: no test specified\" && exit 1", "prisma:generate": "prisma generate", "prisma:migrate": "prisma migrate dev", "prisma:studio": "prisma studio"}, "keywords": [], "author": "", "license": "ISC", "type": "module", "dependencies": {"@prisma/client": "^5.10.2", "bcryptjs": "^2.4.3", "cors": "^2.8.5", "dotenv": "^16.5.0", "express": "^4.18.2", "jsonwebtoken": "^9.0.2", "nodemailer": "^6.10.1", "openai": "^4.95.1", "stripe": "^14.18.0"}, "devDependencies": {"nodemon": "^3.1.0", "prisma": "^5.10.2"}}