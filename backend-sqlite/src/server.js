import express from 'express';
import dotenv from 'dotenv';
import cors from 'cors';
import errorHandler from './middleware/error.js';
import authRoutes from './routes/authRoutes.js';
import subscriptionRoutes from './routes/subscriptionRoutes.js';
import invitationRoutes from './routes/invitationRoutes.js';
import pointRoutes from './routes/pointRoutes.js';
import supportRoutes from './routes/supportRoutes.js';
import adminRoutes from './routes/adminRoutes.js';
import teacherRoutes from './routes/teacherRoutes.js';

// Környezeti változók betöltése
dotenv.config();

// Express alkalmazás
const app = express();

// Body parser
app.use(express.json());

// CORS
app.use((req, res, next) => {
  const allowedOrigins = [process.env.FRONTEND_URL, 'http://localhost:8081', 'http://localhost:8082'];
  const origin = req.headers.origin;

  if (allowedOrigins.includes(origin)) {
    res.header('Access-Control-Allow-Origin', origin);
  }

  res.header('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
  res.header('Access-Control-Allow-Headers', 'Origin, X-Requested-With, Content-Type, Accept, Authorization');
  res.header('Access-Control-Allow-Credentials', 'true');

  // Handle preflight requests
  if (req.method === 'OPTIONS') {
    res.sendStatus(200);
  } else {
    next();
  }
});

// Útvonalak
app.use('/api/auth', authRoutes);
app.use('/api/subscriptions', subscriptionRoutes);
app.use('/api/invitations', invitationRoutes);
app.use('/api/points', pointRoutes);
app.use('/api/support', supportRoutes);
app.use('/api/admin', adminRoutes);
app.use('/api/teacher', teacherRoutes);

// Webhook útvonalak külön kezelése - ezt a route előtt kell definiálni
app.post('/api/subscriptions/webhook', express.raw({ type: 'application/json' }), (req, res, next) => {
  try {
    req.rawBody = req.body;
    if (req.body.toString()) {
      req.body = JSON.parse(req.body.toString());
    }
    next();
  } catch (error) {
    console.error('Webhook parsing error:', error);
    res.status(400).json({ success: false, message: 'Webhook parsing error' });
  }
});

app.post('/api/points/webhook', express.raw({ type: 'application/json' }), (req, res, next) => {
  try {
    req.rawBody = req.body;
    if (req.body.toString()) {
      req.body = JSON.parse(req.body.toString());
    }
    next();
  } catch (error) {
    console.error('Points webhook parsing error:', error);
    res.status(400).json({ success: false, message: 'Webhook parsing error' });
  }
});

// Alapértelmezett útvonal
app.get('/', (req, res) => {
  res.json({ message: 'Magyar-Német Nyelvtanuló API SQLite-tal' });
});

// Debug útvonal a 403 hiba teszteléséhez
app.all('/test', (req, res) => {
  console.log('Test útvonal elérve:', req.method, req.url, req.body);
  res.status(200).json({ success: true, message: 'Test sikeres', method: req.method });
});

// Hibakezelő
app.use(errorHandler);

// Port
const PORT = process.env.PORT || 3000;

// Szerver indítása
const server = app.listen(PORT, () => {
  console.log(`Server running on port ${PORT}`);
});

// Nem kezelt promise elutasítások kezelése
process.on('unhandledRejection', (err, promise) => {
  console.log(`Error: ${err.message}`);
  // Szerver leállítása
  server.close(() => process.exit(1));
});
