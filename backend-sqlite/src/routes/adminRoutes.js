import express from 'express';
import { 
  getAllUsers, 
  getUserDetails, 
  sendPasswordReset, 
  adjustPoints,
  getStats
} from '../controllers/adminController.js';
import { protect } from '../middleware/auth.js';

const router = express.Router();

// Minden útvonal védett, csak bejelentkezett felhasználók számára elérhető
router.use(protect);

// Felhasználók kezelése
router.get('/users', getAllUsers);
router.get('/users/:id', getUserDetails);
router.post('/users/:id/reset-password', sendPasswordReset);
router.post('/users/:id/points', adjustPoints);

// Statisztikák
router.get('/stats', getStats);

export default router;
