import express from 'express';
import {
  getUserPoints,
  usePoints,
  createPointPurchase,
  verifyPointPurchase,
  handlePointsWebhook,
  getPointInvoices
} from '../controllers/pointController.js';
import { protect, checkPoints } from '../middleware/auth.js';

const router = express.Router();

// Nyilvános útvonalak
router.post('/webhook', express.raw({ type: 'application/json' }), handlePointsWebhook);

// Védett útvonalak
router.get('/', protect, getUserPoints);
router.post('/use', protect, checkPoints, usePoints);
router.post('/purchase', protect, createPointPurchase);
router.get('/verify-purchase/:sessionId', protect, verifyPointPurchase);
router.get('/invoices', protect, getPointInvoices);

export default router;
