import express from 'express';
import {
  createTicket,
  getUserTickets,
  getAllTickets,
  getTicketDetails,
  addMessageToTicket,
  updateTicketStatus,
  getAISuggestion
} from '../controllers/supportController.js';
import { protect, isAdmin } from '../middleware/auth.js';

const router = express.Router();

// Felhasználói végpontok
router.post('/tickets', protect, createTicket);
router.get('/tickets', protect, getUserTickets);
router.get('/tickets/:id', protect, getTicketDetails);
router.post('/tickets/:id/messages', protect, addMessageToTicket);

// Admin végpontok
router.get('/admin/tickets', protect, isAdmin, getAllTickets);
router.patch('/admin/tickets/:id', protect, isAdmin, updateTicketStatus);
router.post('/admin/tickets/:id/ai-suggestion', protect, isAdmin, getAISuggestion);

export default router;
