import express from 'express';
import {
  createInvitation,
  verifyInvitation,
  acceptInvitation,
  getInvitations,
  deleteInvitation,
  resendInvitation
} from '../controllers/invitationController.js';
import { protect } from '../middleware/auth.js';

const router = express.Router();

// Nyilvános útvonalak
router.get('/verify/:token', verifyInvitation);
router.post('/accept/:token', acceptInvitation);

// Védett útvonalak
router.post('/', protect, createInvitation);
router.get('/', protect, getInvitations);
router.delete('/:id', protect, deleteInvitation);
router.post('/resend/:id', protect, resendInvitation);

export default router;
