import express from 'express';
import {
  createTeacherSubscription,
  verifyTeacherSubscription,
  activateFreeTrialPackage,
  inviteStudent,
  getStudents
} from '../controllers/teacherController.js';
import { protect } from '../middleware/auth.js';

const router = express.Router();

// Minden útvonal védett, csak bejelentkezett felhasználók számára elérhető
router.use(protect);

// Előfizetés kezelése
router.post('/subscribe', createTeacherSubscription);
router.get('/verify-subscription/:sessionId', verifyTeacherSubscription);
router.post('/activate-free-trial', activateFreeTrialPackage);

// Diákok kezelése
router.post('/invite-student', inviteStudent);
router.get('/students', getStudents);

export default router;
