import express from 'express';
import {
  createSubscription,
  getSubscriptionStatus,
  cancelSubscription,
  reactivateSubscription,
  handleWebhook,
  verifySession,
  getInvoices,
  getInvoice,
  addSubscriptionPoints
} from '../controllers/subscriptionController.js';
import { protect } from '../middleware/auth.js';

const router = express.Router();

// Webhook útvonal - nincs védve
router.post('/webhook', handleWebhook);

// Védett útvonalak
router.post('/', protect, createSubscription);
router.get('/status', protect, getSubscriptionStatus);
router.get('/verify-session/:sessionId', protect, verifySession);
router.post('/cancel', protect, cancelSubscription);
router.post('/reactivate', protect, reactivateSubscription);
router.get('/invoices', protect, getInvoices);
router.get('/invoices/:invoiceId', protect, getInvoice);
router.post('/add-points', protect, addSubscriptionPoints);

export default router;
