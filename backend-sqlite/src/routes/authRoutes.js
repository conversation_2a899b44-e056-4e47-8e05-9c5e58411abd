import express from 'express';
import { register, login, getMe, logout, forgotPassword, resetPassword } from '../controllers/authController.js';
import { protect } from '../middleware/auth.js';

const router = express.Router();

// Regisztr<PERSON><PERSON><PERSON> és bejelentkezés
router.post('/register', register);
router.post('/login', login);
router.get('/logout', logout);

// <PERSON><PERSON><PERSON><PERSON> v<PERSON>llítás
router.post('/forgot-password', forgotPassword);
router.post('/reset-password/:resetToken', resetPassword);

// Védett útvonalak
router.get('/me', protect, getMe);

export default router;
