const errorHandler = (err, req, res, next) => {
  let error = { ...err };
  error.message = err.message;

  // Log to console for dev
  console.error(err);

  // Prisma errors
  if (err.code === 'P2002') {
    const message = '<PERSON><PERSON><PERSON> létezik ilyen értékkel rendelkező bejegyzés';
    error = { message, statusCode: 400 };
  }

  if (err.code === 'P2025') {
    const message = 'Erőforrás nem található';
    error = { message, statusCode: 404 };
  }

  res.status(error.statusCode || 500).json({
    success: false,
    message: error.message || 'Szerver hiba történt'
  });
};

export default errorHandler;
