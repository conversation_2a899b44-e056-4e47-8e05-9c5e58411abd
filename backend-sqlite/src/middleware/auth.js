import jwt from 'jsonwebtoken';
import prisma from '../utils/db.js';

// V<PERSON>dett útvonalak
export const protect = async (req, res, next) => {
  let token;

  // <PERSON><PERSON>rizzük, hogy a token létezik-e a header-ben
  if (
    req.headers.authorization &&
    req.headers.authorization.startsWith('Bearer')
  ) {
    // Token kinyerése a header-ből
    token = req.headers.authorization.split(' ')[1];
  } else if (req.cookies && req.cookies.token) {
    // Token kinyerése a cookie-ból
    token = req.cookies.token;
  }

  // Ellenőrizzük, hogy a token létezik-e
  if (!token) {
    return res.status(401).json({
      success: false,
      message: 'Nincs hozzáférési jogosultsága ehhez az erőforráshoz'
    });
  }

  try {
    // Token dekódolása
    const decoded = jwt.verify(token, process.env.JWT_SECRET);

    // Felhasz<PERSON><PERSON><PERSON>
    const user = await prisma.user.findUnique({
      where: { id: decoded.id }
    });

    if (!user) {
      return res.status(401).json({
        success: false,
        message: 'A felhasználó nem található'
      });
    }

    // Felhasználó hozzáadása a kéréshez
    req.user = user;
    next();
  } catch (err) {
    return res.status(401).json({
      success: false,
      message: 'Nincs hozzáférési jogosultsága ehhez az erőforráshoz'
    });
  }
};

// Előfizetés ellenőrzése
export const checkSubscription = async (req, res, next) => {
  try {
    // Felhasználó előfizetésének ellenőrzése
    const subscription = await prisma.subscription.findFirst({
      where: {
        userId: req.user.id,
        status: 'active',
        stripeCurrentPeriodEnd: {
          gt: new Date()
        }
      }
    });

    if (!subscription) {
      return res.status(403).json({
        success: false,
        message: 'Ehhez a funkcióhoz aktív előfizetés szükséges'
      });
    }

    next();
  } catch (err) {
    return res.status(500).json({
      success: false,
      message: 'Hiba történt az előfizetés ellenőrzésekor'
    });
  }
};

// Pontok ellenőrzése
export const checkPoints = async (req, res, next) => {
  try {
    const pointsNeeded = req.body.amount || 1; // Alapértelmezetten 1 pont szükséges

    // Felhasználó pontjainak ellenőrzése
    const user = await prisma.user.findUnique({
      where: { id: req.user.id },
      select: { points: true }
    });

    if (user.points < pointsNeeded) {
      return res.status(403).json({
        success: false,
        message: 'Nincs elegendő pontod a művelet végrehajtásához',
        data: {
          currentPoints: user.points,
          requiredPoints: pointsNeeded
        }
      });
    }

    next();
  } catch (err) {
    return res.status(500).json({
      success: false,
      message: 'Hiba történt a pontok ellenőrzésekor'
    });
  }
};

// Admin jogosultság ellenőrzése
export const isAdmin = async (req, res, next) => {
  try {
    // Ellenőrizzük, hogy a felhasználó a megadott ID-val rendelkezik-e
    if (req.user.id !== '11383db6-ab6e-4810-81a9-dc5ac1426d3a') {
      return res.status(403).json({
        success: false,
        message: 'Nincs jogosultságod ehhez a művelethez'
      });
    }

    console.log('Admin jogosultság érvényesítve a middleware-ben');
    next();
  } catch (err) {
    return res.status(500).json({
      success: false,
      message: 'Hiba történt a jogosultság ellenőrzésekor'
    });
  }
};
