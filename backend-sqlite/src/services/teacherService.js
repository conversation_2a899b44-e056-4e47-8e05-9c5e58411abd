import prisma from '../utils/db.js';
import * as stripeUtils from '../utils/stripe.js';
import Stripe from 'stripe';

const stripe = new Stripe(process.env.STRIPE_SECRET_KEY);

// Tanári csomag adatai
export const TEACHER_PACKAGE = {
  productId: 'prod_SI7DHRY229d8SP',
  name: '<PERSON><PERSON><PERSON> csomag',
  price: 7500,
  monthlyPoints: 10000,
  maxStudents: 20,
  studentGiftPoints: 500
};

// Ingyenes próba csomag adatai
export const FREE_TRIAL_PACKAGE = {
  productId: 'prod_SI7COuGhfmC6H9',
  name: 'Ingyenes próba csomag',
  price: 0,
  points: 15
};

/**
 * <PERSON><PERSON><PERSON> előfizetés létrehozása
 * @param {string} userId - Felhasználó azonosítója
 * @param {string} stripeCustomerId - Stripe ügyfél azonosítója
 * @returns {Promise<object>} - Checkout session
 */
export const createTeacherSubscription = async (userId, stripeCustomerId) => {
  try {
    // Ellenőrizzük, hogy a felhasználó már tanár-e
    const user = await prisma.user.findUnique({
      where: { id: userId }
    });

    // Ellenőrizzük, hogy a user objektumnak van-e isTeacher tulajdonsága
    const isTeacher = user && 'isTeacher' in user ? user.isTeacher : false;

    if (isTeacher) {
      throw new Error('A felhasználó már rendelkezik tanári előfizetéssel');
    }

    // Lekérjük a tanári csomag árát a Stripe-tól
    const prices = await stripe.prices.list({
      product: TEACHER_PACKAGE.productId,
      active: true,
      limit: 1
    });

    if (prices.data.length === 0) {
      // Ha nincs ár, létrehozunk egyet
      console.log('Nincs ár a tanári csomaghoz, létrehozunk egyet...');

      // Létrehozzuk az árat EUR-ban, hogy egyezzen a meglévő előfizetéssel
      const price = await stripe.prices.create({
        product: TEACHER_PACKAGE.productId,
        unit_amount: TEACHER_PACKAGE.price * 100,
        currency: 'eur', // EUR pénznem használata
        recurring: {
          interval: 'month'
        }
      });

      console.log('Új ár létrehozva:', price.id);
      var priceId = price.id;
    } else {
      var priceId = prices.data[0].id;

      // Ellenőrizzük a pénznemet
      const price = prices.data[0];
      if (price.currency !== 'eur') {
        console.log('A meglévő ár pénzneme nem EUR, létrehozunk egy újat...');

        // Inaktiváljuk a régi árat
        await stripe.prices.update(price.id, { active: false });

        // Létrehozzuk az árat EUR-ban
        const newPrice = await stripe.prices.create({
          product: TEACHER_PACKAGE.productId,
          unit_amount: TEACHER_PACKAGE.price * 100,
          currency: 'eur', // EUR pénznem használata
          recurring: {
            interval: 'month'
          }
        });

        console.log('Új ár létrehozva:', newPrice.id);
        priceId = newPrice.id;
      }
    }

    // Ellenőrizzük, hogy a felhasználónak van-e már előfizetése
    try {
      // Lekérjük a felhasználó Stripe előfizetéseit
      const subscriptions = await stripe.subscriptions.list({
        customer: stripeCustomerId,
        status: 'active',
        limit: 1
      });

      // Ha van aktív előfizetés, ellenőrizzük a pénznemet
      if (subscriptions.data.length > 0) {
        const existingSubscription = subscriptions.data[0];
        const existingPrice = existingSubscription.items.data[0].price;

        console.log('Meglévő előfizetés pénzneme:', existingPrice.currency);

        // Ha a meglévő előfizetés pénzneme nem EUR, akkor frissítsük a priceId-t
        if (existingPrice.currency !== 'eur') {
          // Inaktiváljuk a régi árat
          await stripe.prices.update(priceId, { active: false });

          // Létrehozzuk az árat a meglévő előfizetés pénznemében
          const newPrice = await stripe.prices.create({
            product: TEACHER_PACKAGE.productId,
            unit_amount: TEACHER_PACKAGE.price * 100,
            currency: existingPrice.currency,
            recurring: {
              interval: 'month'
            }
          });

          console.log('Új ár létrehozva a meglévő előfizetés pénznemében:', newPrice.id);
          priceId = newPrice.id;
        }
      }
    } catch (error) {
      console.error('Hiba a felhasználó előfizetéseinek lekérésekor:', error);
      // Folytatjuk a végrehajtást, mert ez nem kritikus hiba
    }

    // Checkout session létrehozása
    const session = await stripe.checkout.sessions.create({
      payment_method_types: ['card'],
      customer: stripeCustomerId,
      line_items: [
        {
          price: priceId,
          quantity: 1
        }
      ],
      mode: 'subscription',
      success_url: `${process.env.FRONTEND_URL}/teacher/success?session_id={CHECKOUT_SESSION_ID}`,
      cancel_url: `${process.env.FRONTEND_URL}/teacher/cancel`,
      metadata: {
        userId,
        packageType: 'teacher'
      }
    });

    return session;
  } catch (error) {
    console.error('Hiba a tanári előfizetés létrehozásakor:', error);
    throw error;
  }
};

/**
 * Ingyenes próba csomag aktiválása
 * @param {string} userId - Felhasználó azonosítója
 * @returns {Promise<object>} - Frissített felhasználó
 */
export const activateFreeTrialPackage = async (userId) => {
  try {
    // Ellenőrizzük, hogy a felhasználó már aktiválta-e az ingyenes csomagot
    const existingTransaction = await prisma.pointTransaction.findFirst({
      where: {
        userId,
        type: 'free_trial'
      }
    });

    if (existingTransaction) {
      throw new Error('Az ingyenes próba csomag már aktiválva lett');
    }

    // Lekérjük a felhasználót
    const user = await prisma.user.findUnique({
      where: { id: userId },
      select: { points: true }
    });

    // Ellenőrizzük, hogy a felhasználónak már van-e 15 pontja (alapértelmezett)
    // Ha nincs, akkor hozzáadjuk a pontokat
    let pointsToAdd = 0;
    if (user.points < FREE_TRIAL_PACKAGE.points) {
      pointsToAdd = FREE_TRIAL_PACKAGE.points - user.points;
    }

    // Tranzakció létrehozása
    const result = await prisma.$transaction([
      // Ha szükséges, pontok hozzáadása a felhasználóhoz
      ...(pointsToAdd > 0 ? [
        prisma.user.update({
          where: { id: userId },
          data: { points: { increment: pointsToAdd } }
        })
      ] : [
        prisma.user.findUnique({
          where: { id: userId }
        })
      ]),
      // Tranzakció rögzítése
      prisma.pointTransaction.create({
        data: {
          userId,
          amount: FREE_TRIAL_PACKAGE.points,
          type: 'free_trial',
          description: 'Ingyenes próba csomag aktiválása'
        }
      })
    ]);

    return result[0];
  } catch (error) {
    console.error('Hiba az ingyenes próba csomag aktiválásakor:', error);
    throw error;
  }
};

/**
 * Tanári előfizetés aktiválása
 * @param {string} userId - Felhasználó azonosítója
 * @param {string} subscriptionId - Stripe előfizetés azonosítója
 * @returns {Promise<object>} - Frissített felhasználó
 */
export const activateTeacherSubscription = async (userId, subscriptionId) => {
  try {
    // Lekérjük az előfizetést a Stripe-tól
    const subscription = await stripe.subscriptions.retrieve(subscriptionId);

    // Ellenőrizzük, hogy az előfizetés aktív-e
    if (subscription.status !== 'active' && subscription.status !== 'trialing') {
      throw new Error('Az előfizetés nem aktív');
    }

    // Tranzakció létrehozása a pontok biztonságos kezeléséhez
    const result = await prisma.$transaction([
      // Felhasználó frissítése
      prisma.user.update({
        where: { id: userId },
        data: {
          isTeacher: true,
          teacherSubscriptionId: subscriptionId,
          maxStudents: TEACHER_PACKAGE.maxStudents,
          monthlyPoints: TEACHER_PACKAGE.monthlyPoints,
          points: { increment: TEACHER_PACKAGE.monthlyPoints }
        }
      }),
      // Tranzakció rögzítése
      prisma.pointTransaction.create({
        data: {
          userId,
          amount: TEACHER_PACKAGE.monthlyPoints,
          type: 'teacher_subscription',
          description: 'Tanári előfizetés aktiválása'
        }
      })
    ]);

    return result[0];
  } catch (error) {
    console.error('Hiba a tanári előfizetés aktiválásakor:', error);
    throw error;
  }
};

/**
 * Diák meghívása
 * @param {string} teacherId - Tanár azonosítója
 * @param {string} email - Diák email címe
 * @returns {Promise<object>} - Meghívó
 */
export const inviteStudent = async (teacherId, email) => {
  try {
    // Ellenőrizzük, hogy a tanár rendelkezik-e tanári előfizetéssel
    const teacher = await prisma.user.findUnique({
      where: { id: teacherId }
    });

    // Ellenőrizzük, hogy a teacher objektumnak van-e isTeacher tulajdonsága
    const isTeacher = teacher && 'isTeacher' in teacher ? teacher.isTeacher : false;
    const maxStudents = teacher && 'maxStudents' in teacher ? teacher.maxStudents : 0;

    if (!isTeacher) {
      throw new Error('Csak tanárok hívhatnak meg diákokat');
    }

    // Ellenőrizzük, hogy a tanár nem érte-e el a maximális diákszámot
    const currentStudentCount = await prisma.teacherStudent.count({
      where: {
        teacherId,
        status: 'active'
      }
    });

    if (currentStudentCount >= maxStudents) {
      throw new Error(`Elérted a maximális diákszámot (${maxStudents})`);
    }

    // Meghívó létrehozása
    const invitation = await prisma.invitation.create({
      data: {
        email,
        token: Math.random().toString(36).substring(2, 15) + Math.random().toString(36).substring(2, 15),
        inviterId: teacherId,
        expiresAt: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000) // 7 nap
      }
    });

    return invitation;
  } catch (error) {
    console.error('Hiba a diák meghívása során:', error);
    throw error;
  }
};

export default {
  TEACHER_PACKAGE,
  FREE_TRIAL_PACKAGE,
  createTeacherSubscription,
  activateFreeTrialPackage,
  activateTeacherSubscription,
  inviteStudent
};
