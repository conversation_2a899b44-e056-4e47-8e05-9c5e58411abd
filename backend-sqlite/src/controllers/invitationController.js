import crypto from 'crypto';
import prisma from '../utils/db.js';
import { sendEmail, getInvitationEmailTemplate } from '../utils/emailService.js';

// Token generálása
const generateToken = () => {
  return crypto.randomBytes(32).toString('hex');
};

// @desc    Meghívó létrehozása
// @route   POST /api/invitations
// @access  Private
export const createInvitation = async (req, res, next) => {
  try {
    const { email } = req.body;
    const inviter = req.user;

    // Alapvető beviteli ellenőrzés
    if (!email) {
      return res.status(400).json({
        success: false,
        message: '<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, adja meg az email címet'
      });
    }

    // <PERSON>ü<PERSON>, hogy a meghívott email már regisztrált-e
    const existingUser = await prisma.user.findUnique({
      where: { email }
    });

    if (existingUser) {
      return res.status(400).json({
        success: false,
        message: 'Ez az email cím már regisztr<PERSON>l<PERSON>'
      });
    }

    // <PERSON>, hogy van-e már aktív meghívó erre az email címre
    const existingInvitation = await prisma.invitation.findFirst({
      where: {
        email,
        status: 'pending',
        expiresAt: {
          gt: new Date()
        }
      }
    });

    if (existingInvitation) {
      return res.status(400).json({
        success: false,
        message: 'Erre az email címre már küldtünk meghívót'
      });
    }

    // Token generálása
    const token = generateToken();

    // Meghívó létrehozása
    const invitation = await prisma.invitation.create({
      data: {
        email,
        token,
        inviterId: inviter.id,
        expiresAt: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000) // 7 nap
      }
    });

    // Meghívó link generálása
    const invitationLink = `${process.env.FRONTEND_URL}/register?token=${token}`;

    // Email küldése
    await sendEmail({
      to: email,
      subject: 'Meghívó a Magyar-Német Nyelvtanuló alkalmazásba',
      html: getInvitationEmailTemplate(inviter.name, invitationLink)
    });

    // Válasz küldése
    res.status(201).json({
      success: true,
      data: {
        id: invitation.id,
        email: invitation.email,
        status: invitation.status,
        expiresAt: invitation.expiresAt
      },
      message: 'Meghívó sikeresen elküldve'
    });
  } catch (err) {
    console.error('Hiba a meghívó létrehozásakor:', err);
    next(err);
  }
};

// @desc    Meghívó ellenőrzése
// @route   GET /api/invitations/verify/:token
// @access  Public
export const verifyInvitation = async (req, res, next) => {
  try {
    const { token } = req.params;

    // Meghívó keresése
    const invitation = await prisma.invitation.findUnique({
      where: { token },
      include: {
        inviter: {
          select: {
            id: true,
            name: true,
            email: true
          }
        }
      }
    });

    // Ellenőrizzük, hogy létezik-e a meghívó
    if (!invitation) {
      return res.status(404).json({
        success: false,
        message: 'Érvénytelen meghívó'
      });
    }

    // Ellenőrizzük, hogy a meghívó nem járt-e le
    if (invitation.expiresAt < new Date()) {
      return res.status(400).json({
        success: false,
        message: 'A meghívó lejárt'
      });
    }

    // Ellenőrizzük, hogy a meghívó nincs-e már elfogadva
    if (invitation.status !== 'pending') {
      return res.status(400).json({
        success: false,
        message: 'A meghívó már fel lett használva'
      });
    }

    // Válasz küldése
    res.status(200).json({
      success: true,
      data: {
        email: invitation.email,
        inviter: invitation.inviter.name
      }
    });
  } catch (err) {
    console.error('Hiba a meghívó ellenőrzésekor:', err);
    next(err);
  }
};

// @desc    Meghívó elfogadása regisztrációkor
// @route   POST /api/invitations/accept/:token
// @access  Public
export const acceptInvitation = async (req, res, next) => {
  try {
    const { token } = req.params;
    const { userId } = req.body;

    // Meghívó keresése
    const invitation = await prisma.invitation.findUnique({
      where: { token }
    });

    // Ellenőrizzük, hogy létezik-e a meghívó
    if (!invitation) {
      return res.status(404).json({
        success: false,
        message: 'Érvénytelen meghívó'
      });
    }

    // Ellenőrizzük, hogy a meghívó nem járt-e le
    if (invitation.expiresAt < new Date()) {
      return res.status(400).json({
        success: false,
        message: 'A meghívó lejárt'
      });
    }

    // Ellenőrizzük, hogy a meghívó nincs-e már elfogadva
    if (invitation.status !== 'pending') {
      return res.status(400).json({
        success: false,
        message: 'A meghívó már fel lett használva'
      });
    }

    // Meghívó frissítése
    await prisma.invitation.update({
      where: { id: invitation.id },
      data: {
        status: 'accepted',
        inviteeId: userId
      }
    });

    // Válasz küldése
    res.status(200).json({
      success: true,
      message: 'Meghívó sikeresen elfogadva'
    });
  } catch (err) {
    console.error('Hiba a meghívó elfogadásakor:', err);
    next(err);
  }
};

// @desc    Meghívók listázása
// @route   GET /api/invitations
// @access  Private
export const getInvitations = async (req, res, next) => {
  try {
    const invitations = await prisma.invitation.findMany({
      where: {
        inviterId: req.user.id
      },
      include: {
        invitee: {
          select: {
            id: true,
            name: true,
            email: true
          }
        }
      },
      orderBy: {
        createdAt: 'desc'
      }
    });

    // Válasz küldése
    res.status(200).json({
      success: true,
      count: invitations.length,
      data: invitations
    });
  } catch (err) {
    console.error('Hiba a meghívók lekérésekor:', err);
    next(err);
  }
};

// @desc    Meghívó törlése
// @route   DELETE /api/invitations/:id
// @access  Private
export const deleteInvitation = async (req, res, next) => {
  try {
    const { id } = req.params;

    // Meghívó keresése
    const invitation = await prisma.invitation.findUnique({
      where: { id }
    });

    // Ellenőrizzük, hogy létezik-e a meghívó
    if (!invitation) {
      return res.status(404).json({
        success: false,
        message: 'Meghívó nem található'
      });
    }

    // Ellenőrizzük, hogy a felhasználó a meghívó tulajdonosa-e
    if (invitation.inviterId !== req.user.id) {
      return res.status(403).json({
        success: false,
        message: 'Nincs jogosultsága a meghívó törléséhez'
      });
    }

    // Meghívó törlése
    await prisma.invitation.delete({
      where: { id }
    });

    // Válasz küldése
    res.status(200).json({
      success: true,
      message: 'Meghívó sikeresen törölve'
    });
  } catch (err) {
    console.error('Hiba a meghívó törlésekor:', err);
    next(err);
  }
};

// @desc    Meghívó újraküldése
// @route   POST /api/invitations/resend/:id
// @access  Private
export const resendInvitation = async (req, res, next) => {
  try {
    const { id } = req.params;
    const inviter = req.user;

    // Meghívó keresése
    const invitation = await prisma.invitation.findUnique({
      where: { id }
    });

    // Ellenőrizzük, hogy létezik-e a meghívó
    if (!invitation) {
      return res.status(404).json({
        success: false,
        message: 'Meghívó nem található'
      });
    }

    // Ellenőrizzük, hogy a felhasználó a meghívó tulajdonosa-e
    if (invitation.inviterId !== inviter.id) {
      return res.status(403).json({
        success: false,
        message: 'Nincs jogosultsága a meghívó újraküldéséhez'
      });
    }

    // Ellenőrizzük, hogy a meghívó nincs-e már elfogadva
    if (invitation.status === 'accepted') {
      return res.status(400).json({
        success: false,
        message: 'A meghívó már el lett fogadva'
      });
    }

    // Új token generálása
    const token = generateToken();

    // Meghívó frissítése
    const updatedInvitation = await prisma.invitation.update({
      where: { id },
      data: {
        token,
        status: 'pending',
        expiresAt: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000) // 7 nap
      }
    });

    // Meghívó link generálása
    const invitationLink = `${process.env.FRONTEND_URL}/register?token=${token}`;

    // Email küldése
    await sendEmail({
      to: invitation.email,
      subject: 'Meghívó a Magyar-Német Nyelvtanuló alkalmazásba',
      html: getInvitationEmailTemplate(inviter.name, invitationLink)
    });

    // Válasz küldése
    res.status(200).json({
      success: true,
      data: {
        id: updatedInvitation.id,
        email: updatedInvitation.email,
        status: updatedInvitation.status,
        expiresAt: updatedInvitation.expiresAt
      },
      message: 'Meghívó sikeresen újraküldve'
    });
  } catch (err) {
    console.error('Hiba a meghívó újraküldésekor:', err);
    next(err);
  }
};
