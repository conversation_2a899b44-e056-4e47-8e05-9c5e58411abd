import prisma from '../utils/db.js';
import * as stripeUtils from '../utils/stripe.js';
import Stripe from 'stripe';

const stripe = new Stripe(process.env.STRIPE_SECRET_KEY);

// Pontcsomagok árazása Stripe termék azonosítókkal
const POINT_PACKAGES = {
  'prod_SA9WopBkHjC4iO': { points: 4000, price: 5000, name: '4000 pont' },
  'prod_SA9XQfqwr7PohC': { points: 9000, price: 10000, name: '9000 pont' },
  'prod_SA9Xpdu6nzCR6y': { points: 14000, price: 15000, name: '14000 pont' },
  'prod_SA9YwZAI9rOOC6': { points: 18000, price: 20000, name: '18000 pont' },
  'prod_SA9ZDpDtKAguo0': { points: 22000, price: 25000, name: '22000 pont' },
  'prod_SA9Zlo40dGcWFh': { points: 25000, price: 30000, name: '25000 pont' },
};

// @desc    Felhasználó pontjainak lekérdezése
// @route   GET /api/points
// @access  Private
export const getUserPoints = async (req, res, next) => {
  try {
    const user = req.user;

    // Felhasználó pontjainak lekérdezése
    const userData = await prisma.user.findUnique({
      where: { id: user.id },
      select: { points: true }
    });

    // Ponttranzakciók lekérdezése
    const transactions = await prisma.pointTransaction.findMany({
      where: { userId: user.id },
      orderBy: { createdAt: 'desc' },
      take: 10 // Csak az utolsó 10 tranzakciót kérjük le
    });

    res.status(200).json({
      success: true,
      data: {
        points: userData.points,
        transactions
      }
    });
  } catch (err) {
    next(err);
  }
};

// @desc    Pont felhasználása (API hívás)
// @route   POST /api/points/use
// @access  Private
export const usePoints = async (req, res, next) => {
  try {
    const user = req.user;
    const { amount = 1, description = 'API hívás' } = req.body;

    // Ellenőrizzük, hogy a felhasználónak van-e elegendő pontja
    const userData = await prisma.user.findUnique({
      where: { id: user.id },
      select: { points: true }
    });

    if (userData.points < amount) {
      return res.status(400).json({
        success: false,
        message: 'Nincs elegendő pontod a művelet végrehajtásához'
      });
    }

    // Tranzakció létrehozása a pontok biztonságos kezeléséhez
    const result = await prisma.$transaction([
      // Pontok levonása a felhasználótól
      prisma.user.update({
        where: { id: user.id },
        data: { points: { decrement: amount } }
      }),
      // Tranzakció rögzítése
      prisma.pointTransaction.create({
        data: {
          userId: user.id,
          amount: -amount, // Negatív érték, mert felhasználás
          type: 'usage',
          description
        }
      })
    ]);

    res.status(200).json({
      success: true,
      data: {
        remainingPoints: result[0].points,
        transaction: result[1]
      }
    });
  } catch (err) {
    next(err);
  }
};

// @desc    Pontcsomag vásárlása (Stripe checkout session létrehozása)
// @route   POST /api/points/purchase
// @access  Private
export const createPointPurchase = async (req, res, next) => {
  try {
    const user = req.user;
    const { packageId } = req.body;

    // Ellenőrizzük, hogy létezik-e a megadott csomag
    if (!POINT_PACKAGES[packageId]) {
      return res.status(400).json({
        success: false,
        message: 'Érvénytelen pontcsomag azonosító'
      });
    }

    const pointPackage = POINT_PACKAGES[packageId];

    // Lekérjük a termék árait a Stripe-tól
    const prices = await stripe.prices.list({
      product: packageId,
      active: true,
      limit: 1
    });

    if (prices.data.length === 0) {
      return res.status(400).json({
        success: false,
        message: 'Nem található aktív ár ehhez a termékhez'
      });
    }

    const priceId = prices.data[0].id;

    // Checkout session létrehozása
    const session = await stripe.checkout.sessions.create({
      payment_method_types: ['card'],
      customer: user.stripeCustomerId,
      line_items: [
        {
          price: priceId,
          quantity: 1
        }
      ],
      mode: 'payment',
      success_url: `${process.env.FRONTEND_URL}/points/success?session_id={CHECKOUT_SESSION_ID}`,
      cancel_url: `${process.env.FRONTEND_URL}/points/cancel`,
      metadata: {
        userId: user.id,
        packageId,
        points: pointPackage.points
      }
    });

    res.status(200).json({
      success: true,
      sessionId: session.id,
      url: session.url
    });
  } catch (err) {
    console.error('Hiba a pontcsomag vásárlásánál:', err);
    next(err);
  }
};

// @desc    Pontcsomag vásárlás ellenőrzése
// @route   GET /api/points/verify-purchase/:sessionId
// @access  Private
export const verifyPointPurchase = async (req, res, next) => {
  try {
    const { sessionId } = req.params;
    const user = req.user;

    if (!sessionId) {
      return res.status(400).json({
        success: false,
        message: 'Session azonosító megadása kötelező'
      });
    }

    // Session lekérése a Stripe-tól
    const session = await stripe.checkout.sessions.retrieve(sessionId, {
      expand: ['payment_intent']
    });

    // Ellenőrizzük, hogy a session a felhasználóhoz tartozik-e
    // Teszt módban ez az ellenőrzés kikapcsolva, mert a teszt session-ök
    // nem mindig rendelkeznek a megfelelő customer ID-val
    // if (session.customer !== user.stripeCustomerId) {
    //   return res.status(403).json({
    //     success: false,
    //     message: 'Nincs jogosultságod ehhez a session-höz'
    //   });
    // }

    console.log('Session ellenőrzése:', {
      sessionId,
      userId: user.id,
      sessionCustomer: session.customer,
      userStripeCustomerId: user.stripeCustomerId,
      paymentStatus: session.payment_status,
      paymentIntent: session.payment_intent,
      paymentIntentStatus: session.payment_intent ? session.payment_intent.status : null,
      metadata: session.metadata
    });

    // Ellenőrizzük, hogy a fizetés sikeres volt-e
    // Teszt módban a payment_status lehet, hogy nem frissül azonnal 'paid'-re,
    // ezért a payment_intent.status-t is ellenőrizzük
    const isPaymentSuccessful = session.payment_status === 'paid' ||
                               (session.payment_intent && session.payment_intent.status === 'succeeded');

    if (!isPaymentSuccessful) {
      return res.status(400).json({
        success: false,
        message: 'A fizetés még nem teljesült'
      });
    }

    // Ellenőrizzük, hogy a pontok már hozzá lettek-e adva
    // A payment_intent lehet string vagy objektum, ezért kezeljük mindkettőt
    const paymentIntentId = typeof session.payment_intent === 'string'
                          ? session.payment_intent
                          : (session.payment_intent ? session.payment_intent.id : null);

    console.log('Payment Intent ID:', paymentIntentId);

    if (!paymentIntentId) {
      return res.status(400).json({
        success: false,
        message: 'Hiányzó payment intent azonosító'
      });
    }

    const existingTransaction = await prisma.pointTransaction.findFirst({
      where: {
        stripePaymentIntentId: paymentIntentId,
        userId: user.id,
        type: 'purchase'
      }
    });

    if (existingTransaction) {
      return res.status(200).json({
        success: true,
        message: 'A pontok már hozzá lettek adva a fiókodhoz',
        data: {
          transaction: existingTransaction
        }
      });
    }

    // Pontok hozzáadása a felhasználóhoz
    // Ha nincs metadata, próbáljuk meg kitalálni a pontok számát az összeg alapján
    let pointsToAdd = 0;

    if (session.metadata && session.metadata.points) {
      pointsToAdd = parseInt(session.metadata.points);
    } else {
      // Ha nincs metadata, próbáljuk meg kitalálni a pontok számát az összeg alapján
      const amount = session.amount_total || (session.payment_intent ? session.payment_intent.amount : 0);

      // Pontcsomagok árai és pontjai
      const packages = [
        { price: 5000, points: 4000 },
        { price: 10000, points: 9000 },
        { price: 15000, points: 14000 },
        { price: 20000, points: 18000 },
        { price: 25000, points: 22000 },
        { price: 30000, points: 25000 }
      ];

      // Keressük meg a megfelelő csomagot
      const pkg = packages.find(p => p.price * 100 === amount);
      if (pkg) {
        pointsToAdd = pkg.points;
      } else {
        // Ha nem találtunk megfelelő csomagot, használjunk egy alapértelmezett értéket
        pointsToAdd = Math.floor(amount / 100); // 1 pont minden 100 egység után
      }
    }

    console.log('Hozzáadandó pontok:', pointsToAdd);

    if (pointsToAdd <= 0) {
      return res.status(400).json({
        success: false,
        message: 'Nem sikerült meghatározni a hozzáadandó pontok számát'
      });
    }

    const result = await prisma.$transaction([
      // Pontok hozzáadása a felhasználóhoz
      prisma.user.update({
        where: { id: user.id },
        data: { points: { increment: pointsToAdd } }
      }),
      // Tranzakció rögzítése
      prisma.pointTransaction.create({
        data: {
          userId: user.id,
          amount: pointsToAdd,
          type: 'purchase',
          description: `${pointsToAdd} pont vásárlása`,
          stripePaymentIntentId: paymentIntentId
        }
      })
    ]);

    res.status(200).json({
      success: true,
      message: 'A pontok sikeresen hozzá lettek adva a fiókodhoz',
      data: {
        points: result[0].points,
        transaction: result[1]
      }
    });
  } catch (err) {
    next(err);
  }
};

// @desc    Pontfeltöltési számlák lekérdezése
// @route   GET /api/points/invoices
// @access  Private
export const getPointInvoices = async (req, res, next) => {
  try {
    const user = req.user;

    // Lekérjük a felhasználó ponttranzakcióit, amelyek vásárlások
    const pointPurchases = await prisma.pointTransaction.findMany({
      where: {
        userId: user.id,
        type: 'purchase',
        stripePaymentIntentId: {
          not: null
        }
      },
      orderBy: {
        createdAt: 'desc'
      }
    });

    // Ha nincs Stripe Customer ID, nem tudunk számlákat lekérdezni
    if (!user.stripeCustomerId) {
      return res.status(200).json({
        success: true,
        data: {
          invoices: [],
          payments: [],
          pointPurchases
        }
      });
    }

    // Lekérjük a felhasználó Stripe számláit
    const invoices = await stripe.invoices.list({
      customer: user.stripeCustomerId,
      limit: 100
    });

    // Lekérjük a felhasználó Stripe fizetéseit
    const charges = await stripe.charges.list({
      customer: user.stripeCustomerId,
      limit: 100
    });

    // Számlák formázása
    const formattedInvoices = invoices.data.map(invoice => ({
      id: invoice.id,
      number: invoice.number,
      amount: invoice.amount_paid,
      currency: invoice.currency,
      status: invoice.status,
      created: new Date(invoice.created * 1000).toISOString(),
      periodStart: invoice.period_start ? new Date(invoice.period_start * 1000).toISOString() : null,
      periodEnd: invoice.period_end ? new Date(invoice.period_end * 1000).toISOString() : null,
      pdfUrl: invoice.invoice_pdf,
      hostedInvoiceUrl: invoice.hosted_invoice_url,
      description: 'Előfizetés'
    }));

    // Fizetések formázása
    const formattedCharges = charges.data.map(charge => {
      // Ellenőrizzük, hogy ez egy pontfeltöltési fizetés-e
      const pointPurchase = pointPurchases.find(p => p.stripePaymentIntentId === charge.payment_intent);

      return {
        id: charge.id,
        amount: charge.amount,
        currency: charge.currency,
        status: charge.status,
        description: pointPurchase ? `${pointPurchase.amount} pont vásárlása` : (charge.description || 'Fizetés'),
        created: new Date(charge.created * 1000).toISOString(),
        receiptUrl: charge.receipt_url,
        paymentIntent: charge.payment_intent,
        isPointPurchase: !!pointPurchase,
        pointAmount: pointPurchase ? pointPurchase.amount : null
      };
    });

    // Csak a pontfeltöltési fizetések kiszűrése
    const pointPurchaseCharges = formattedCharges.filter(charge => charge.isPointPurchase);

    res.status(200).json({
      success: true,
      data: {
        invoices: formattedInvoices,
        payments: pointPurchaseCharges,
        pointPurchases
      }
    });
  } catch (err) {
    console.error('Hiba a pontfeltöltési számlák lekérdezésekor:', err);
    next(err);
  }
};

// @desc    Webhook kezelése a pontfeltöltéshez
// @route   POST /api/points/webhook
// @access  Public
export const handlePointsWebhook = async (req, res, next) => {
  try {
    const signature = req.headers['stripe-signature'];

    if (!signature) {
      console.error('Hiányzó Stripe aláírás');
      return res.status(400).json({
        success: false,
        message: 'Hiányzó Stripe aláírás'
      });
    }

    // Webhook esemény ellenőrzése
    let event;
    try {
      // Külön webhook titkos kulcs a pontrendszerhez
      const webhookSecret = process.env.STRIPE_POINTS_WEBHOOK_SECRET || process.env.STRIPE_WEBHOOK_SECRET;

      console.log('Webhook titkos kulcs:', webhookSecret ? 'Megadva' : 'Hiányzik');
      console.log('Webhook aláírás:', signature);
      console.log('Webhook payload mérete:', req.rawBody ? req.rawBody.length : 'Hiányzik');

      // Közvetlenül használjuk a stripe.webhooks.constructEvent függvényt
      event = stripe.webhooks.constructEvent(
        req.rawBody,
        signature,
        webhookSecret
      );

      console.log('Webhook esemény sikeresen feldolgozva:', event.type);
    } catch (err) {
      console.error('Webhook aláírás ellenőrzési hiba:', err);
      return res.status(400).json({
        success: false,
        message: 'Webhook aláírás ellenőrzési hiba'
      });
    }

    // Esemény típusa alapján kezelés
    console.log('Webhook esemény típusa:', event.type);

    if (event.type === 'checkout.session.completed') {
      const session = event.data.object;
      console.log('Checkout session completed:', {
        sessionId: session.id,
        paymentStatus: session.payment_status,
        metadata: session.metadata
      });

      // Minden checkout session-t kezelünk, ami a points oldalól jön
      // A metadata ellenőrzését engedékenyebbé tesszük
      if (session.metadata && session.metadata.userId) {
        console.log('Pontfeltöltés kezelése:', session.id);
        await handlePointPurchaseCompleted(session);
      } else {
        console.log('Nem pontfeltöltési session:', session.id);
      }
    } else if (event.type === 'payment_intent.succeeded') {
      const paymentIntent = event.data.object;
      console.log('Payment intent succeeded:', {
        paymentIntentId: paymentIntent.id,
        amount: paymentIntent.amount,
        metadata: paymentIntent.metadata
      });

      // Ha a payment intent-hez tartozik checkout session, akkor azt is kezeljük
      if (paymentIntent.metadata && paymentIntent.metadata.userId) {
        // Keressük meg a checkout session-t a payment intent alapján
        try {
          const sessions = await stripe.checkout.sessions.list({
            payment_intent: paymentIntent.id,
            limit: 1
          });

          if (sessions.data.length > 0) {
            const session = sessions.data[0];
            console.log('Megtalált session a payment intent alapján:', session.id);
            await handlePointPurchaseCompleted(session);
          } else {
            console.log('Nem találtunk session-t a payment intent-hez:', paymentIntent.id);
          }
        } catch (error) {
          console.error('Hiba a session keresésekor:', error);
        }
      }
    }

    res.status(200).json({ received: true });
  } catch (err) {
    next(err);
  }
};

// Pontfeltöltés kezelése
const handlePointPurchaseCompleted = async (session) => {
  try {
    // Lekérjük a teljes session adatokat a payment_intent-tel együtt
    const fullSession = await stripe.checkout.sessions.retrieve(session.id, {
      expand: ['payment_intent']
    });

    // Ellenőrizzük, hogy a fizetés sikeres volt-e
    const isPaymentSuccessful = fullSession.payment_status === 'paid' ||
                               (fullSession.payment_intent && fullSession.payment_intent.status === 'succeeded');

    if (!isPaymentSuccessful) {
      console.log('A fizetés még nem teljesült:', fullSession.id);
      return;
    }

    // A payment_intent lehet string vagy objektum, ezért kezeljük mindkettőt
    const paymentIntentId = typeof fullSession.payment_intent === 'string'
                          ? fullSession.payment_intent
                          : (fullSession.payment_intent ? fullSession.payment_intent.id : null);

    if (!paymentIntentId) {
      console.log('Hiányzó payment intent azonosító:', fullSession.id);
      return;
    }

    // Próbáljuk meg kinyerni a felhasználó azonosítóját
    let userId = null;

    if (fullSession.metadata && fullSession.metadata.userId) {
      userId = fullSession.metadata.userId;
    } else if (fullSession.customer) {
      // Ha nincs userId a metadata-ban, próbáljuk meg a customer ID alapján megtalálni a felhasználót
      try {
        const user = await prisma.user.findFirst({
          where: {
            stripeCustomerId: fullSession.customer
          }
        });

        if (user) {
          userId = user.id;
          console.log('Felhasználó megtalálva a customer ID alapján:', userId);
        }
      } catch (error) {
        console.error('Hiba a felhasználó keresésekor:', error);
      }
    }

    if (!userId) {
      console.log('Hiányzó felhasználó azonosító a metadata-ban és nem találtunk felhasználót a customer ID alapján:', fullSession.id);
      return;
    }

    // Pontok meghatározása
    let pointsToAdd = 0;

    if (fullSession.metadata && fullSession.metadata.points) {
      pointsToAdd = parseInt(fullSession.metadata.points);
    } else {
      // Ha nincs metadata, próbáljuk meg kitalálni a pontok számát az összeg alapján
      const amount = fullSession.amount_total || (fullSession.payment_intent ? fullSession.payment_intent.amount : 0);

      // Pontcsomagok árai és pontjai
      const packages = [
        { price: 5000, points: 4000 },
        { price: 10000, points: 9000 },
        { price: 15000, points: 14000 },
        { price: 20000, points: 18000 },
        { price: 25000, points: 22000 },
        { price: 30000, points: 25000 }
      ];

      // Keressük meg a megfelelő csomagot
      const pkg = packages.find(p => p.price * 100 === amount);
      if (pkg) {
        pointsToAdd = pkg.points;
      } else {
        // Ha nem találtunk megfelelő csomagot, használjunk egy alapértelmezett értéket
        pointsToAdd = Math.floor(amount / 100); // 1 pont minden 100 egység után
      }
    }

    if (pointsToAdd <= 0) {
      console.log('Nem sikerült meghatározni a hozzáadandó pontok számát:', fullSession.id);
      return;
    }

    // Ellenőrizzük, hogy a pontok már hozzá lettek-e adva
    const existingTransaction = await prisma.pointTransaction.findFirst({
      where: {
        stripePaymentIntentId: paymentIntentId,
        userId,
        type: 'purchase'
      }
    });

    if (existingTransaction) {
      console.log('A pontok már hozzá lettek adva:', paymentIntentId);
      return;
    }

    // Pontok hozzáadása a felhasználóhoz
    await prisma.$transaction([
      // Pontok hozzáadása a felhasználóhoz
      prisma.user.update({
        where: { id: userId },
        data: { points: { increment: pointsToAdd } }
      }),
      // Tranzakció rögzítése
      prisma.pointTransaction.create({
        data: {
          userId,
          amount: pointsToAdd,
          type: 'purchase',
          description: `${pointsToAdd} pont vásárlása`,
          stripePaymentIntentId: paymentIntentId
        }
      })
    ]);

    console.log('Pontok sikeresen hozzáadva:', userId, pointsToAdd);
  } catch (error) {
    console.error('Hiba a pontfeltöltés kezelésekor:', error);
  }
};
