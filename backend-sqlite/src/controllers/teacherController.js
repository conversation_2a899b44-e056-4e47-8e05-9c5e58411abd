import prisma from '../utils/db.js';
import * as teacherService from '../services/teacherService.js';
import * as stripeUtils from '../utils/stripe.js';
import Stripe from 'stripe';
import { sendEmail, getInvitationEmailTemplate } from '../utils/emailService.js';

const stripe = new Stripe(process.env.STRIPE_SECRET_KEY);

// @desc    Tanári előfizetés létrehozása
// @route   POST /api/teacher/subscribe
// @access  Private
export const createTeacherSubscription = async (req, res, next) => {
  try {
    const user = req.user;

    // Ellen<PERSON>ü<PERSON>, hogy a felhasználónak van-e már tan<PERSON> el<PERSON>
    if (user.isTeacher) {
      return res.status(400).json({
        success: false,
        message: 'M<PERSON>r rendelkezel taná<PERSON> el<PERSON>'
      });
    }

    // Checkout session létrehozása
    const session = await teacherService.createTeacherSubscription(
      user.id,
      user.stripeCustomerId
    );

    res.status(200).json({
      success: true,
      sessionId: session.id,
      url: session.url
    });
  } catch (err) {
    next(err);
  }
};

// @desc    Tanári előfizetés ellenőrzése
// @route   GET /api/teacher/verify-subscription/:sessionId
// @access  Private
export const verifyTeacherSubscription = async (req, res, next) => {
  try {
    const { sessionId } = req.params;
    const user = req.user;

    // Session lekérése a Stripe-tól
    const session = await stripe.checkout.sessions.retrieve(sessionId, {
      expand: ['subscription']
    });

    // Ellenőrizzük, hogy a session a jelenlegi felhasználóhoz tartozik-e
    if (session.customer !== user.stripeCustomerId) {
      return res.status(403).json({
        success: false,
        message: 'Nem jogosult a művelet végrehajtására'
      });
    }

    // Ellenőrizzük, hogy a session sikeres volt-e
    if (session.payment_status !== 'paid') {
      return res.status(400).json({
        success: false,
        message: 'A fizetés még nem teljesült'
      });
    }

    // Ellenőrizzük, hogy a session tartalmaz-e előfizetést
    if (!session.subscription) {
      return res.status(400).json({
        success: false,
        message: 'A session nem tartalmaz előfizetést'
      });
    }

    // Tanári előfizetés aktiválása
    const updatedUser = await teacherService.activateTeacherSubscription(
      user.id,
      session.subscription.id
    );

    res.status(200).json({
      success: true,
      message: 'Tanári előfizetés sikeresen aktiválva',
      data: {
        isTeacher: updatedUser.isTeacher,
        maxStudents: updatedUser.maxStudents,
        monthlyPoints: updatedUser.monthlyPoints
      }
    });
  } catch (err) {
    next(err);
  }
};

// @desc    Ingyenes próba csomag aktiválása
// @route   POST /api/teacher/activate-free-trial
// @access  Private
export const activateFreeTrialPackage = async (req, res, next) => {
  try {
    const user = req.user;

    // Ingyenes próba csomag aktiválása
    const updatedUser = await teacherService.activateFreeTrialPackage(user.id);

    res.status(200).json({
      success: true,
      message: 'Ingyenes próba csomag sikeresen aktiválva',
      data: {
        points: updatedUser.points
      }
    });
  } catch (err) {
    next(err);
  }
};

// @desc    Diák meghívása
// @route   POST /api/teacher/invite-student
// @access  Private (Teacher)
export const inviteStudent = async (req, res, next) => {
  try {
    const { email } = req.body;
    const user = req.user;

    // Ellenőrizzük, hogy a felhasználó tanár-e
    if (!user.isTeacher) {
      return res.status(403).json({
        success: false,
        message: 'Csak tanárok hívhatnak meg diákokat'
      });
    }

    // Ellenőrizzük, hogy a meghívott email már regisztrált-e
    const existingUser = await prisma.user.findUnique({
      where: { email }
    });

    // Ha a felhasználó már létezik, ellenőrizzük, hogy már a tanár diákja-e
    if (existingUser) {
      const existingRelation = await prisma.teacherStudent.findUnique({
        where: {
          teacherId_studentId: {
            teacherId: user.id,
            studentId: existingUser.id
          }
        }
      });

      if (existingRelation) {
        return res.status(400).json({
          success: false,
          message: 'Ez a felhasználó már a diákod'
        });
      }

      // Ha a felhasználó létezik, de még nem a tanár diákja, hozzáadjuk
      const relation = await prisma.teacherStudent.create({
        data: {
          teacherId: user.id,
          studentId: existingUser.id
        }
      });

      // Ajándék pontok hozzáadása a diáknak
      await prisma.$transaction([
        // Pontok hozzáadása a diáknak
        prisma.user.update({
          where: { id: existingUser.id },
          data: { points: { increment: teacherService.TEACHER_PACKAGE.studentGiftPoints } }
        }),
        // Tranzakció rögzítése
        prisma.pointTransaction.create({
          data: {
            userId: existingUser.id,
            amount: teacherService.TEACHER_PACKAGE.studentGiftPoints,
            type: 'teacher_gift',
            description: `${user.name} tanár ajándék pontjai`
          }
        })
      ]);

      return res.status(200).json({
        success: true,
        message: 'Felhasználó sikeresen hozzáadva a diákjaidhoz',
        data: {
          relation
        }
      });
    }

    // Ha a felhasználó még nem létezik, meghívót küldünk
    const invitation = await teacherService.inviteStudent(user.id, email);

    // Meghívó link generálása
    const invitationLink = `${process.env.FRONTEND_URL}/register?token=${invitation.token}`;

    // Email küldése
    await sendEmail({
      to: email,
      subject: 'Meghívó a Magyar-Német Nyelvtanuló alkalmazásba',
      html: getInvitationEmailTemplate(user.name, invitationLink, true)
    });

    res.status(201).json({
      success: true,
      message: 'Meghívó sikeresen elküldve',
      data: {
        invitation: {
          id: invitation.id,
          email: invitation.email,
          status: invitation.status,
          expiresAt: invitation.expiresAt
        }
      }
    });
  } catch (err) {
    next(err);
  }
};

// @desc    Diákok listázása
// @route   GET /api/teacher/students
// @access  Private (Teacher)
export const getStudents = async (req, res, next) => {
  try {
    const user = req.user;

    // Ellenőrizzük, hogy a felhasználó tanár-e
    if (!user.isTeacher) {
      return res.status(403).json({
        success: false,
        message: 'Csak tanárok férhetnek hozzá a diákok listájához'
      });
    }

    // Diákok lekérése
    const students = await prisma.teacherStudent.findMany({
      where: {
        teacherId: user.id,
        status: 'active'
      },
      include: {
        student: {
          select: {
            id: true,
            name: true,
            email: true,
            points: true,
            createdAt: true
          }
        }
      },
      orderBy: {
        createdAt: 'desc'
      }
    });

    res.status(200).json({
      success: true,
      data: students.map(relation => ({
        id: relation.id,
        student: relation.student,
        status: relation.status,
        createdAt: relation.createdAt
      }))
    });
  } catch (err) {
    next(err);
  }
};

export default {
  createTeacherSubscription,
  verifyTeacherSubscription,
  activateFreeTrialPackage,
  inviteStudent,
  getStudents
};
