import bcrypt from 'bcryptjs';
import jwt from 'jsonwebtoken';
import crypto from 'crypto';
import prisma from '../utils/db.js';
import * as stripeUtils from '../utils/stripe.js';
import * as teacherService from '../services/teacherService.js';
import { sendEmail } from '../utils/emailService.js';

// JWT token generálása
const generateToken = (id) => {
  return jwt.sign({ id }, process.env.JWT_SECRET, {
    expiresIn: process.env.JWT_EXPIRE
  });
};

// Token küldése válaszként
const sendTokenResponse = (user, statusCode, res) => {
  // Token generálása
  const token = generateToken(user.id);

  // <PERSON>ie opciók
  const options = {
    expires: new Date(
      Date.now() + process.env.JWT_COOKIE_EXPIRE * 24 * 60 * 60 * 1000
    ),
    httpOnly: true
  };

  // HTTPS használata produkcióban
  if (process.env.NODE_ENV === 'production') {
    options.secure = true;
  }

  // Jelszó eltávolítása a válaszból
  const userResponse = { ...user };
  delete userResponse.password;

  res
    .status(statusCode)
    .cookie('token', token, options)
    .json({
      success: true,
      token,
      user: userResponse
    });
};

// @desc    Felhasználó regisztrálása
// @route   POST /api/auth/register
// @access  Public
export const register = async (req, res, next) => {
  try {
    console.log('Regisztrációs kérés érkezett:', req.body);

    const { name, email, password, invitationToken } = req.body;

    // Alapvető beviteli ellenőrzés
    if (!name || !email || !password) {
      console.log('Hiányzó mezők:', { name, email, password: password ? 'Megadva' : 'Hiányzik' });
      return res.status(400).json({
        success: false,
        message: 'Kérjük, adja meg a nevét, email címét és jelszavát'
      });
    }

    // Ellenőrizzük, hogy létezik-e már a felhasználó
    const existingUser = await prisma.user.findUnique({
      where: { email }
    });

    if (existingUser) {
      console.log('Felhasználó már létezik:', email);
      return res.status(400).json({
        success: false,
        message: 'Ez az email cím már regisztrálva van'
      });
    }

    // Ha van meghívó token, ellenőrizzük
    let invitation = null;
    if (invitationToken) {
      invitation = await prisma.invitation.findUnique({
        where: { token: invitationToken }
      });

      // Ellenőrizzük, hogy létezik-e a meghívó
      if (!invitation) {
        return res.status(400).json({
          success: false,
          message: 'Érvénytelen meghívó token'
        });
      }

      // Ellenőrizzük, hogy a meghívó nem járt-e le
      if (invitation.expiresAt < new Date()) {
        return res.status(400).json({
          success: false,
          message: 'A meghívó lejárt'
        });
      }

      // Ellenőrizzük, hogy a meghívó nincs-e már elfogadva
      if (invitation.status !== 'pending') {
        return res.status(400).json({
          success: false,
          message: 'A meghívó már fel lett használva'
        });
      }

      // Ellenőrizzük, hogy a meghívó email címe megegyezik-e a regisztrációs email címmel
      if (invitation.email !== email) {
        return res.status(400).json({
          success: false,
          message: 'A meghívó más email címre szól'
        });
      }
    }

    try {
      // Stripe ügyfél létrehozása
      console.log('Stripe ügyfél létrehozása:', email, name);
      const customer = await stripeUtils.createCustomer(email, name);
      console.log('Stripe ügyfél létrehozva:', customer.id);

      // Jelszó titkosítása
      const salt = await bcrypt.genSalt(10);
      const hashedPassword = await bcrypt.hash(password, salt);

      // Felhasználó létrehozása
      console.log('SQLite felhasználó létrehozása');
      const user = await prisma.user.create({
        data: {
          name,
          email,
          password: hashedPassword,
          stripeCustomerId: customer.id,
          points: 15 // Explicit módon megadjuk a pontszámot
        }
      });
      console.log('Felhasználó létrehozva:', user.id);

      // Ha van meghívó, frissítsük az állapotát
      if (invitation) {
        await prisma.invitation.update({
          where: { id: invitation.id },
          data: {
            status: 'accepted',
            inviteeId: user.id
          }
        });
        console.log('Meghívó elfogadva:', invitation.id);

        // Ellenőrizzük, hogy tanári meghívó-e
        const inviter = await prisma.user.findUnique({
          where: { id: invitation.inviterId },
          select: { isTeacher: true }
        });

        // Ha tanár hívta meg, akkor hozzáadjuk a tanár-diák kapcsolatot
        if (inviter && inviter.isTeacher) {
          await prisma.teacherStudent.create({
            data: {
              teacherId: invitation.inviterId,
              studentId: user.id
            }
          });

          // Ajándék pontok hozzáadása a diáknak
          await prisma.$transaction([
            // Pontok hozzáadása a diáknak
            prisma.user.update({
              where: { id: user.id },
              data: { points: { increment: teacherService.TEACHER_PACKAGE.studentGiftPoints } }
            }),
            // Tranzakció rögzítése
            prisma.pointTransaction.create({
              data: {
                userId: user.id,
                amount: teacherService.TEACHER_PACKAGE.studentGiftPoints,
                type: 'teacher_gift',
                description: 'Tanári meghívás ajándék pontjai'
              }
            })
          ]);

          console.log('Tanár-diák kapcsolat létrehozva és ajándék pontok hozzáadva');
        }
      } else {
        // Az alapértelmezett pontszám már 15, így nem kell külön aktiválni az ingyenes próba csomagot
        // Csak rögzítjük a tranzakciót, hogy nyomon követhető legyen
        try {
          await prisma.pointTransaction.create({
            data: {
              userId: user.id,
              amount: 15,
              type: 'free_trial',
              description: 'Ingyenes próba csomag (regisztrációkor)'
            }
          });
          console.log('Ingyenes próba csomag tranzakció rögzítve:', user.id);
        } catch (freeTrialErr) {
          console.error('Hiba a tranzakció rögzítésekor:', freeTrialErr);
          // A hiba nem akadályozza meg a regisztrációt
        }
      }

      // Token generálása
      console.log('Token generálása és válasz küldése');
      sendTokenResponse(user, 201, res);
    } catch (stripeErr) {
      console.error('Hiba a Stripe vagy felhasználó létrehozásakor:', stripeErr);
      return res.status(500).json({
        success: false,
        message: 'Hiba a regisztráció során: ' + stripeErr.message
      });
    }
  } catch (err) {
    console.error('Regisztrációs hiba:', err);
    next(err);
  }
};

// @desc    Felhasználó bejelentkeztetése
// @route   POST /api/auth/login
// @access  Public
export const login = async (req, res, next) => {
  try {
    const { email, password } = req.body;

    // Ellenőrizzük, hogy az email és a jelszó meg van-e adva
    if (!email || !password) {
      return res.status(400).json({
        success: false,
        message: 'Kérjük, adja meg az email címét és jelszavát'
      });
    }

    // Felhasználó keresése
    const user = await prisma.user.findUnique({
      where: { email }
    });

    // Bejelentkezési napló adatok
    const ipAddress = req.headers['x-forwarded-for'] || req.socket.remoteAddress;
    const userAgent = req.headers['user-agent'];

    // Ha nincs ilyen felhasználó, naplózzuk a sikertelen bejelentkezést
    if (!user) {
      // Sikertelen bejelentkezés naplózása (ha van email)
      try {
        await prisma.loginLog.create({
          data: {
            userId: 'unknown', // Nem létező felhasználó
            ipAddress,
            userAgent,
            success: false
          }
        });
      } catch (logErr) {
        console.error('Hiba a bejelentkezési napló létrehozásakor:', logErr);
      }

      return res.status(401).json({
        success: false,
        message: 'Érvénytelen bejelentkezési adatok'
      });
    }

    // Jelszó ellenőrzése
    const isMatch = await bcrypt.compare(password, user.password);

    // Sikertelen bejelentkezés naplózása
    if (!isMatch) {
      try {
        await prisma.loginLog.create({
          data: {
            userId: user.id,
            ipAddress,
            userAgent,
            success: false
          }
        });
      } catch (logErr) {
        console.error('Hiba a bejelentkezési napló létrehozásakor:', logErr);
      }

      return res.status(401).json({
        success: false,
        message: 'Érvénytelen bejelentkezési adatok'
      });
    }

    // Sikeres bejelentkezés naplózása
    try {
      await prisma.loginLog.create({
        data: {
          userId: user.id,
          ipAddress,
          userAgent,
          success: true
        }
      });
    } catch (logErr) {
      console.error('Hiba a bejelentkezési napló létrehozásakor:', logErr);
      // A naplózási hiba nem akadályozza meg a bejelentkezést
    }

    // Token generálása
    sendTokenResponse(user, 200, res);
  } catch (err) {
    console.error('Bejelentkezési hiba:', err);
    next(err);
  }
};

// @desc    Felhasználó kijelentkeztetése
// @route   GET /api/auth/logout
// @access  Private
export const logout = (req, res, next) => {
  res.cookie('token', 'none', {
    expires: new Date(Date.now() + 10 * 1000),
    httpOnly: true
  });

  res.status(200).json({
    success: true,
    message: 'Sikeres kijelentkezés'
  });
};

// @desc    Aktuális felhasználó lekérése
// @route   GET /api/auth/me
// @access  Private
export const getMe = async (req, res, next) => {
  try {
    // Felhasználó lekérése
    const user = await prisma.user.findUnique({
      where: { id: req.user.id }
    });

    // Csak azokat a mezőket adjuk vissza, amelyek biztosan léteznek
    const safeUser = {
      id: user.id,
      name: user.name,
      email: user.email,
      stripeCustomerId: user.stripeCustomerId,
      points: user.points,
      createdAt: user.createdAt,
      updatedAt: user.updatedAt,
      isAdmin: user.isAdmin || false
    };

    // Opcionális mezők hozzáadása, ha léteznek
    if ('isTeacher' in user) safeUser.isTeacher = user.isTeacher;
    if ('maxStudents' in user) safeUser.maxStudents = user.maxStudents;
    if ('monthlyPoints' in user) safeUser.monthlyPoints = user.monthlyPoints;

    // Előfizetés lekérése
    const subscription = await prisma.subscription.findFirst({
      where: {
        userId: req.user.id,
        status: 'active'
      },
      orderBy: {
        createdAt: 'desc'
      }
    });

    // Válasz küldése
    res.status(200).json({
      success: true,
      data: {
        ...safeUser,
        subscription: subscription ? {
          id: subscription.id,
          status: subscription.status,
          currentPeriodEnd: subscription.stripeCurrentPeriodEnd,
          cancelAtPeriodEnd: subscription.cancelAtPeriodEnd
        } : null
      }
    });
  } catch (err) {
    next(err);
  }
};

// @desc    Elfelejtett jelszó - jelszó visszaállítási token küldése
// @route   POST /api/auth/forgot-password
// @access  Public
export const forgotPassword = async (req, res, next) => {
  try {
    const { email } = req.body;

    // Ellenőrizzük, hogy az email meg van-e adva
    if (!email) {
      return res.status(400).json({
        success: false,
        message: 'Kérjük, adja meg az email címét'
      });
    }

    // Felhasználó keresése
    const user = await prisma.user.findUnique({
      where: { email }
    });

    // Ha nincs ilyen felhasználó, akkor is sikeres választ adunk (biztonsági okokból)
    if (!user) {
      return res.status(200).json({
        success: true,
        message: 'Ha az email cím regisztrálva van, akkor elküldtük a jelszó visszaállítási linket'
      });
    }

    // Jelszó visszaállítási token generálása
    const resetToken = crypto.randomBytes(20).toString('hex');

    // Token hash-elése
    const resetPasswordToken = crypto
      .createHash('sha256')
      .update(resetToken)
      .digest('hex');

    // Token lejárati ideje (1 óra)
    const resetPasswordExpire = new Date(Date.now() + 60 * 60 * 1000);

    // Token mentése az adatbázisba
    await prisma.user.update({
      where: { id: user.id },
      data: {
        resetPasswordToken,
        resetPasswordExpire
      }
    });

    // Reset URL
    const resetUrl = `${process.env.FRONTEND_URL}/reset-password/${resetToken}`;

    // Email sablon
    const html = `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="utf-8">
        <title>Jelszó visszaállítás</title>
        <style>
          body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
          }
          .header {
            background-color: #4F46E5;
            color: white;
            padding: 20px;
            text-align: center;
            border-radius: 5px 5px 0 0;
          }
          .content {
            padding: 20px;
            border: 1px solid #ddd;
            border-top: none;
            border-radius: 0 0 5px 5px;
          }
          .button {
            display: inline-block;
            background-color: #4F46E5;
            color: white;
            text-decoration: none;
            padding: 10px 20px;
            border-radius: 5px;
            margin: 20px 0;
          }
          .footer {
            margin-top: 20px;
            font-size: 12px;
            color: #666;
            text-align: center;
          }
        </style>
      </head>
      <body>
        <div class="header">
          <h1>Jelszó visszaállítás</h1>
        </div>
        <div class="content">
          <p>Kedves ${user.name}!</p>
          <p>Jelszó visszaállítási kérelmet kaptál a Magyar-Német Nyelvtanuló alkalmazásban.</p>
          <p>A jelszó visszaállításához kattints az alábbi gombra:</p>
          <p style="text-align: center;">
            <a href="${resetUrl}" class="button">Jelszó visszaállítása</a>
          </p>
          <p>Ha a gomb nem működik, másold be az alábbi linket a böngésződbe:</p>
          <p>${resetUrl}</p>
          <p>Ez a link 1 óráig érvényes.</p>
          <p>Ha nem te kérted a jelszó visszaállítást, hagyd figyelmen kívül ezt az emailt.</p>
          <p>Üdvözlettel,<br>Magyar-Német Nyelvtanuló Csapat</p>
        </div>
        <div class="footer">
          <p>Ez egy automatikus üzenet, kérjük, ne válaszolj rá.</p>
        </div>
      </body>
      </html>
    `;

    try {
      await sendEmail({
        to: user.email,
        subject: 'Jelszó visszaállítás - Magyar-Német Nyelvtanuló',
        html
      });

      res.status(200).json({
        success: true,
        message: 'Ha az email cím regisztrálva van, akkor elküldtük a jelszó visszaállítási linket'
      });
    } catch (error) {
      console.error('Hiba az email küldésekor:', error);

      // Töröljük a tokent az adatbázisból
      await prisma.user.update({
        where: { id: user.id },
        data: {
          resetPasswordToken: null,
          resetPasswordExpire: null
        }
      });

      return res.status(500).json({
        success: false,
        message: 'Nem sikerült elküldeni az emailt. Kérjük, próbáld újra később.'
      });
    }
  } catch (err) {
    console.error('Jelszó visszaállítási hiba:', err);
    next(err);
  }
};

// @desc    Jelszó visszaállítása
// @route   POST /api/auth/reset-password/:resetToken
// @access  Public
export const resetPassword = async (req, res, next) => {
  try {
    const { password } = req.body;
    const { resetToken } = req.params;

    // Ellenőrizzük, hogy a jelszó meg van-e adva
    if (!password) {
      return res.status(400).json({
        success: false,
        message: 'Kérjük, adja meg az új jelszavát'
      });
    }

    // Token hash-elése
    const resetPasswordToken = crypto
      .createHash('sha256')
      .update(resetToken)
      .digest('hex');

    // Felhasználó keresése a token alapján
    const user = await prisma.user.findFirst({
      where: {
        resetPasswordToken,
        resetPasswordExpire: {
          gt: new Date()
        }
      }
    });

    if (!user) {
      return res.status(400).json({
        success: false,
        message: 'Érvénytelen vagy lejárt token'
      });
    }

    // Új jelszó titkosítása
    const salt = await bcrypt.genSalt(10);
    const hashedPassword = await bcrypt.hash(password, salt);

    // Jelszó frissítése és token törlése
    await prisma.user.update({
      where: { id: user.id },
      data: {
        password: hashedPassword,
        resetPasswordToken: null,
        resetPasswordExpire: null
      }
    });

    res.status(200).json({
      success: true,
      message: 'A jelszó sikeresen frissítve'
    });
  } catch (err) {
    console.error('Jelszó visszaállítási hiba:', err);
    next(err);
  }
};
