import prisma from '../utils/db.js';
import bcrypt from 'bcryptjs';
import { sendEmail } from '../utils/emailService.js';
import crypto from 'crypto';

// Admin jogosultság ellenőrzése
const checkAdminPermission = (userId) => {
  return userId === '11383db6-ab6e-4810-81a9-dc5ac1426d3a';
};

// @desc    Összes felhasználó lekérése
// @route   GET /api/admin/users
// @access  Admin
export const getAllUsers = async (req, res, next) => {
  try {
    // Admin jogosultság ellenőrzése
    if (!checkAdminPermission(req.user.id)) {
      return res.status(403).json({
        success: false,
        message: 'Nincs jogosultságod ehhez a művelethez'
      });
    }

    // Felhasználók lekérése
    const users = await prisma.user.findMany({
      select: {
        id: true,
        name: true,
        email: true,
        points: true,
        isAdmin: true,
        createdAt: true,
        updatedAt: true,
        _count: {
          select: {
            subscriptions: true,
            pointTransactions: true
          }
        }
      },
      orderBy: {
        createdAt: 'desc'
      }
    });

    // Aktív előfizetések lekérése minden felhasználóhoz
    const usersWithSubscription = await Promise.all(
      users.map(async (user) => {
        const activeSubscription = await prisma.subscription.findFirst({
          where: {
            userId: user.id,
            status: 'active'
          },
          orderBy: {
            createdAt: 'desc'
          }
        });

        return {
          ...user,
          hasActiveSubscription: !!activeSubscription,
          subscription: activeSubscription
        };
      })
    );

    res.status(200).json({
      success: true,
      count: usersWithSubscription.length,
      data: usersWithSubscription
    });
  } catch (err) {
    console.error('Admin felhasználók lekérési hiba:', err);
    next(err);
  }
};

// @desc    Felhasználó részleteinek lekérése
// @route   GET /api/admin/users/:id
// @access  Admin
export const getUserDetails = async (req, res, next) => {
  try {
    const { id } = req.params;

    // Admin jogosultság ellenőrzése
    if (!checkAdminPermission(req.user.id)) {
      return res.status(403).json({
        success: false,
        message: 'Nincs jogosultságod ehhez a művelethez'
      });
    }

    // Felhasználó lekérése
    const user = await prisma.user.findUnique({
      where: { id },
      select: {
        id: true,
        name: true,
        email: true,
        points: true,
        isAdmin: true,
        stripeCustomerId: true,
        createdAt: true,
        updatedAt: true
      }
    });

    if (!user) {
      return res.status(404).json({
        success: false,
        message: 'Felhasználó nem található'
      });
    }

    // Előfizetések lekérése
    const subscriptions = await prisma.subscription.findMany({
      where: { userId: id },
      orderBy: { createdAt: 'desc' }
    });

    // Pont tranzakciók lekérése
    const pointTransactions = await prisma.pointTransaction.findMany({
      where: { userId: id },
      orderBy: { createdAt: 'desc' },
      take: 50 // Csak az utolsó 50 tranzakciót kérjük le
    });

    // Support ticketek lekérése
    const supportTickets = await prisma.supportTicket.findMany({
      where: { creatorId: id },
      orderBy: { createdAt: 'desc' },
      include: {
        _count: {
          select: { messages: true }
        }
      }
    });

    res.status(200).json({
      success: true,
      data: {
        user,
        subscriptions,
        pointTransactions,
        supportTickets
      }
    });
  } catch (err) {
    console.error('Admin felhasználó részletek lekérési hiba:', err);
    next(err);
  }
};

// @desc    Új jelszó küldése felhasználónak
// @route   POST /api/admin/users/:id/reset-password
// @access  Admin
export const sendPasswordReset = async (req, res, next) => {
  try {
    const { id } = req.params;

    // Admin jogosultság ellenőrzése
    if (!checkAdminPermission(req.user.id)) {
      return res.status(403).json({
        success: false,
        message: 'Nincs jogosultságod ehhez a művelethez'
      });
    }

    // Felhasználó lekérése
    const user = await prisma.user.findUnique({
      where: { id }
    });

    if (!user) {
      return res.status(404).json({
        success: false,
        message: 'Felhasználó nem található'
      });
    }

    // Jelszó visszaállítási token generálása
    const resetToken = crypto.randomBytes(20).toString('hex');

    // Token hash-elése
    const resetPasswordToken = crypto
      .createHash('sha256')
      .update(resetToken)
      .digest('hex');

    // Token lejárati ideje (1 óra)
    const resetPasswordExpire = new Date(Date.now() + 60 * 60 * 1000);

    // Token mentése az adatbázisba
    await prisma.user.update({
      where: { id },
      data: {
        resetPasswordToken,
        resetPasswordExpire
      }
    });

    // Reset URL
    const resetUrl = `${process.env.FRONTEND_URL}/reset-password/${resetToken}`;

    // Email sablon
    const html = `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="utf-8">
        <title>Jelszó visszaállítás</title>
        <style>
          body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
          }
          .header {
            background-color: #4F46E5;
            color: white;
            padding: 20px;
            text-align: center;
            border-radius: 5px 5px 0 0;
          }
          .content {
            padding: 20px;
            border: 1px solid #ddd;
            border-top: none;
            border-radius: 0 0 5px 5px;
          }
          .button {
            display: inline-block;
            background-color: #4F46E5;
            color: white;
            text-decoration: none;
            padding: 10px 20px;
            border-radius: 5px;
            margin: 20px 0;
          }
          .footer {
            margin-top: 20px;
            font-size: 12px;
            color: #666;
            text-align: center;
          }
        </style>
      </head>
      <body>
        <div class="header">
          <h1>Jelszó visszaállítás</h1>
        </div>
        <div class="content">
          <p>Kedves ${user.name}!</p>
          <p>Az adminisztrátor jelszó visszaállítást kezdeményezett a fiókodhoz a Magyar-Német Nyelvtanuló alkalmazásban.</p>
          <p>A jelszó visszaállításához kattints az alábbi gombra:</p>
          <p style="text-align: center;">
            <a href="${resetUrl}" class="button">Jelszó visszaállítása</a>
          </p>
          <p>Ha a gomb nem működik, másold be az alábbi linket a böngésződbe:</p>
          <p>${resetUrl}</p>
          <p>Ez a link 1 óráig érvényes.</p>
          <p>Ha nem te kérted a jelszó visszaállítást, kérjük, vedd fel a kapcsolatot az ügyfélszolgálattal.</p>
          <p>Üdvözlettel,<br>Magyar-Német Nyelvtanuló Csapat</p>
        </div>
        <div class="footer">
          <p>Ez egy automatikus üzenet, kérjük, ne válaszolj rá.</p>
        </div>
      </body>
      </html>
    `;

    try {
      await sendEmail({
        to: user.email,
        subject: 'Jelszó visszaállítás - Magyar-Német Nyelvtanuló',
        html
      });

      res.status(200).json({
        success: true,
        message: 'Jelszó visszaállítási email elküldve'
      });
    } catch (error) {
      console.error('Hiba az email küldésekor:', error);

      // Töröljük a tokent az adatbázisból
      await prisma.user.update({
        where: { id },
        data: {
          resetPasswordToken: null,
          resetPasswordExpire: null
        }
      });

      return res.status(500).json({
        success: false,
        message: 'Nem sikerült elküldeni az emailt. Kérjük, próbáld újra később.'
      });
    }
  } catch (err) {
    console.error('Admin jelszó visszaállítási hiba:', err);
    next(err);
  }
};

// @desc    Pontok módosítása
// @route   POST /api/admin/users/:id/points
// @access  Admin
export const adjustPoints = async (req, res, next) => {
  try {
    const { id } = req.params;
    const { amount, reason } = req.body;

    // Admin jogosultság ellenőrzése
    if (!checkAdminPermission(req.user.id)) {
      return res.status(403).json({
        success: false,
        message: 'Nincs jogosultságod ehhez a művelethez'
      });
    }

    // Ellenőrizzük, hogy az amount meg van-e adva és szám-e
    if (!amount || isNaN(amount)) {
      return res.status(400).json({
        success: false,
        message: 'Kérjük, adj meg egy érvényes pontszámot'
      });
    }

    // Felhasználó lekérése
    const user = await prisma.user.findUnique({
      where: { id }
    });

    if (!user) {
      return res.status(404).json({
        success: false,
        message: 'Felhasználó nem található'
      });
    }

    // Tranzakció létrehozása a pontok módosításához
    const result = await prisma.$transaction(async (prisma) => {
      // Pont tranzakció létrehozása
      const pointTransaction = await prisma.pointTransaction.create({
        data: {
          userId: id,
          amount: parseInt(amount),
          type: amount > 0 ? 'admin_add' : 'admin_deduct',
          description: reason || (amount > 0 ? 'Admin által hozzáadott pontok' : 'Admin által levont pontok')
        }
      });

      // Felhasználó pontjainak frissítése
      const updatedUser = await prisma.user.update({
        where: { id },
        data: {
          points: {
            increment: parseInt(amount)
          }
        }
      });

      return { pointTransaction, updatedUser };
    });

    res.status(200).json({
      success: true,
      message: `${Math.abs(amount)} pont ${amount > 0 ? 'hozzáadva' : 'levonva'} a felhasználó egyenlegéből`,
      data: {
        transaction: result.pointTransaction,
        newBalance: result.updatedUser.points
      }
    });
  } catch (err) {
    console.error('Admin pont módosítási hiba:', err);
    next(err);
  }
};

// @desc    Statisztikák lekérése
// @route   GET /api/admin/stats
// @access  Admin
export const getStats = async (req, res, next) => {
  try {
    // Admin jogosultság ellenőrzése
    if (!checkAdminPermission(req.user.id)) {
      return res.status(403).json({
        success: false,
        message: 'Nincs jogosultságod ehhez a művelethez'
      });
    }

    // Összes felhasználó száma
    const totalUsers = await prisma.user.count();

    // Aktív előfizetéssel rendelkező felhasználók száma
    const activeSubscriptions = await prisma.subscription.count({
      where: {
        status: 'active'
      }
    });

    // Mai regisztrációk száma
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    const todayRegistrations = await prisma.user.count({
      where: {
        createdAt: {
          gte: today
        }
      }
    });

    // Elmúlt 7 nap regisztrációi
    const lastWeek = new Date();
    lastWeek.setDate(lastWeek.getDate() - 7);
    const weeklyRegistrations = await prisma.user.count({
      where: {
        createdAt: {
          gte: lastWeek
        }
      }
    });

    // Elmúlt 30 nap regisztrációi
    const lastMonth = new Date();
    lastMonth.setDate(lastMonth.getDate() - 30);
    const monthlyRegistrations = await prisma.user.count({
      where: {
        createdAt: {
          gte: lastMonth
        }
      }
    });

    // Összes support ticket száma
    const totalTickets = await prisma.supportTicket.count();

    // Nyitott support ticketek száma
    const openTickets = await prisma.supportTicket.count({
      where: {
        status: 'open'
      }
    });

    // Folyamatban lévő support ticketek száma
    const inProgressTickets = await prisma.supportTicket.count({
      where: {
        status: 'in_progress'
      }
    });

    // Lezárt support ticketek száma
    const closedTickets = await prisma.supportTicket.count({
      where: {
        status: 'closed'
      }
    });

    // Összes pont tranzakció
    const totalPointTransactions = await prisma.pointTransaction.count();

    // Összes kiosztott pont
    const totalPointsAdded = await prisma.pointTransaction.aggregate({
      _sum: {
        amount: true
      },
      where: {
        amount: {
          gt: 0
        }
      }
    });

    // Összes elhasznált pont
    const totalPointsUsed = await prisma.pointTransaction.aggregate({
      _sum: {
        amount: true
      },
      where: {
        amount: {
          lt: 0
        }
      }
    });

    res.status(200).json({
      success: true,
      data: {
        users: {
          total: totalUsers,
          activeSubscriptions,
          registrations: {
            today: todayRegistrations,
            weekly: weeklyRegistrations,
            monthly: monthlyRegistrations
          }
        },
        tickets: {
          total: totalTickets,
          open: openTickets,
          inProgress: inProgressTickets,
          closed: closedTickets
        },
        points: {
          totalTransactions: totalPointTransactions,
          totalAdded: totalPointsAdded._sum.amount || 0,
          totalUsed: totalPointsUsed._sum.amount || 0,
          balance: (totalPointsAdded._sum.amount || 0) + (totalPointsUsed._sum.amount || 0)
        }
      }
    });
  } catch (err) {
    console.error('Admin statisztikák lekérési hiba:', err);
    next(err);
  }
};
