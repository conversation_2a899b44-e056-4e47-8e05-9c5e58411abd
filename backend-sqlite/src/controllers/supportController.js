import prisma from '../utils/db.js';
import OpenAI from 'openai';
import { sendNewTicketEmail, sendNewMessageEmail, sendStatusChangeEmail } from '../utils/emailService.js';

// OpenAI konfiguráció
let openai;
try {
  openai = new OpenAI({
    apiKey: process.env.OPENAI_API_KEY || 'sk-dummy-key',
  });
} catch (error) {
  console.error('Hiba az OpenAI inicializálásakor:', error);
}

// @desc    Új support ticket létrehozása
// @route   POST /api/support/tickets
// @access  Private
export const createTicket = async (req, res, next) => {
  try {
    const { subject, message, priority = 'medium' } = req.body;
    const user = req.user;

    if (!subject || !message) {
      return res.status(400).json({
        success: false,
        message: 'A tárgy és az üzenet megadása kötelező'
      });
    }

    // Tranzakció létrehozása a ticket és az első üzenet biztonságos mentéséhez
    const result = await prisma.$transaction(async (prisma) => {
      // Ticket létrehozása
      const ticket = await prisma.supportTicket.create({
        data: {
          subject,
          priority,
          creatorId: user.id,
          status: 'open'
        }
      });

      // Első üzenet létrehozása
      const supportMessage = await prisma.supportMessage.create({
        data: {
          content: message,
          isFromAdmin: false,
          ticketId: ticket.id,
          userId: user.id
        }
      });

      return { ticket, supportMessage };
    });

    // E-mail értesítés küldése a felhasználónak
    try {
      await sendNewTicketEmail(result.ticket, user);
      console.log(`E-mail értesítés elküldve a felhasználónak: ${user.email}`);
    } catch (emailError) {
      console.error('Hiba az e-mail küldésekor:', emailError);
      // Az e-mail küldési hiba nem akadályozza meg a ticket létrehozását
    }

    res.status(201).json({
      success: true,
      data: result.ticket
    });
  } catch (err) {
    next(err);
  }
};

// @desc    Felhasználó saját ticketjeinek lekérdezése
// @route   GET /api/support/tickets
// @access  Private
export const getUserTickets = async (req, res, next) => {
  try {
    const user = req.user;

    // Felhasználó ticketjeinek lekérdezése
    const tickets = await prisma.supportTicket.findMany({
      where: {
        creatorId: user.id
      },
      orderBy: {
        updatedAt: 'desc'
      },
      include: {
        _count: {
          select: { messages: true }
        }
      }
    });

    res.status(200).json({
      success: true,
      data: tickets
    });
  } catch (err) {
    next(err);
  }
};

// @desc    Összes ticket lekérdezése (csak adminoknak)
// @route   GET /api/support/admin/tickets
// @access  Admin
export const getAllTickets = async (req, res, next) => {
  try {
    const user = req.user;

    // Ellenőrizzük, hogy a felhasználó a megadott ID-val rendelkezik-e
    if (user.id !== '11383db6-ab6e-4810-81a9-dc5ac1426d3a') {
      return res.status(403).json({
        success: false,
        message: 'Nincs jogosultságod ehhez a művelethez'
      });
    }
    console.log('Admin jogosultság érvényesítve a getAllTickets függvényben');

    // Összes ticket lekérdezése
    const tickets = await prisma.supportTicket.findMany({
      orderBy: [
        {
          status: 'asc' // Először a nyitott ticketek
        },
        {
          priority: 'desc' // Aztán a magas prioritásúak
        },
        {
          updatedAt: 'desc' // Végül a legújabbak
        }
      ],
      include: {
        creator: {
          select: {
            id: true,
            name: true,
            email: true
          }
        },
        _count: {
          select: { messages: true }
        }
      }
    });

    res.status(200).json({
      success: true,
      data: tickets
    });
  } catch (err) {
    next(err);
  }
};

// @desc    Ticket részleteinek lekérdezése
// @route   GET /api/support/tickets/:id
// @access  Private
export const getTicketDetails = async (req, res, next) => {
  try {
    const { id } = req.params;
    const user = req.user;

    // Ticket lekérdezése
    const ticket = await prisma.supportTicket.findUnique({
      where: { id },
      include: {
        creator: {
          select: {
            id: true,
            name: true,
            email: true
          }
        },
        messages: {
          orderBy: {
            createdAt: 'asc'
          },
          include: {
            user: {
              select: {
                id: true,
                name: true,
                email: true
              }
            }
          }
        }
      }
    });

    if (!ticket) {
      return res.status(404).json({
        success: false,
        message: 'A megadott azonosítójú ticket nem található'
      });
    }

    // Ellenőrizzük, hogy a felhasználó jogosult-e a ticket megtekintésére
    if (ticket.creatorId !== user.id && user.id !== '11383db6-ab6e-4810-81a9-dc5ac1426d3a') {
      return res.status(403).json({
        success: false,
        message: 'Nincs jogosultságod ehhez a tickethez'
      });
    }
    console.log('Jogosultság ellenőrzés érvényesítve a getTicketDetails függvényben');

    res.status(200).json({
      success: true,
      data: ticket
    });
  } catch (err) {
    next(err);
  }
};

// @desc    Üzenet küldése egy tickethez
// @route   POST /api/support/tickets/:id/messages
// @access  Private
export const addMessageToTicket = async (req, res, next) => {
  try {
    const { id } = req.params;
    const { content } = req.body;
    const user = req.user;

    if (!content) {
      return res.status(400).json({
        success: false,
        message: 'Az üzenet tartalma nem lehet üres'
      });
    }

    // Ticket lekérdezése
    const ticket = await prisma.supportTicket.findUnique({
      where: { id }
    });

    if (!ticket) {
      return res.status(404).json({
        success: false,
        message: 'A megadott azonosítójú ticket nem található'
      });
    }

    // Ellenőrizzük, hogy a felhasználó jogosult-e üzenetet küldeni a tickethez
    if (ticket.creatorId !== user.id && user.id !== '11383db6-ab6e-4810-81a9-dc5ac1426d3a') {
      return res.status(403).json({
        success: false,
        message: 'Nincs jogosultságod ehhez a tickethez'
      });
    }
    console.log('Jogosultság ellenőrzés érvényesítve az addMessageToTicket függvényben');

    // Ha a ticket le van zárva, nem lehet új üzenetet küldeni
    if (ticket.status === 'closed') {
      return res.status(400).json({
        success: false,
        message: 'A ticket le van zárva, nem küldhetsz új üzenetet'
      });
    }

    // Tranzakció létrehozása az üzenet és a ticket státusz frissítéséhez
    const result = await prisma.$transaction(async (prisma) => {
      // Üzenet létrehozása
      const message = await prisma.supportMessage.create({
        data: {
          content,
          isFromAdmin: user.id === '11383db6-ab6e-4810-81a9-dc5ac1426d3a', // Admin üzenet, ha a megadott ID-val rendelkezik
          ticketId: id,
          userId: user.id
        }
      });

      let updatedTicket = ticket;
      let statusChanged = false;
      let oldStatus = ticket.status;

      // Ha admin válaszol, állítsuk a ticket státuszát "in_progress"-re
      if (user.id === '11383db6-ab6e-4810-81a9-dc5ac1426d3a' && ticket.status === 'open') {
        updatedTicket = await prisma.supportTicket.update({
          where: { id },
          data: {
            status: 'in_progress'
          }
        });
        statusChanged = true;
      }

      // Ha a felhasználó válaszol egy "in_progress" ticketre, állítsuk vissza "open"-re
      if (user.id !== '11383db6-ab6e-4810-81a9-dc5ac1426d3a' && ticket.status === 'in_progress') {
        updatedTicket = await prisma.supportTicket.update({
          where: { id },
          data: {
            status: 'open'
          }
        });
        statusChanged = true;
      }

      return { message, updatedTicket, statusChanged, oldStatus };
    });

    // E-mail értesítés küldése a ticket tulajdonosának, ha nem ő küldte az üzenetet
    try {
      // Ticket tulajdonosának lekérése
      const ticketOwner = await prisma.user.findUnique({
        where: { id: ticket.creatorId }
      });

      // Ha nem a tulajdonos küldte az üzenetet, értesítsük őt
      if (ticketOwner && ticketOwner.id !== user.id) {
        await sendNewMessageEmail(result.updatedTicket, result.message, ticketOwner);
        console.log(`Új üzenet értesítés elküldve a ticket tulajdonosának: ${ticketOwner.email}`);
      }

      // Ha a státusz megváltozott, küldjünk erről is értesítést
      if (result.statusChanged) {
        await sendStatusChangeEmail(result.updatedTicket, ticketOwner, result.oldStatus);
        console.log(`Státusz változás értesítés elküldve a ticket tulajdonosának: ${ticketOwner.email}`);
      }
    } catch (emailError) {
      console.error('Hiba az e-mail küldésekor:', emailError);
      // Az e-mail küldési hiba nem akadályozza meg az üzenet küldését
    }

    res.status(201).json({
      success: true,
      data: result.message
    });
  } catch (err) {
    next(err);
  }
};

// @desc    Ticket státuszának módosítása (csak adminoknak)
// @route   PATCH /api/support/admin/tickets/:id
// @access  Admin
export const updateTicketStatus = async (req, res, next) => {
  try {
    const { id } = req.params;
    const { status, priority } = req.body;
    const user = req.user;

    // Ellenőrizzük, hogy a felhasználó a megadott ID-val rendelkezik-e
    if (user.id !== '11383db6-ab6e-4810-81a9-dc5ac1426d3a') {
      return res.status(403).json({
        success: false,
        message: 'Nincs jogosultságod ehhez a művelethez'
      });
    }
    console.log('Admin jogosultság érvényesítve az updateTicketStatus függvényben');

    // Ticket lekérdezése
    const ticket = await prisma.supportTicket.findUnique({
      where: { id }
    });

    if (!ticket) {
      return res.status(404).json({
        success: false,
        message: 'A megadott azonosítójú ticket nem található'
      });
    }

    // Frissítési adatok összeállítása
    const updateData = {};

    if (status) {
      updateData.status = status;

      // Ha a státusz "closed", állítsuk be a lezárás dátumát
      if (status === 'closed') {
        updateData.closedAt = new Date();
      } else {
        updateData.closedAt = null;
      }
    }

    if (priority) {
      updateData.priority = priority;
    }

    // Régi státusz mentése az értesítéshez
    const oldStatus = ticket.status;

    // Ticket frissítése
    const updatedTicket = await prisma.supportTicket.update({
      where: { id },
      data: updateData
    });

    // Ha a státusz megváltozott, küldjünk e-mail értesítést a ticket tulajdonosának
    if (status && status !== oldStatus) {
      try {
        // Ticket tulajdonosának lekérése
        const ticketOwner = await prisma.user.findUnique({
          where: { id: ticket.creatorId }
        });

        if (ticketOwner) {
          await sendStatusChangeEmail(updatedTicket, ticketOwner, oldStatus);
          console.log(`Státusz változás értesítés elküldve a ticket tulajdonosának: ${ticketOwner.email}`);
        }
      } catch (emailError) {
        console.error('Hiba az e-mail küldésekor:', emailError);
        // Az e-mail küldési hiba nem akadályozza meg a státusz frissítését
      }
    }

    res.status(200).json({
      success: true,
      data: updatedTicket
    });
  } catch (err) {
    next(err);
  }
};

// @desc    OpenAI válaszjavaslat kérése (csak adminoknak)
// @route   POST /api/support/admin/tickets/:id/ai-suggestion
// @access  Admin
export const getAISuggestion = async (req, res, next) => {
  try {
    const { id } = req.params;
    const user = req.user;

    // Ellenőrizzük, hogy a felhasználó a megadott ID-val rendelkezik-e
    if (user.id !== '11383db6-ab6e-4810-81a9-dc5ac1426d3a') {
      return res.status(403).json({
        success: false,
        message: 'Nincs jogosultságod ehhez a művelethez'
      });
    }
    console.log('Admin jogosultság érvényesítve a getAISuggestion függvényben');

    // Ticket lekérdezése az összes üzenettel
    const ticket = await prisma.supportTicket.findUnique({
      where: { id },
      include: {
        messages: {
          orderBy: {
            createdAt: 'asc'
          }
        },
        creator: {
          select: {
            name: true,
            email: true
          }
        }
      }
    });

    if (!ticket) {
      return res.status(404).json({
        success: false,
        message: 'A megadott azonosítójú ticket nem található'
      });
    }

    // Előző beszélgetés összeállítása az OpenAI számára
    const conversation = ticket.messages.map(msg => {
      const role = msg.isFromAdmin ? 'assistant' : 'user';
      return { role, content: msg.content };
    });

    // Rendszerüzenet hozzáadása
    const messages = [
      {
        role: 'system',
        content: `Te egy segítőkész ügyfélszolgálati asszisztens vagy a Magyar-Német Nyelvtanuló alkalmazásban.
                  A felhasználó neve: ${ticket.creator.name}, email: ${ticket.creator.email}.
                  A ticket tárgya: "${ticket.subject}".
                  Válaszolj udvariasan, informatívan és segítőkészen.
                  Használj magyar nyelvet, kivéve ha a felhasználó németül kérdez, akkor németül válaszolj.`
      },
      ...conversation,
      {
        role: 'user',
        content: 'Kérlek, javasolj egy megfelelő választ erre a support ticketre.'
      }
    ];

    // OpenAI API hívás
    let suggestion;

    try {
      if (!openai) {
        throw new Error('OpenAI nincs inicializálva');
      }

      const completion = await openai.chat.completions.create({
        model: 'gpt-3.5-turbo',
        messages: messages,
        max_tokens: 500,
        temperature: 0.7,
      });

      suggestion = completion.choices[0].message.content;
    } catch (error) {
      console.error('OpenAI API hiba:', error);
      suggestion = 'Az OpenAI API jelenleg nem elérhető. Kérjük, válaszolj manuálisan a felhasználó kérdésére.';
    }

    res.status(200).json({
      success: true,
      data: {
        suggestion
      }
    });
  } catch (err) {
    console.error('OpenAI API hiba:', err);

    // Ha az OpenAI API nem elérhető, küldjünk egy alapértelmezett választ
    if (err.response && err.response.status === 429) {
      return res.status(429).json({
        success: false,
        message: 'Az OpenAI API túlterhelés miatt nem elérhető. Kérjük, próbáld újra később.'
      });
    }

    next(err);
  }
};
