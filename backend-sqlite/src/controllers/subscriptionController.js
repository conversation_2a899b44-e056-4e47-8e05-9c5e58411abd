import prisma from '../utils/db.js';
import * as stripeUtils from '../utils/stripe.js';
import Stripe from 'stripe';

const stripe = new Stripe(process.env.STRIPE_SECRET_KEY);

// Havi előfizetési csomag adatai
export const MONTHLY_SUBSCRIPTION_PACKAGE = {
  productId: process.env.STRIPE_PRODUCT_ID || 'prod_SIVdw7ceeWtlkJ',
  name: '<PERSON><PERSON> előfizeté<PERSON>',
  price: 2999,
  monthlyPoints: 500
};

// @desc    Előfizetés létrehozása
// @route   POST /api/subscriptions
// @access  Private
export const createSubscription = async (req, res, next) => {
  try {
    const user = req.user;

    // Ellen<PERSON>zük, hogy a felhasználónak van-e már aktív előfizetése
    const existingSubscription = await prisma.subscription.findFirst({
      where: {
        userId: user.id,
        status: 'active',
        stripeCurrentPeriodEnd: {
          gt: new Date()
        }
      }
    });

    if (existingSubscription) {
      return res.status(400).json({
        success: false,
        message: '<PERSON><PERSON><PERSON> van aktív előfizetésed'
      });
    }

    // Checkout session létrehozása
    const session = await stripeUtils.createCheckoutSession(
      user.stripeCustomerId,
      process.env.STRIPE_PRODUCT_ID,
      `${process.env.FRONTEND_URL}/subscription/success?session_id={CHECKOUT_SESSION_ID}`,
      `${process.env.FRONTEND_URL}/subscription/cancel`
    );

    res.status(200).json({
      success: true,
      sessionId: session.id,
      url: session.url
    });
  } catch (err) {
    next(err);
  }
};

// @desc    Session ellenőrzése
// @route   GET /api/subscriptions/verify-session/:sessionId
// @access  Private
export const verifySession = async (req, res, next) => {
  try {
    const { sessionId } = req.params;
    const user = req.user;

    if (!sessionId) {
      return res.status(400).json({
        success: false,
        message: 'Session azonosító megadása kötelező'
      });
    }

    // Session lekérése a Stripe-tól
    const session = await stripe.checkout.sessions.retrieve(sessionId);

    // Ellenőrizzük, hogy a session a felhasználóhoz tartozik-e
    if (session.customer !== user.stripeCustomerId) {
      return res.status(403).json({
        success: false,
        message: 'Nincs jogosultságod ehhez a session-höz'
      });
    }

    // Ellenőrizzük, hogy a session sikeres-e
    if (session.payment_status !== 'paid') {
      return res.status(400).json({
        success: false,
        message: 'A fizetés még nem teljesült'
      });
    }

    // Előfizetés lekérése
    const subscription = await prisma.subscription.findFirst({
      where: {
        userId: user.id,
        stripeSubscriptionId: session.subscription
      }
    });

    // Ha nincs előfizetés, akkor létrehozzuk
    if (!subscription && session.subscription) {
      // Előfizetés lekérése a Stripe-tól
      const stripeSubscription = await stripe.subscriptions.retrieve(session.subscription);

      // Előfizetés mentése az adatbázisba
      // Biztosítsuk, hogy az előfizetés státusza 'active' legyen, ha a Stripe-ban 'trialing' vagy 'active'
      const status = ['trialing', 'active'].includes(stripeSubscription.status) ? 'active' : stripeSubscription.status;

      console.log('Új előfizetés létrehozása:', {
        userId: user.id,
        stripeSubscriptionId: stripeSubscription.id,
        stripePriceId: stripeSubscription.items.data[0].price.id,
        stripeCurrentPeriodEnd: new Date(stripeSubscription.current_period_end * 1000),
        status: status, // Módosított státusz
        cancelAtPeriodEnd: stripeSubscription.cancel_at_period_end
      });

      await prisma.subscription.create({
        data: {
          userId: user.id,
          stripeSubscriptionId: stripeSubscription.id,
          stripePriceId: stripeSubscription.items.data[0].price.id,
          stripeCurrentPeriodEnd: new Date(stripeSubscription.current_period_end * 1000),
          status: status, // Módosított státusz
          cancelAtPeriodEnd: stripeSubscription.cancel_at_period_end
        }
      });
    }

    return res.status(200).json({
      success: true,
      data: {
        sessionId: session.id,
        paymentStatus: session.payment_status,
        subscriptionId: session.subscription
      }
    });
  } catch (err) {
    console.error('Hiba a session ellenőrzésekor:', err);
    next(err);
  }
};

// @desc    Előfizetés állapotának lekérése
// @route   GET /api/subscriptions/status
// @access  Private
export const getSubscriptionStatus = async (req, res, next) => {
  try {
    const user = req.user;

    // Előfizetés lekérése
    const subscription = await prisma.subscription.findFirst({
      where: {
        userId: user.id
      },
      orderBy: {
        createdAt: 'desc'
      }
    });

    if (!subscription) {
      return res.status(200).json({
        success: true,
        data: {
          isActive: false,
          currentPeriodEnd: null,
          cancelAtPeriodEnd: false
        }
      });
    }

    // Ellenőrizzük, hogy az előfizetés aktív-e
    // Az 'active' és 'trialing' státuszok is aktívnak számítanak
    const isActive = (subscription.status === 'active' || subscription.status === 'trialing') &&
                     new Date(subscription.stripeCurrentPeriodEnd) > new Date();

    res.status(200).json({
      success: true,
      data: {
        isActive,
        currentPeriodEnd: subscription.stripeCurrentPeriodEnd,
        cancelAtPeriodEnd: subscription.cancelAtPeriodEnd,
        status: subscription.status
      }
    });
  } catch (err) {
    next(err);
  }
};

// @desc    Előfizetés lemondása
// @route   POST /api/subscriptions/cancel
// @access  Private
export const cancelSubscription = async (req, res, next) => {
  try {
    const user = req.user;

    // Előfizetés lekérése
    const subscription = await prisma.subscription.findFirst({
      where: {
        userId: user.id,
        status: 'active'
      },
      orderBy: {
        createdAt: 'desc'
      }
    });

    if (!subscription) {
      return res.status(404).json({
        success: false,
        message: 'Nincs aktív előfizetésed'
      });
    }

    // Előfizetés lemondása a Stripe-on
    const canceledSubscription = await stripeUtils.cancelSubscription(
      subscription.stripeSubscriptionId
    );

    // Előfizetés frissítése az adatbázisban
    await prisma.subscription.update({
      where: { id: subscription.id },
      data: {
        cancelAtPeriodEnd: true
      }
    });

    res.status(200).json({
      success: true,
      message: 'Előfizetésed sikeresen lemondva',
      data: {
        cancelAtPeriodEnd: true,
        currentPeriodEnd: subscription.stripeCurrentPeriodEnd
      }
    });
  } catch (err) {
    next(err);
  }
};

// @desc    Előfizetés újraaktiválása
// @route   POST /api/subscriptions/reactivate
// @access  Private
export const reactivateSubscription = async (req, res, next) => {
  try {
    const user = req.user;

    // Előfizetés lekérése
    const subscription = await prisma.subscription.findFirst({
      where: {
        userId: user.id,
        status: 'active',
        cancelAtPeriodEnd: true
      },
      orderBy: {
        createdAt: 'desc'
      }
    });

    if (!subscription) {
      return res.status(404).json({
        success: false,
        message: 'Nincs lemondott előfizetésed'
      });
    }

    // Előfizetés újraaktiválása a Stripe-on
    const reactivatedSubscription = await stripeUtils.reactivateSubscription(
      subscription.stripeSubscriptionId
    );

    // Előfizetés frissítése az adatbázisban
    await prisma.subscription.update({
      where: { id: subscription.id },
      data: {
        cancelAtPeriodEnd: false
      }
    });

    res.status(200).json({
      success: true,
      message: 'Előfizetésed sikeresen újraaktiválva',
      data: {
        cancelAtPeriodEnd: false,
        currentPeriodEnd: subscription.stripeCurrentPeriodEnd
      }
    });
  } catch (err) {
    next(err);
  }
};

// @desc    Stripe webhook kezelése
// @route   POST /api/subscriptions/webhook
// @access  Public
export const handleWebhook = async (req, res, next) => {
  try {
    const signature = req.headers['stripe-signature'];

    if (!signature) {
      console.error('Hiányzó Stripe aláírás');
      return res.status(400).json({
        success: false,
        message: 'Hiányzó Stripe aláírás'
      });
    }

    console.log('Webhook esemény érkezett, signature:', signature);
    console.log('Webhook body:', req.rawBody ? 'Van rawBody' : 'Nincs rawBody');

    // Webhook esemény ellenőrzése
    let event;
    try {
      event = stripe.webhooks.constructEvent(
        req.rawBody,
        signature,
        process.env.STRIPE_WEBHOOK_SECRET
      );
    } catch (err) {
      console.error('Webhook signature verification failed:', err.message);
      return res.status(400).json({ success: false, message: `Webhook Error: ${err.message}` });
    }

    console.log('Webhook esemény típusa:', event.type);

    // Esemény típusa alapján kezelés
    switch (event.type) {
      case 'checkout.session.completed':
        console.log('Checkout session completed esemény feldolgozása');
        await handleCheckoutSessionCompleted(event.data.object);
        break;
      case 'invoice.payment_succeeded':
        console.log('Invoice payment succeeded esemény feldolgozása');
        await handleInvoicePaymentSucceeded(event.data.object);
        break;
      case 'customer.subscription.updated':
        console.log('Customer subscription updated esemény feldolgozása');
        await handleSubscriptionUpdated(event.data.object);
        break;
      case 'customer.subscription.deleted':
        console.log('Customer subscription deleted esemény feldolgozása');
        await handleSubscriptionDeleted(event.data.object);
        break;
      default:
        console.log(`Nem kezelt esemény típus: ${event.type}`);
    }

    console.log('Webhook esemény sikeresen feldolgozva');
    res.status(200).json({ received: true });
  } catch (err) {
    console.error('Webhook hiba:', err);
    return res.status(400).json({
      success: false,
      message: `Webhook hiba: ${err.message}`
    });
  }
};

// Checkout session kezelése
const handleCheckoutSessionCompleted = async (session) => {
  try {
    // Felhasználó keresése
    const user = await prisma.user.findFirst({
      where: { stripeCustomerId: session.customer }
    });

    if (!user) {
      throw new Error(`Nem található felhasználó a következő Stripe ügyfél azonosítóval: ${session.customer}`);
    }

    // Előfizetés lekérése a Stripe-tól
    const subscription = await stripe.subscriptions.retrieve(session.subscription);

    // Előfizetés mentése az adatbázisba
    // Biztosítsuk, hogy az előfizetés státusza 'active' legyen, ha a Stripe-ban 'trialing' vagy 'active'
    const status = ['trialing', 'active'].includes(subscription.status) ? 'active' : subscription.status;

    console.log('Webhook: Új előfizetés létrehozása:', {
      userId: user.id,
      stripeSubscriptionId: subscription.id,
      stripePriceId: subscription.items.data[0].price.id,
      stripeCurrentPeriodEnd: new Date(subscription.current_period_end * 1000),
      status: status, // Módosított státusz
      cancelAtPeriodEnd: subscription.cancel_at_period_end
    });

    await prisma.subscription.create({
      data: {
        userId: user.id,
        stripeSubscriptionId: subscription.id,
        stripePriceId: subscription.items.data[0].price.id,
        stripeCurrentPeriodEnd: new Date(subscription.current_period_end * 1000),
        status: status, // Módosított státusz
        cancelAtPeriodEnd: subscription.cancel_at_period_end
      }
    });

    // Fizetés mentése az adatbázisba
    await prisma.payment.create({
      data: {
        userId: user.id,
        stripePaymentIntentId: session.payment_intent,
        stripeCustomerId: session.customer,
        amount: session.amount_total,
        currency: session.currency,
        status: 'succeeded',
        description: 'Előfizetés'
      }
    });

    // Ellenőrizzük, hogy a termék azonosító megegyezik-e a havi előfizetési csomag azonosítójával
    const price = await stripe.prices.retrieve(subscription.items.data[0].price.id, {
      expand: ['product']
    });
    const productId = price.product.id;
    const productName = price.product.name;

    console.log('Előfizetés termék azonosító:', productId);
    console.log('Előfizetés termék neve:', productName);
    console.log('Havi előfizetési csomag azonosító:', MONTHLY_SUBSCRIPTION_PACKAGE.productId);
    console.log('Környezeti változó STRIPE_PRODUCT_ID:', process.env.STRIPE_PRODUCT_ID);
    console.log('Egyezés ellenőrzése (azonosító):', productId === MONTHLY_SUBSCRIPTION_PACKAGE.productId);
    console.log('Egyezés ellenőrzése (név):', productName && (productName.toLowerCase().includes('havi') || productName.toLowerCase().includes('monthly')));

    // Ellenőrizzük, hogy a termék azonosító megegyezik-e a havi előfizetési csomag azonosítójával
    // vagy a termék neve tartalmazza-e a "havi" vagy "monthly" szót
    if (productId === MONTHLY_SUBSCRIPTION_PACKAGE.productId ||
        (productName &&
         (productName.toLowerCase().includes('havi') ||
          productName.toLowerCase().includes('monthly')))) {
      console.log('Havi előfizetési csomag aktiválva, pontok hozzáadása:', MONTHLY_SUBSCRIPTION_PACKAGE.monthlyPoints);

      // Pontok hozzáadása a felhasználóhoz
      await prisma.user.update({
        where: { id: user.id },
        data: { points: { increment: MONTHLY_SUBSCRIPTION_PACKAGE.monthlyPoints } }
      });

      // Tranzakció rögzítése
      await prisma.pointTransaction.create({
        data: {
          userId: user.id,
          amount: MONTHLY_SUBSCRIPTION_PACKAGE.monthlyPoints,
          type: 'subscription',
          description: 'Havi előfizetés aktiválása'
        }
      });

      console.log('Pontok sikeresen hozzáadva a havi előfizetéshez:', user.id, MONTHLY_SUBSCRIPTION_PACKAGE.monthlyPoints);
    } else {
      // Az ingyenes próba csomag már ad 15 pontot, így nem adunk plusz pontot az első előfizetéskor
      console.log('Nem havi előfizetési csomag, nem adunk plusz pontot:', user.id);
    }
  } catch (error) {
    console.error('Hiba a checkout session feldolgozásakor:', error);
    throw error;
  }
};

// Számla fizetés kezelése
const handleInvoicePaymentSucceeded = async (invoice) => {
  try {
    // Csak előfizetéshez kapcsolódó számlákat kezelünk
    if (!invoice.subscription) {
      return;
    }

    // Előfizetés lekérése a Stripe-tól
    const subscription = await stripe.subscriptions.retrieve(invoice.subscription);

    // Felhasználó keresése
    const user = await prisma.user.findFirst({
      where: { stripeCustomerId: invoice.customer }
    });

    if (!user) {
      throw new Error(`Nem található felhasználó a következő Stripe ügyfél azonosítóval: ${invoice.customer}`);
    }

    // Előfizetés keresése az adatbázisban
    const existingSubscription = await prisma.subscription.findFirst({
      where: { stripeSubscriptionId: subscription.id }
    });

    if (existingSubscription) {
      // Előfizetés frissítése
      await prisma.subscription.update({
        where: { id: existingSubscription.id },
        data: {
          stripeCurrentPeriodEnd: new Date(subscription.current_period_end * 1000),
          status: subscription.status,
          cancelAtPeriodEnd: subscription.cancel_at_period_end
        }
      });
    } else {
      // Új előfizetés létrehozása
      // Biztosítsuk, hogy az előfizetés státusza 'active' legyen, ha a Stripe-ban 'trialing' vagy 'active'
      const status = ['trialing', 'active'].includes(subscription.status) ? 'active' : subscription.status;

      console.log('Számla fizetés: Új előfizetés létrehozása:', {
        userId: user.id,
        stripeSubscriptionId: subscription.id,
        stripePriceId: subscription.items.data[0].price.id,
        stripeCurrentPeriodEnd: new Date(subscription.current_period_end * 1000),
        status: status, // Módosított státusz
        cancelAtPeriodEnd: subscription.cancel_at_period_end
      });

      await prisma.subscription.create({
        data: {
          userId: user.id,
          stripeSubscriptionId: subscription.id,
          stripePriceId: subscription.items.data[0].price.id,
          stripeCurrentPeriodEnd: new Date(subscription.current_period_end * 1000),
          status: status, // Módosított státusz
          cancelAtPeriodEnd: subscription.cancel_at_period_end
        }
      });
    }

    // Fizetés mentése az adatbázisba
    if (invoice.payment_intent) {
      await prisma.payment.create({
        data: {
          userId: user.id,
          stripePaymentIntentId: invoice.payment_intent,
          stripeCustomerId: invoice.customer,
          amount: invoice.amount_paid,
          currency: invoice.currency,
          status: 'succeeded',
          description: 'Előfizetés megújítás'
        }
      });
    }

    // Ellenőrizzük, hogy a termék azonosító megegyezik-e a havi előfizetési csomag azonosítójával
    const price = await stripe.prices.retrieve(subscription.items.data[0].price.id, {
      expand: ['product']
    });
    const productId = price.product.id;
    const productName = price.product.name;

    console.log('Előfizetés megújítás termék azonosító:', productId);
    console.log('Előfizetés megújítás termék neve:', productName);
    console.log('Havi előfizetési csomag azonosító:', MONTHLY_SUBSCRIPTION_PACKAGE.productId);
    console.log('Környezeti változó STRIPE_PRODUCT_ID:', process.env.STRIPE_PRODUCT_ID);
    console.log('Egyezés ellenőrzése (azonosító):', productId === MONTHLY_SUBSCRIPTION_PACKAGE.productId);
    console.log('Egyezés ellenőrzése (név):', productName && (productName.toLowerCase().includes('havi') || productName.toLowerCase().includes('monthly')));

    // Ellenőrizzük, hogy a termék azonosító megegyezik-e a havi előfizetési csomag azonosítójával
    // vagy a termék neve tartalmazza-e a "havi" vagy "monthly" szót
    if (productId === MONTHLY_SUBSCRIPTION_PACKAGE.productId ||
        (productName &&
         (productName.toLowerCase().includes('havi') ||
          productName.toLowerCase().includes('monthly')))) {
      console.log('Havi előfizetési csomag megújítva, pontok hozzáadása:', MONTHLY_SUBSCRIPTION_PACKAGE.monthlyPoints);

      // Pontok hozzáadása a felhasználóhoz
      await prisma.user.update({
        where: { id: user.id },
        data: { points: { increment: MONTHLY_SUBSCRIPTION_PACKAGE.monthlyPoints } }
      });

      // Tranzakció rögzítése
      await prisma.pointTransaction.create({
        data: {
          userId: user.id,
          amount: MONTHLY_SUBSCRIPTION_PACKAGE.monthlyPoints,
          type: 'subscription',
          description: 'Havi előfizetés megújítása'
        }
      });

      console.log('Pontok sikeresen hozzáadva a havi előfizetés megújításához:', user.id, MONTHLY_SUBSCRIPTION_PACKAGE.monthlyPoints);
    } else {
      // Nem havi előfizetési csomag, nem adunk plusz pontot
      console.log('Nem havi előfizetési csomag megújítása, nem adunk plusz pontot:', user.id);
    }
  } catch (error) {
    console.error('Hiba a számla fizetés feldolgozásakor:', error);
    throw error;
  }
};

// Előfizetés frissítés kezelése
const handleSubscriptionUpdated = async (subscription) => {
  try {
    // Felhasználó keresése
    const user = await prisma.user.findFirst({
      where: { stripeCustomerId: subscription.customer }
    });

    if (!user) {
      throw new Error(`Nem található felhasználó a következő Stripe ügyfél azonosítóval: ${subscription.customer}`);
    }

    // Előfizetés keresése az adatbázisban
    const existingSubscription = await prisma.subscription.findFirst({
      where: { stripeSubscriptionId: subscription.id }
    });

    if (existingSubscription) {
      // Előfizetés frissítése
      // Biztosítsuk, hogy az előfizetés státusza 'active' legyen, ha a Stripe-ban 'trialing' vagy 'active'
      const status = ['trialing', 'active'].includes(subscription.status) ? 'active' : subscription.status;

      console.log('Előfizetés frissítése:', {
        id: existingSubscription.id,
        stripeCurrentPeriodEnd: new Date(subscription.current_period_end * 1000),
        status: status, // Módosított státusz
        cancelAtPeriodEnd: subscription.cancel_at_period_end
      });

      await prisma.subscription.update({
        where: { id: existingSubscription.id },
        data: {
          stripeCurrentPeriodEnd: new Date(subscription.current_period_end * 1000),
          status: status, // Módosított státusz
          cancelAtPeriodEnd: subscription.cancel_at_period_end
        }
      });
    }
  } catch (error) {
    console.error('Hiba az előfizetés frissítés feldolgozásakor:', error);
    throw error;
  }
};

// Előfizetés törlés kezelése
const handleSubscriptionDeleted = async (subscription) => {
  try {
    // Előfizetés keresése az adatbázisban
    const existingSubscription = await prisma.subscription.findFirst({
      where: { stripeSubscriptionId: subscription.id }
    });

    if (existingSubscription) {
      // Előfizetés frissítése
      await prisma.subscription.update({
        where: { id: existingSubscription.id },
        data: {
          status: 'inactive',
          cancelAtPeriodEnd: false
        }
      });
    }
  } catch (error) {
    console.error('Hiba az előfizetés törlés feldolgozásakor:', error);
    throw error;
  }
};

// @desc    Számlák lekérése
// @route   GET /api/subscriptions/invoices
// @access  Private
export const getInvoices = async (req, res, next) => {
  try {
    const user = req.user;

    // Ellenőrizzük, hogy a felhasználónak van-e Stripe ügyfél azonosítója
    if (!user.stripeCustomerId) {
      return res.status(400).json({
        success: false,
        message: 'Nincs Stripe ügyfél azonosítód'
      });
    }

    // Számlák lekérése a Stripe-tól
    const invoices = await stripe.invoices.list({
      customer: user.stripeCustomerId,
      limit: 10,
      expand: ['data.subscription']
    });

    // Fizetések lekérése az adatbázisból
    const payments = await prisma.payment.findMany({
      where: {
        userId: user.id
      },
      orderBy: {
        createdAt: 'desc'
      }
    });

    // Előfizetés lekérése
    const subscription = await prisma.subscription.findFirst({
      where: {
        userId: user.id,
        status: 'active'
      },
      orderBy: {
        createdAt: 'desc'
      }
    });

    // Stripe előfizetés lekérése, ha van
    let stripeSubscription = null;
    if (subscription && subscription.stripeSubscriptionId) {
      try {
        stripeSubscription = await stripe.subscriptions.retrieve(
          subscription.stripeSubscriptionId,
          {
            expand: ['default_payment_method', 'items.data.price.product']
          }
        );
      } catch (error) {
        console.error('Hiba a Stripe előfizetés lekérésekor:', error);
      }
    }

    // Válasz küldése
    res.status(200).json({
      success: true,
      data: {
        invoices: invoices.data.map(invoice => ({
          id: invoice.id,
          number: invoice.number,
          amount: invoice.amount_paid,
          currency: invoice.currency,
          status: invoice.status,
          created: new Date(invoice.created * 1000),
          periodStart: invoice.period_start ? new Date(invoice.period_start * 1000) : null,
          periodEnd: invoice.period_end ? new Date(invoice.period_end * 1000) : null,
          pdfUrl: invoice.invoice_pdf,
          hostedInvoiceUrl: invoice.hosted_invoice_url
        })),
        payments: payments.map(payment => ({
          id: payment.id,
          amount: payment.amount,
          currency: payment.currency,
          status: payment.status,
          description: payment.description,
          created: payment.createdAt
        })),
        subscription: subscription ? {
          id: subscription.id,
          status: subscription.status,
          currentPeriodEnd: subscription.stripeCurrentPeriodEnd,
          cancelAtPeriodEnd: subscription.cancelAtPeriodEnd,
          stripeDetails: stripeSubscription ? {
            id: stripeSubscription.id,
            status: stripeSubscription.status,
            currentPeriodStart: new Date(stripeSubscription.current_period_start * 1000),
            currentPeriodEnd: new Date(stripeSubscription.current_period_end * 1000),
            cancelAtPeriodEnd: stripeSubscription.cancel_at_period_end,
            canceledAt: stripeSubscription.canceled_at ? new Date(stripeSubscription.canceled_at * 1000) : null,
            startDate: new Date(stripeSubscription.start_date * 1000),
            trialEnd: stripeSubscription.trial_end ? new Date(stripeSubscription.trial_end * 1000) : null,
            paymentMethod: stripeSubscription.default_payment_method ? {
              brand: stripeSubscription.default_payment_method.card.brand,
              last4: stripeSubscription.default_payment_method.card.last4,
              expMonth: stripeSubscription.default_payment_method.card.exp_month,
              expYear: stripeSubscription.default_payment_method.card.exp_year
            } : null,
            items: stripeSubscription.items.data.map(item => ({
              id: item.id,
              priceId: item.price.id,
              productId: item.price.product.id,
              productName: item.price.product.name,
              amount: item.price.unit_amount,
              currency: item.price.currency,
              interval: item.price.recurring ? item.price.recurring.interval : null,
              intervalCount: item.price.recurring ? item.price.recurring.interval_count : null
            }))
          } : null
        } : null
      }
    });
  } catch (err) {
    console.error('Hiba a számlák lekérésekor:', err);
    next(err);
  }
};

// @desc    Manuális pontfeltöltés a havi előfizetéshez
// @route   POST /api/subscriptions/add-points
// @access  Private
export const addSubscriptionPoints = async (req, res, next) => {
  try {
    const user = req.user;

    // Ellenőrizzük, hogy a felhasználónak van-e aktív előfizetése
    const subscription = await prisma.subscription.findFirst({
      where: {
        userId: user.id,
        status: 'active',
        stripeCurrentPeriodEnd: {
          gt: new Date()
        }
      }
    });

    if (!subscription) {
      return res.status(400).json({
        success: false,
        message: 'Nincs aktív előfizetésed'
      });
    }

    // Ellenőrizzük, hogy a felhasználó nem kapott-e már pontokat az elmúlt 24 órában
    const recentTransaction = await prisma.pointTransaction.findFirst({
      where: {
        userId: user.id,
        type: 'subscription',
        createdAt: {
          gt: new Date(Date.now() - 24 * 60 * 60 * 1000) // 24 órával ezelőtti időpont
        }
      }
    });

    if (recentTransaction) {
      return res.status(400).json({
        success: false,
        message: 'Már kaptál pontokat az elmúlt 24 órában'
      });
    }

    // Pontok hozzáadása a felhasználóhoz
    await prisma.user.update({
      where: { id: user.id },
      data: { points: { increment: MONTHLY_SUBSCRIPTION_PACKAGE.monthlyPoints } }
    });

    // Tranzakció rögzítése
    const transaction = await prisma.pointTransaction.create({
      data: {
        userId: user.id,
        amount: MONTHLY_SUBSCRIPTION_PACKAGE.monthlyPoints,
        type: 'subscription',
        description: 'Havi előfizetés pontok manuális hozzáadása'
      }
    });

    res.status(200).json({
      success: true,
      message: 'Pontok sikeresen hozzáadva',
      data: {
        points: user.points + MONTHLY_SUBSCRIPTION_PACKAGE.monthlyPoints,
        transaction
      }
    });
  } catch (err) {
    console.error('Hiba a pontok hozzáadásakor:', err);
    next(err);
  }
};

// @desc    Számla lekérése
// @route   GET /api/subscriptions/invoices/:invoiceId
// @access  Private
export const getInvoice = async (req, res, next) => {
  try {
    const { invoiceId } = req.params;
    const user = req.user;

    // Számla lekérése a Stripe-tól
    const invoice = await stripe.invoices.retrieve(invoiceId);

    // Ellenőrizzük, hogy a számla a felhasználóhoz tartozik-e
    if (invoice.customer !== user.stripeCustomerId) {
      return res.status(403).json({
        success: false,
        message: 'Nincs jogosultságod ehhez a számlához'
      });
    }

    // Válasz küldése
    res.status(200).json({
      success: true,
      data: {
        id: invoice.id,
        number: invoice.number,
        amount: invoice.amount_paid,
        currency: invoice.currency,
        status: invoice.status,
        created: new Date(invoice.created * 1000),
        periodStart: invoice.period_start ? new Date(invoice.period_start * 1000) : null,
        periodEnd: invoice.period_end ? new Date(invoice.period_end * 1000) : null,
        pdfUrl: invoice.invoice_pdf,
        hostedInvoiceUrl: invoice.hosted_invoice_url
      }
    });
  } catch (err) {
    console.error('Hiba a számla lekérésekor:', err);
    next(err);
  }
};
