import nodemailer from 'nodemailer';
import dotenv from 'dotenv';

// Környezeti változók betöltése
dotenv.config();

// Nodemailer transporter létrehozása
const transporter = nodemailer.createTransport({
  host: process.env.SMTP_HOST || 'nb-hosting.hu',
  port: parseInt(process.env.SMTP_PORT || '25'),
  secure: process.env.SMTP_SECURE === 'true',
  auth: {
    user: process.env.SMTP_USER || '<EMAIL>',
    pass: process.env.SMTP_PASS || 'Atom.1993*'
  }
});

// Email küldése
export const sendEmail = async (options) => {
  try {
    // Email opciók
    const mailOptions = {
      from: `"Magyar-Német Nyelvtanuló" <${process.env.SMTP_USER || '<EMAIL>'}>`,
      to: options.to,
      subject: options.subject,
      html: options.html
    };

    // <PERSON>ail küld<PERSON>
    const info = await transporter.sendMail(mailOptions);
    console.log('Email elküldve:', info.messageId);
    return info;
  } catch (error) {
    console.error('Hiba az email küldésekor:', error);
    throw error;
  }
};

// Közös e-mail stílusok
const emailStyles = `
  body {
    font-family: Arial, sans-serif;
    line-height: 1.6;
    color: #333;
    max-width: 600px;
    margin: 0 auto;
    padding: 20px;
  }
  .header {
    background-color: #4F46E5;
    color: white;
    padding: 20px;
    text-align: center;
    border-radius: 5px 5px 0 0;
  }
  .content {
    padding: 20px;
    border: 1px solid #ddd;
    border-top: none;
    border-radius: 0 0 5px 5px;
  }
  .button {
    display: inline-block;
    background-color: #4F46E5;
    color: white;
    text-decoration: none;
    padding: 10px 20px;
    border-radius: 5px;
    margin: 20px 0;
  }
  .footer {
    margin-top: 20px;
    font-size: 12px;
    color: #666;
    text-align: center;
  }
  .ticket-info {
    background-color: #f5f5f5;
    padding: 15px;
    border-radius: 5px;
    margin: 15px 0;
  }
  .message-box {
    background-color: #f9f9f9;
    border-left: 4px solid #4F46E5;
    padding: 10px 15px;
    margin: 10px 0;
  }
  .status-badge {
    display: inline-block;
    padding: 3px 8px;
    border-radius: 3px;
    font-size: 12px;
    font-weight: bold;
  }
  .status-open {
    background-color: #e3f2fd;
    color: #0d47a1;
  }
  .status-in-progress {
    background-color: #fff8e1;
    color: #ff8f00;
  }
  .status-closed {
    background-color: #e8f5e9;
    color: #2e7d32;
  }
`;

// Meghívó email sablon
export const getInvitationEmailTemplate = (inviterName, invitationLink) => {
  return `
    <!DOCTYPE html>
    <html>
    <head>
      <meta charset="utf-8">
      <title>Meghívó a Magyar-Német Nyelvtanuló alkalmazásba</title>
      <style>
        ${emailStyles}
      </style>
    </head>
    <body>
      <div class="header">
        <h1>Meghívó a Magyar-Német Nyelvtanuló alkalmazásba</h1>
      </div>
      <div class="content">
        <p>Kedves Barátom!</p>
        <p><strong>${inviterName}</strong> meghívott téged a Magyar-Német Nyelvtanuló alkalmazásba.</p>
        <p>A Magyar-Német Nyelvtanuló egy hatékony eszköz a magyar és német nyelv tanulásához, amely segít a szókincs bővítésében, a nyelvtani ismeretek fejlesztésében és a beszédkészség javításában.</p>
        <p>A meghívó elfogadásához kattints az alábbi gombra:</p>
        <p style="text-align: center;">
          <a href="${invitationLink}" class="button">Meghívó elfogadása</a>
        </p>
        <p>Ha a gomb nem működik, másold be az alábbi linket a böngésződbe:</p>
        <p>${invitationLink}</p>
        <p>Üdvözlettel,<br>A Magyar-Német Nyelvtanuló csapata</p>
      </div>
      <div class="footer">
        <p>Ez egy automatikus üzenet, kérjük, ne válaszolj rá.</p>
      </div>
    </body>
    </html>
  `;
};

// Új ticket értesítő email sablon
export const getNewTicketEmailTemplate = (ticket, user) => {
  const ticketId = ticket.id.substring(0, 8);
  const priorityText = ticket.priority === 'low' ? 'Alacsony' :
                       ticket.priority === 'medium' ? 'Közepes' : 'Magas';

  return `
    <!DOCTYPE html>
    <html>
    <head>
      <meta charset="utf-8">
      <title>Új support ticket létrehozva</title>
      <style>
        ${emailStyles}
      </style>
    </head>
    <body>
      <div class="header">
        <h1>Új support ticket létrehozva</h1>
      </div>
      <div class="content">
        <p>Kedves ${user.name}!</p>
        <p>Sikeresen létrehoztál egy új support ticketet a Magyar-Német Nyelvtanuló alkalmazásban.</p>

        <div class="ticket-info">
          <p><strong>Ticket azonosító:</strong> #${ticketId}</p>
          <p><strong>Tárgy:</strong> ${ticket.subject}</p>
          <p><strong>Prioritás:</strong> ${priorityText}</p>
          <p><strong>Státusz:</strong> <span class="status-badge status-open">Nyitott</span></p>
        </div>

        <p>Ügyfélszolgálatunk hamarosan válaszol a kérdésedre. A ticket állapotát és a válaszokat a Magyar-Német Nyelvtanuló alkalmazás Support menüpontjában követheted nyomon.</p>

        <p>Üdvözlettel,<br>Magyar-Német Nyelvtanuló Csapat</p>
      </div>
      <div class="footer">
        <p>Ez egy automatikus üzenet, kérjük, ne válaszolj rá.</p>
      </div>
    </body>
    </html>
  `;
};

// Új üzenet értesítő email sablon
export const getNewMessageEmailTemplate = (ticket, message, user) => {
  const ticketId = ticket.id.substring(0, 8);
  const isAdminMessage = message.isFromAdmin;

  let statusClass = 'status-open';
  let statusText = 'Nyitott';

  if (ticket.status === 'in_progress') {
    statusClass = 'status-in-progress';
    statusText = 'Folyamatban';
  } else if (ticket.status === 'closed') {
    statusClass = 'status-closed';
    statusText = 'Lezárva';
  }

  return `
    <!DOCTYPE html>
    <html>
    <head>
      <meta charset="utf-8">
      <title>Új üzenet a support ticketedben</title>
      <style>
        ${emailStyles}
      </style>
    </head>
    <body>
      <div class="header">
        <h1>Új üzenet a support ticketedben</h1>
      </div>
      <div class="content">
        <p>Kedves ${user.name}!</p>
        <p>${isAdminMessage ? 'Ügyfélszolgálatunk válaszolt' : 'Új üzenetet kaptál'} a support ticketedben.</p>

        <div class="ticket-info">
          <p><strong>Ticket azonosító:</strong> #${ticketId}</p>
          <p><strong>Tárgy:</strong> ${ticket.subject}</p>
          <p><strong>Státusz:</strong> <span class="status-badge ${statusClass}">${statusText}</span></p>
          <p><strong>Új üzenet:</strong></p>
          <div class="message-box">
            ${message.content.replace(/\n/g, '<br>')}
          </div>
        </div>

        <p>A ticket részleteit és a teljes beszélgetést a Magyar-Német Nyelvtanuló alkalmazás Support menüpontjában tekintheted meg.</p>

        <p>Üdvözlettel,<br>Magyar-Német Nyelvtanuló Csapat</p>
      </div>
      <div class="footer">
        <p>Ez egy automatikus üzenet, kérjük, ne válaszolj rá.</p>
      </div>
    </body>
    </html>
  `;
};

// Ticket státusz változás értesítő email sablon
export const getStatusChangeEmailTemplate = (ticket, user, oldStatus) => {
  const ticketId = ticket.id.substring(0, 8);

  const getStatusText = (status) => {
    return status === 'open' ? 'Nyitott' :
           status === 'in_progress' ? 'Folyamatban' : 'Lezárva';
  };

  const getStatusClass = (status) => {
    return status === 'open' ? 'status-open' :
           status === 'in_progress' ? 'status-in-progress' : 'status-closed';
  };

  const oldStatusText = getStatusText(oldStatus);
  const newStatusText = getStatusText(ticket.status);
  const newStatusClass = getStatusClass(ticket.status);

  return `
    <!DOCTYPE html>
    <html>
    <head>
      <meta charset="utf-8">
      <title>Ticket státusza megváltozott</title>
      <style>
        ${emailStyles}
      </style>
    </head>
    <body>
      <div class="header">
        <h1>Ticket státusza megváltozott</h1>
      </div>
      <div class="content">
        <p>Kedves ${user.name}!</p>
        <p>A support ticketed státusza megváltozott.</p>

        <div class="ticket-info">
          <p><strong>Ticket azonosító:</strong> #${ticketId}</p>
          <p><strong>Tárgy:</strong> ${ticket.subject}</p>
          <p><strong>Régi státusz:</strong> ${oldStatusText}</p>
          <p><strong>Új státusz:</strong> <span class="status-badge ${newStatusClass}">${newStatusText}</span></p>
        </div>

        <p>A ticket részleteit és a teljes beszélgetést a Magyar-Német Nyelvtanuló alkalmazás Support menüpontjában tekintheted meg.</p>

        <p>Üdvözlettel,<br>Magyar-Német Nyelvtanuló Csapat</p>
      </div>
      <div class="footer">
        <p>Ez egy automatikus üzenet, kérjük, ne válaszolj rá.</p>
      </div>
    </body>
    </html>
  `;
};

// E-mail küldése új ticket létrehozásakor
export const sendNewTicketEmail = async (ticket, user) => {
  const subject = `[#${ticket.id.substring(0, 8)}] Új support ticket létrehozva`;
  const html = getNewTicketEmailTemplate(ticket, user);
  return sendEmail({ to: user.email, subject, html });
};

// E-mail küldése új üzenet esetén
export const sendNewMessageEmail = async (ticket, message, user) => {
  const subject = `[#${ticket.id.substring(0, 8)}] Új üzenet a support ticketedben`;
  const html = getNewMessageEmailTemplate(ticket, message, user);
  return sendEmail({ to: user.email, subject, html });
};

// E-mail küldése státusz változás esetén
export const sendStatusChangeEmail = async (ticket, user, oldStatus) => {
  const subject = `[#${ticket.id.substring(0, 8)}] Ticket státusza megváltozott`;
  const html = getStatusChangeEmailTemplate(ticket, user, oldStatus);
  return sendEmail({ to: user.email, subject, html });
};

export default {
  sendEmail,
  getInvitationEmailTemplate,
  getNewTicketEmailTemplate,
  getNewMessageEmailTemplate,
  getStatusChangeEmailTemplate,
  sendNewTicketEmail,
  sendNewMessageEmail,
  sendStatusChangeEmail
};
