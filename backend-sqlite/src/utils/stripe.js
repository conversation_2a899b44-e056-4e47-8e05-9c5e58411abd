import Stripe from 'stripe';
import dotenv from 'dotenv';

dotenv.config();

const stripe = new Stripe(process.env.STRIPE_SECRET_KEY);

// Stripe ügyfél létrehozása
export const createCustomer = async (email, name) => {
  try {
    const customer = await stripe.customers.create({
      email,
      name
    });

    return customer;
  } catch (error) {
    console.error('Stripe customer creation error:', error);
    throw new Error('Nem sikerült létrehozni a Stripe ügyfelet');
  }
};

// Fizetési szándék létrehozása
export const createPaymentIntent = async (amount, currency, customer, description) => {
  try {
    const paymentIntent = await stripe.paymentIntents.create({
      amount,
      currency,
      customer,
      description,
      automatic_payment_methods: {
        enabled: true
      }
    });

    return paymentIntent;
  } catch (error) {
    console.error('Stripe payment intent creation error:', error);
    throw new Error('Nem sikerült létrehozni a fizetési szándékot');
  }
};

// Előfizetés létrehozása
export const createSubscription = async (customerId, priceId) => {
  try {
    const subscription = await stripe.subscriptions.create({
      customer: customerId,
      items: [{ price: priceId }],
      payment_behavior: 'default_incomplete',
      expand: ['latest_invoice.payment_intent']
    });

    return subscription;
  } catch (error) {
    console.error('Stripe subscription creation error:', error);
    throw new Error('Nem sikerült létrehozni az előfizetést');
  }
};

// Előfizetés lemondása
export const cancelSubscription = async (subscriptionId) => {
  try {
    const subscription = await stripe.subscriptions.update(subscriptionId, {
      cancel_at_period_end: true
    });

    return subscription;
  } catch (error) {
    console.error('Stripe subscription cancellation error:', error);
    throw new Error('Nem sikerült lemondani az előfizetést');
  }
};

// Előfizetés újraaktiválása
export const reactivateSubscription = async (subscriptionId) => {
  try {
    const subscription = await stripe.subscriptions.update(subscriptionId, {
      cancel_at_period_end: false
    });

    return subscription;
  } catch (error) {
    console.error('Stripe subscription reactivation error:', error);
    throw new Error('Nem sikerült újraaktiválni az előfizetést');
  }
};

// Előfizetés lekérése
export const retrieveSubscription = async (subscriptionId) => {
  try {
    const subscription = await stripe.subscriptions.retrieve(subscriptionId);
    return subscription;
  } catch (error) {
    console.error('Stripe subscription retrieval error:', error);
    throw new Error('Nem sikerült lekérni az előfizetést');
  }
};

// Checkout session létrehozása
export const createCheckoutSession = async (customerId, productId, successUrl, cancelUrl, metadata = {}) => {
  try {
    // Lekérjük a termékhez tartozó árakat
    const prices = await stripe.prices.list({
      product: productId,
      active: true,
      limit: 1
    });

    if (prices.data.length === 0) {
      throw new Error('Nem található ár a termékhez');
    }

    const priceId = prices.data[0].id;
    const price = prices.data[0];

    // Ellenőrizzük, hogy egyszeri vagy ismétlődő fizetésről van-e szó
    const mode = price.type === 'recurring' ? 'subscription' : 'payment';

    // Session létrehozása
    const session = await stripe.checkout.sessions.create({
      customer: customerId,
      payment_method_types: ['card'],
      line_items: [
        {
          price: priceId,
          quantity: 1
        }
      ],
      mode: mode,
      success_url: successUrl,
      cancel_url: cancelUrl,
      metadata: metadata
    });

    return session;
  } catch (error) {
    console.error('Stripe checkout session creation error:', error);
    throw new Error('Nem sikerült létrehozni a checkout session-t');
  }
};

// Checkout session létrehozása egyedi árazással
export const createCustomCheckoutSession = async (customerId, productId, amount, successUrl, cancelUrl, metadata = {}, isRecurring = true) => {
  try {
    // Session létrehozása
    const session = await stripe.checkout.sessions.create({
      customer: customerId,
      payment_method_types: ['card'],
      line_items: [
        {
          price_data: {
            currency: 'huf',
            product: productId,
            ...(isRecurring ? {
              recurring: {
                interval: 'month'
              }
            } : {}),
            unit_amount: amount * 100 // Ft átváltva fillérre
          },
          quantity: 1
        }
      ],
      mode: isRecurring ? 'subscription' : 'payment',
      success_url: successUrl,
      cancel_url: cancelUrl,
      metadata: metadata
    });

    return session;
  } catch (error) {
    console.error('Stripe custom checkout session creation error:', error);
    throw new Error('Nem sikerült létrehozni az egyedi checkout session-t');
  }
};

// Webhook esemény ellenőrzése
export const constructEvent = (payload, signature) => {
  try {
    const event = stripe.webhooks.constructEvent(
      payload,
      signature,
      process.env.STRIPE_WEBHOOK_SECRET
    );
    return event;
  } catch (error) {
    console.error('Stripe webhook verification error:', error);
    throw new Error('Webhook signature verification failed');
  }
};

// Termék létrehozása vagy frissítése
export const createOrUpdateProduct = async (productId, name, description, metadata = {}) => {
  try {
    try {
      // Ellenőrizzük, hogy létezik-e már a termék
      const existingProduct = await stripe.products.retrieve(productId);

      // Ha létezik, frissítjük
      const product = await stripe.products.update(productId, {
        name,
        description,
        metadata
      });

      return product;
    } catch (error) {
      // Ha nem létezik, létrehozzuk
      const product = await stripe.products.create({
        id: productId,
        name,
        description,
        metadata
      });

      return product;
    }
  } catch (error) {
    console.error('Stripe product creation/update error:', error);
    throw new Error('Nem sikerült létrehozni vagy frissíteni a terméket');
  }
};

// Ár létrehozása vagy frissítése
export const createOrUpdatePrice = async (productId, amount, currency = 'huf', isRecurring = true, interval = 'month') => {
  try {
    // Lekérjük a termékhez tartozó aktív árakat
    const prices = await stripe.prices.list({
      product: productId,
      active: true
    });

    // Ha van már ár, inaktiváljuk
    for (const price of prices.data) {
      await stripe.prices.update(price.id, { active: false });
    }

    // Új ár létrehozása
    const price = await stripe.prices.create({
      product: productId,
      unit_amount: amount * 100, // Ft átváltva fillérre
      currency,
      ...(isRecurring ? {
        recurring: {
          interval
        }
      } : {})
    });

    return price;
  } catch (error) {
    console.error('Stripe price creation/update error:', error);
    throw new Error('Nem sikerült létrehozni vagy frissíteni az árat');
  }
};

export default {
  createCustomer,
  createPaymentIntent,
  createSubscription,
  cancelSubscription,
  reactivateSubscription,
  retrieveSubscription,
  createCheckoutSession,
  createCustomCheckoutSession,
  constructEvent,
  createOrUpdateProduct,
  createOrUpdatePrice
};
