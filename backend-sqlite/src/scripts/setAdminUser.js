import { PrismaClient } from '@prisma/client';
import dotenv from 'dotenv';

// Környezeti változók betöltése
dotenv.config();

const prisma = new PrismaClient();

async function setAdminUser() {
  try {
    // Admin felhasz<PERSON>l<PERSON> beállítása a megadott ID alapján
    const adminUserId = '11383db6-ab6e-4810-81a9-dc5ac1426d3a';
    
    // Ellen<PERSON>, hogy létezik-e a felhasználó
    const user = await prisma.user.findUnique({
      where: { id: adminUserId }
    });

    if (!user) {
      console.error(`Nem találhat<PERSON> felhas<PERSON>ló a következő ID-val: ${adminUserId}`);
      return;
    }

    // Admin jogosultság beállítása
    await prisma.user.update({
      where: { id: adminUserId },
      data: { isAdmin: true }
    });

    console.log(`Admin jogosultság sikeresen beállítva a következő felhasználónak: ${user.name} (${user.email})`);
  } catch (error) {
    console.error('Hiba az admin felhasználó beállításakor:', error);
  } finally {
    await prisma.$disconnect();
  }
}

// Script futtatása
setAdminUser();
