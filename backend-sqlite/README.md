# Magyar-Német Nyelvtanuló Backend (SQLite)

Ez a projekt a Magyar-Német Nyelvtanuló alkalmazás backend része, amely SQLite adatbázist használ MongoDB helyett.

## Telepítés

1. Telepítsd a függőségeket:

```bash
npm install
```

2. Inicializáld a Prisma klienst:

```bash
npx prisma generate
```

3. Hozd létre az adatbázist és a táblákat:

```bash
npx prisma migrate dev --name init
```

## Környezeti változók

A `.env` fájlban találhatók a környezeti változók. Ezeket a saját környezetedhez kell igazítanod:

```
# Database
DATABASE_URL="file:../magyar_nemet_nyelvtanulo.db"
DB_NAME="magyar_nemet_nyelvtanulo"

# Server
PORT=3000
NODE_ENV=development
FRONTEND_URL=http://localhost:8081

# JWT
JWT_SECRET=your_jwt_secret
JWT_EXPIRE=30d
JWT_COOKIE_EXPIRE=30

# Stripe
STRIPE_SECRET_KEY=your_stripe_secret_key
STRIPE_WEBHOOK_SECRET=your_stripe_webhook_secret
STRIPE_PRICE_ID=your_stripe_price_id
STRIPE_PRODUCT_ID=your_stripe_product_id
```

## Futtatás

Fejlesztői módban:

```bash
npm run dev
```

Produkciós módban:

```bash
npm start
```

## Adatbázis kezelése

Az adatbázis kezeléséhez használhatod a Prisma Studio-t:

```bash
npx prisma studio
```

Ez egy webes felületet nyit meg a `http://localhost:5555` címen, ahol kezelheted az adatbázist.

## API Dokumentáció

### Autentikáció

- `POST /api/auth/register` - Felhasználó regisztrálása
- `POST /api/auth/login` - Felhasználó bejelentkeztetése
- `GET /api/auth/logout` - Felhasználó kijelentkeztetése
- `GET /api/auth/me` - Aktuális felhasználó lekérése

### Előfizetés

- `POST /api/subscriptions` - Előfizetés létrehozása
- `GET /api/subscriptions/status` - Előfizetés állapotának lekérése
- `POST /api/subscriptions/cancel` - Előfizetés lemondása
- `POST /api/subscriptions/reactivate` - Előfizetés újraaktiválása
- `POST /api/subscriptions/webhook` - Stripe webhook kezelése
