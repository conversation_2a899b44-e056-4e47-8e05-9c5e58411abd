import dotenv from 'dotenv';
import Stripe from 'stripe';
import * as teacherService from '../src/services/teacherService.js';

// Környezeti változók betöltése
dotenv.config();

// Stripe inicializálása
const stripe = new Stripe(process.env.STRIPE_SECRET_KEY);

/**
 * Termék létrehozása vagy frissítése
 * @param {string} productId - Termék azonosítója
 * @param {string} name - Termék neve
 * @param {string} description - Termék leírása
 * @param {object} metadata - Termék metaadatai
 * @returns {Promise<object>} - Létrehozott vagy frissített termék
 */
const createOrUpdateProduct = async (productId, name, description, metadata = {}) => {
  try {
    try {
      // Ellenőrizzük, hogy létezik-e már a termék
      const existingProduct = await stripe.products.retrieve(productId);
      console.log(`A termék már létezik: ${existingProduct.id}`);
      
      // Ha létezik, frissítjük
      const product = await stripe.products.update(productId, {
        name,
        description,
        metadata
      });
      
      console.log(`Termék frissítve: ${product.id}`);
      return product;
    } catch (error) {
      // Ha nem létezik, létrehozzuk
      const product = await stripe.products.create({
        id: productId,
        name,
        description,
        metadata
      });
      
      console.log(`Új termék létrehozva: ${product.id}`);
      return product;
    }
  } catch (error) {
    console.error('Hiba a termék létrehozása/frissítése során:', error);
    throw error;
  }
};

/**
 * Ár létrehozása vagy frissítése
 * @param {string} productId - Termék azonosítója
 * @param {number} amount - Ár (Ft)
 * @param {string} currency - Pénznem
 * @param {boolean} isRecurring - Ismétlődő-e
 * @param {string} interval - Ismétlődés intervalluma
 * @returns {Promise<object>} - Létrehozott ár
 */
const createOrUpdatePrice = async (productId, amount, currency = 'huf', isRecurring = true, interval = 'month') => {
  try {
    // Lekérjük a termékhez tartozó aktív árakat
    const prices = await stripe.prices.list({
      product: productId,
      active: true
    });
    
    // Ha van már ár, inaktiváljuk
    for (const price of prices.data) {
      await stripe.prices.update(price.id, { active: false });
      console.log(`Régi ár inaktiválva: ${price.id}`);
    }
    
    // Új ár létrehozása
    const priceData = {
      product: productId,
      unit_amount: amount * 100, // Ft átváltva fillérre
      currency
    };
    
    // Ha ismétlődő, hozzáadjuk a recurring mezőt
    if (isRecurring) {
      priceData.recurring = { interval };
    }
    
    const price = await stripe.prices.create(priceData);
    console.log(`Új ár létrehozva: ${price.id} (${amount} ${currency})`);
    
    return price;
  } catch (error) {
    console.error('Hiba az ár létrehozása/frissítése során:', error);
    throw error;
  }
};

/**
 * Fő függvény
 */
const main = async () => {
  try {
    console.log('Stripe termékek létrehozása/frissítése...');
    
    // 1. Ingyenes próba csomag
    const freeTrialProduct = await createOrUpdateProduct(
      teacherService.FREE_TRIAL_PACKAGE.productId,
      'Ingyenes próba csomag',
      'Ingyenes próba csomag új felhasználóknak',
      {
        type: 'free_trial',
        points: teacherService.FREE_TRIAL_PACKAGE.points.toString()
      }
    );
    
    // Ingyenes próba csomag ára (0 Ft, egyszeri)
    await createOrUpdatePrice(
      freeTrialProduct.id,
      0,
      'huf',
      false // Nem ismétlődő
    );
    
    // 2. Tanári csomag
    const teacherProduct = await createOrUpdateProduct(
      teacherService.TEACHER_PACKAGE.productId,
      'Tanári csomag',
      'Tanári csomag max. 20 diák kezeléséhez',
      {
        type: 'teacher',
        monthlyPoints: teacherService.TEACHER_PACKAGE.monthlyPoints.toString(),
        maxStudents: teacherService.TEACHER_PACKAGE.maxStudents.toString(),
        studentGiftPoints: teacherService.TEACHER_PACKAGE.studentGiftPoints.toString()
      }
    );
    
    // Tanári csomag ára (7500 Ft/hó)
    await createOrUpdatePrice(
      teacherProduct.id,
      teacherService.TEACHER_PACKAGE.price,
      'huf',
      true, // Ismétlődő
      'month' // Havi
    );
    
    console.log('Stripe termékek sikeresen létrehozva/frissítve!');
  } catch (error) {
    console.error('Hiba a Stripe termékek létrehozása/frissítése során:', error);
    process.exit(1);
  }
};

// Fő függvény futtatása
main();
