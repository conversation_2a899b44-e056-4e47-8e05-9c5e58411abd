const Stripe = require('stripe');
require('dotenv').config();

const stripe = new Stripe(process.env.STRIPE_SECRET_KEY);

// Stripe ügyfél létrehozása
exports.createCustomer = async (email, name) => {
  try {
    const customer = await stripe.customers.create({
      email,
      name
    });

    return customer;
  } catch (error) {
    console.error('Stripe customer creation error:', error);
    throw new Error('Nem sikerült létrehozni a Stripe ügyfelet');
  }
};

// Fizetési szándék létrehozása
exports.createPaymentIntent = async (amount, currency, customer, description) => {
  try {
    const paymentIntent = await stripe.paymentIntents.create({
      amount,
      currency,
      customer,
      description,
      automatic_payment_methods: {
        enabled: true
      }
    });

    return paymentIntent;
  } catch (error) {
    console.error('Stripe payment intent creation error:', error);
    throw new Error('Nem sikerült létrehozni a fizetési szándékot');
  }
};

// Előfizetés létrehozása
exports.createSubscription = async (customerId, priceId) => {
  try {
    const subscription = await stripe.subscriptions.create({
      customer: customerId,
      items: [{ price: priceId }],
      payment_behavior: 'default_incomplete',
      expand: ['latest_invoice.payment_intent']
    });

    return subscription;
  } catch (error) {
    console.error('Stripe subscription creation error:', error);
    throw new Error('Nem sikerült létrehozni az előfizetést');
  }
};

// Előfizetés lemondása
exports.cancelSubscription = async (subscriptionId) => {
  try {
    const subscription = await stripe.subscriptions.update(subscriptionId, {
      cancel_at_period_end: true
    });

    return subscription;
  } catch (error) {
    console.error('Stripe subscription cancellation error:', error);
    throw new Error('Nem sikerült lemondani az előfizetést');
  }
};

// Előfizetés újraaktiválása
exports.reactivateSubscription = async (subscriptionId) => {
  try {
    const subscription = await stripe.subscriptions.update(subscriptionId, {
      cancel_at_period_end: false
    });

    return subscription;
  } catch (error) {
    console.error('Stripe subscription reactivation error:', error);
    throw new Error('Nem sikerült újraaktiválni az előfizetést');
  }
};

// Előfizetés lekérése
exports.retrieveSubscription = async (subscriptionId) => {
  try {
    const subscription = await stripe.subscriptions.retrieve(subscriptionId);
    return subscription;
  } catch (error) {
    console.error('Stripe subscription retrieval error:', error);
    throw new Error('Nem sikerült lekérni az előfizetést');
  }
};

// Checkout session létrehozása
exports.createCheckoutSession = async (customerId, priceId, successUrl, cancelUrl) => {
  try {
    const session = await stripe.checkout.sessions.create({
      customer: customerId,
      payment_method_types: ['card'],
      line_items: [
        {
          price: priceId,
          quantity: 1
        }
      ],
      mode: 'subscription',
      success_url: successUrl,
      cancel_url: cancelUrl
    });

    return session;
  } catch (error) {
    console.error('Stripe checkout session creation error:', error);
    throw new Error('Nem sikerült létrehozni a checkout session-t');
  }
};

// Webhook esemény ellenőrzése
exports.constructEvent = (payload, signature) => {
  try {
    const event = stripe.webhooks.constructEvent(
      payload,
      signature,
      process.env.STRIPE_WEBHOOK_SECRET
    );
    return event;
  } catch (error) {
    console.error('Stripe webhook verification error:', error);
    throw new Error('Webhook signature verification failed');
  }
};

module.exports = exports;
