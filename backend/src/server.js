const express = require('express');
const dotenv = require('dotenv');
const cors = require('cors');
const connectDB = require('./config/db');
const errorHandler = require('./middleware/error');

// Környezeti változók betöltése
dotenv.config();

// Express alkalmazás
const app = express();

// Adatbázis kapcsolat
const startServer = async () => {
  try {
    const dbPool = await connectDB();
    app.locals.db = dbPool;

    // Port
    const PORT = process.env.PORT || 5000;

    // Szerver indítása
    const server = app.listen(PORT, () => {
      console.log(`Server running on port ${PORT}`);
    });

    // Nem kezelt promise elutasítások kezelése
    process.on('unhandledRejection', (err) => {
      console.log(`Error: ${err.message}`);
      // Szerver leállítása
      server.close(() => process.exit(1));
    });
  } catch (error) {
    console.error('Hiba a szerver indításakor:', error);
    process.exit(1);
  }
};

// Body parser
app.use(express.json());

// CORS
app.use((req, res, next) => {
  res.header('Access-Control-Allow-Origin', process.env.FRONTEND_URL || 'http://localhost:8081');
  res.header('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
  res.header('Access-Control-Allow-Headers', 'Origin, X-Requested-With, Content-Type, Accept, Authorization');
  res.header('Access-Control-Allow-Credentials', 'true');

  // Handle preflight requests
  if (req.method === 'OPTIONS') {
    res.sendStatus(200);
  } else {
    next();
  }
});

// Útvonalak
app.use('/api/auth', require('./routes/authRoutes'));
app.use('/api/subscriptions', require('./routes/subscriptionRoutes'));

// Webhook útvonal külön kezelése
app.use('/api/subscriptions/webhook', express.raw({ type: 'application/json' }), (req, res, next) => {
  if (req.originalUrl === '/api/subscriptions/webhook') {
    req.rawBody = req.body;
    req.body = JSON.parse(req.body.toString());
  }
  next();
});

// Alapértelmezett útvonal
app.get('/', (req, res) => {
  res.json({ message: 'Magyar-Német Nyelvtanuló API' });
});

// Debug útvonal a 403 hiba teszteléséhez
app.all('/test', (req, res) => {
  console.log('Test útvonal elérve:', req.method, req.url, req.body);
  res.status(200).json({ success: true, message: 'Test sikeres', method: req.method });
});

// Hibakezelő
app.use(errorHandler);

// Szerver indítása
startServer();
