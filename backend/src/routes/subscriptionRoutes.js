const express = require('express');
const {
  createSubscription,
  getSubscriptionStatus,
  cancelSubscription,
  reactivateSubscription,
  handleWebhook
} = require('../controllers/subscriptionController');
const { protect } = require('../middleware/auth');

const router = express.Router();

// Webhook útvonal - nincs védve, és raw body-t vár
router.post('/webhook', (req, res, next) => {
  if (req.originalUrl === '/api/subscriptions/webhook') {
    // Express raw body parser már kiszolgálta ezt az útvonalat
    // a server.js-ben lévő konfiguráció miatt
    handleWebhook(req, res, next);
  } else {
    next();
  }
});

// Védett útvonalak
router.post('/', protect, createSubscription);
router.get('/status', protect, getSubscriptionStatus);
router.post('/cancel', protect, cancelSubscription);
router.post('/reactivate', protect, reactivateSubscription);

module.exports = router;
