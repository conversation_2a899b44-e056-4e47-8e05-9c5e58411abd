const express = require('express');
const { register, login, getMe, logout } = require('../controllers/authController');
const { protect } = require('../middleware/auth');

const router = express.Router();

// Regisztráció és bejelentkezés
router.post('/register', register);
router.post('/login', login);
router.get('/logout', logout);

// Védett útvonalak
router.get('/me', protect, getMe);

module.exports = router;
