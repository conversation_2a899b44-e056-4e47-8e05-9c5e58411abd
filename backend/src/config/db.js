const { Pool } = require('pg');
require('dotenv').config();

const connectDB = async () => {
  try {
    // PostgreSQL kapcsolat létrehozása
    const pool = new Pool({
      connectionString: process.env.POSTGRESQL_URI
    });

    // Kapcsolat tesztelése
    const client = await pool.connect();
    console.log(`PostgreSQL Connected: ${process.env.POSTGRESQL_URI.split('@')[1]}`);
    console.log(`Using database: ${process.env.DB_NAME}`);
    
    // Kliens f<PERSON>badítása, de a pool megmarad
    client.release();
    
    return pool;
  } catch (error) {
    console.error(`Error connecting to PostgreSQL: ${error.message}`);
    process.exit(1);
  }
};

module.exports = connectDB;