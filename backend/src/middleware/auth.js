const jwt = require('jsonwebtoken');

// <PERSON><PERSON><PERSON>t útvonalak
exports.protect = async (req, res, next) => {
  let token;

  // <PERSON><PERSON>zü<PERSON>, hogy a token létezik-e a header-ben
  if (
    req.headers.authorization &&
    req.headers.authorization.startsWith('Bearer')
  ) {
    // Token kinyerése a header-ből
    token = req.headers.authorization.split(' ')[1];
  } else if (req.cookies && req.cookies.token) {
    // Token kinyerése a cookie-ból
    token = req.cookies.token;
  }

  // Ellen<PERSON>rizzük, hogy a token létezik-e
  if (!token) {
    return res.status(401).json({
      success: false,
      message: 'Nincs hozzáférési jogosultsága ehhez az erőforráshoz'
    });
  }

  try {
    // Token dekódolása
    const decoded = jwt.verify(token, process.env.JWT_SECRET);

    // Felhasználó lekérése
    const pool = req.app.locals.db;
    const result = await pool.query('SELECT * FROM "User" WHERE id = $1', [decoded.id]);

    if (result.rows.length === 0) {
      return res.status(401).json({
        success: false,
        message: 'Nincs hozzáférési jogosultsága ehhez az erőforráshoz'
      });
    }

    req.user = result.rows[0];

    next();
  } catch (err) {
    return res.status(401).json({
      success: false,
      message: 'Nincs hozzáférési jogosultsága ehhez az erőforráshoz'
    });
  }
};

// Előfizetés ellenőrzése
exports.requireSubscription = async (req, res, next) => {
  try {
    // Ellenőrizzük, hogy a felhasználó létezik-e
    if (!req.user) {
      return res.status(401).json({
        success: false,
        message: 'Nincs hozzáférési jogosultsága ehhez az erőforráshoz'
      });
    }

    // Előfizetés lekérése
    const pool = req.app.locals.db;
    const result = await pool.query(
      'SELECT * FROM "Subscription" WHERE "userId" = $1 AND status = $2 AND "stripeCurrentPeriodEnd" > $3',
      [req.user.id, 'active', new Date()]
    );
    const subscription = result.rows[0];

    // Ellenőrizzük, hogy az előfizetés létezik-e és aktív-e
    if (!subscription) {
      return res.status(403).json({
        success: false,
        message: 'Ehhez a funkcióhoz aktív előfizetés szükséges'
      });
    }

    // Előfizetés hozzáadása a kéréshez
    req.subscription = subscription;

    next();
  } catch (err) {
    console.error('Hiba az előfizetés ellenőrzésekor:', err);
    return res.status(500).json({
      success: false,
      message: 'Szerver hiba történt'
    });
  }
};
