const { Pool } = require('pg');
const bcrypt = require('bcryptjs');
const jwt = require('jsonwebtoken');
const crypto = require('crypto');
const stripeUtils = require('../utils/stripe');

// JWT token generálása
const generateToken = (id) => {
  return jwt.sign({ id }, process.env.JWT_SECRET, {
    expiresIn: process.env.JWT_EXPIRE
  });
};

// @desc    Felhasználó regisztrálása
// @route   POST /api/auth/register
// @access  Public
exports.register = async (req, res, next) => {
  const pool = req.app.locals.db;

  try {
    console.log('Regisztrációs kérés érkezett:', req.body);

    const { name, email, password } = req.body;

    // Alapvető beviteli ellenőrzés
    if (!name || !email || !password) {
      console.log('<PERSON><PERSON><PERSON><PERSON><PERSON> mezők:', { name, email, password: password ? 'Megadva' : 'Hi<PERSON>yzi<PERSON>' });
      return res.status(400).json({
        success: false,
        message: '<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, adja meg a nevét, email címét és jelszavát'
      });
    }

    // Ellenőrizzük, hogy a felhasználó létezik-e már
    console.log('PostgreSQL felhasználó keresése:', email);
    const existingUser = await pool.query('SELECT * FROM "User" WHERE email = $1', [email]);
    console.log('Felhasználó található?', existingUser.rows.length > 0);

    if (existingUser.rows.length > 0) {
      return res.status(400).json({
        success: false,
        message: 'Ez az email cím már regisztrálva van'
      });
    }

    try {
      // Stripe ügyfél létrehozása
      console.log('Stripe ügyfél létrehozása:', email, name);
      const customer = await stripeUtils.createCustomer(email, name);
      console.log('Stripe ügyfél létrehozva:', customer.id);

      // Jelszó titkosítása
      const salt = await bcrypt.genSalt(10);
      const hashedPassword = await bcrypt.hash(password, salt);

      // Felhasználó létrehozása
      console.log('PostgreSQL felhasználó létrehozása');
      const result = await pool.query(
        'INSERT INTO "User" (id, name, email, password, "stripeCustomerId", points, "createdAt", "updatedAt") VALUES ($1, $2, $3, $4, $5, $6, $7, $8) RETURNING *',
        [crypto.randomUUID(), name, email, hashedPassword, customer.id, 1000, new Date(), new Date()]
      );
      const user = result.rows[0];
      console.log('Felhasználó létrehozva:', user.id);

      // Token generálása
      console.log('Token generálása és válasz küldése');
      sendTokenResponse(user, 201, res);
    } catch (stripeErr) {
      console.error('Hiba a Stripe vagy felhasználó létrehozásakor:', stripeErr);
      return res.status(500).json({
        success: false,
        message: 'Hiba a regisztráció során: ' + stripeErr.message
      });
    }
  } catch (err) {
    console.error('Általános hiba a regisztráció során:', err);
    res.status(500).json({
      success: false,
      message: 'Szerver hiba a regisztráció során',
      error: err.message
    });
  }
};

// @desc    Felhasználó bejelentkeztetése
// @route   POST /api/auth/login
// @access  Public
exports.login = async (req, res, next) => {
  const pool = req.app.locals.db;

  try {
    const { email, password } = req.body;

    // Ellenőrizzük, hogy az email és a jelszó meg van-e adva
    if (!email || !password) {
      return res.status(400).json({
        success: false,
        message: 'Kérjük, adja meg az email címét és jelszavát'
      });
    }

    // Felhasználó keresése
    const result = await pool.query('SELECT * FROM "User" WHERE email = $1', [email]);
    const user = result.rows[0];

    if (!user) {
      return res.status(401).json({
        success: false,
        message: 'Érvénytelen bejelentkezési adatok'
      });
    }

    // Jelszó ellenőrzése
    const isMatch = await bcrypt.compare(password, user.password);

    if (!isMatch) {
      return res.status(401).json({
        success: false,
        message: 'Érvénytelen bejelentkezési adatok'
      });
    }

    // Token generálása
    sendTokenResponse(user, 200, res);
  } catch (err) {
    next(err);
  }
};

// @desc    Aktuális felhasználó lekérése
// @route   GET /api/auth/me
// @access  Private
exports.getMe = async (req, res, next) => {
  const pool = req.app.locals.db;

  try {
    const result = await pool.query('SELECT id, name, email, "stripeCustomerId", "createdAt", "updatedAt" FROM "User" WHERE id = $1', [req.user.id]);
    const user = result.rows[0];

    if (!user) {
      return res.status(404).json({
        success: false,
        message: 'Felhasználó nem található'
      });
    }

    // Előfizetés lekérése
    const subscriptionResult = await pool.query(
      'SELECT * FROM "Subscription" WHERE "userId" = $1 AND status = $2 ORDER BY "createdAt" DESC LIMIT 1',
      [req.user.id, 'active']
    );
    const subscription = subscriptionResult.rows[0];

    res.status(200).json({
      success: true,
      data: {
        ...user,
        subscription: subscription || null
      }
    });
  } catch (err) {
    next(err);
  }
};

// @desc    Kijelentkezés / Token törlése
// @route   GET /api/auth/logout
// @access  Private
exports.logout = async (req, res, next) => {
  try {
    res.cookie('token', 'none', {
      expires: new Date(Date.now() + 10 * 1000),
      httpOnly: true
    });

    res.status(200).json({
      success: true,
      data: {}
    });
  } catch (err) {
    next(err);
  }
};

// Token küldése és cookie beállítása
const sendTokenResponse = (user, statusCode, res) => {
  // Token generálása
  const token = generateToken(user.id);

  const options = {
    expires: new Date(
      Date.now() + process.env.JWT_COOKIE_EXPIRE * 24 * 60 * 60 * 1000
    ),
    httpOnly: true
  };

  // Cookie beállítása
  res
    .status(statusCode)
    .cookie('token', token, options)
    .json({
      success: true,
      token,
      user: {
        id: user.id,
        name: user.name,
        email: user.email
      }
    });
};
