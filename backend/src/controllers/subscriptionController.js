const User = require('../models/User');
const Subscription = require('../models/Subscription');
const Payment = require('../models/Payment');
const stripeUtils = require('../utils/stripe');
const stripe = require('stripe')(process.env.STRIPE_SECRET_KEY);

// @desc    Előfizetés létrehozása
// @route   POST /api/subscriptions
// @access  Private
exports.createSubscription = async (req, res, next) => {
  try {
    const user = await User.findById(req.user.id);

    if (!user) {
      return res.status(404).json({
        success: false,
        message: '<PERSON>lhasználó nem található'
      });
    }

    // <PERSON>zük, hogy van-e már aktív előfizetése
    const existingSubscription = await Subscription.findOne({
      user: user._id,
      status: 'active'
    });

    if (existingSubscription) {
      return res.status(400).json({
        success: false,
        message: '<PERSON><PERSON><PERSON> van aktív előfizetésed'
      });
    }

    // Checkout session létrehozása
    const session = await stripeUtils.createCheckoutSession(
      user.stripeCustomerId,
      req.body.priceId || process.env.STRIPE_PRICE_ID,
      `${process.env.FRONTEND_URL}/subscription/success?session_id={CHECKOUT_SESSION_ID}`,
      `${process.env.FRONTEND_URL}/subscription/cancel`
    );

    res.status(200).json({
      success: true,
      sessionId: session.id,
      url: session.url
    });
  } catch (err) {
    next(err);
  }
};

// @desc    Előfizetés állapotának lekérése
// @route   GET /api/subscriptions/status
// @access  Private
exports.getSubscriptionStatus = async (req, res, next) => {
  try {
    const subscription = await Subscription.findOne({ user: req.user.id });

    if (!subscription) {
      return res.status(404).json({
        success: false,
        message: 'Nincs aktív előfizetésed'
      });
    }

    // Ellenőrizzük, hogy az előfizetés aktív-e
    const isActive = subscription.isActive();

    res.status(200).json({
      success: true,
      data: {
        id: subscription._id,
        status: subscription.status,
        currentPeriodEnd: subscription.stripeCurrentPeriodEnd,
        cancelAtPeriodEnd: subscription.cancelAtPeriodEnd,
        isActive
      }
    });
  } catch (err) {
    next(err);
  }
};

// @desc    Előfizetés lemondása
// @route   POST /api/subscriptions/cancel
// @access  Private
exports.cancelSubscription = async (req, res, next) => {
  try {
    const subscription = await Subscription.findOne({ user: req.user.id });

    if (!subscription) {
      return res.status(404).json({
        success: false,
        message: 'Nincs aktív előfizetésed'
      });
    }

    // Előfizetés lemondása a Stripe-ban
    await stripeUtils.cancelSubscription(subscription.stripeSubscriptionId);

    // Előfizetés frissítése az adatbázisban
    subscription.cancelAtPeriodEnd = true;
    await subscription.save();

    res.status(200).json({
      success: true,
      data: {
        id: subscription._id,
        status: subscription.status,
        currentPeriodEnd: subscription.stripeCurrentPeriodEnd,
        cancelAtPeriodEnd: subscription.cancelAtPeriodEnd
      }
    });
  } catch (err) {
    next(err);
  }
};

// @desc    Előfizetés újraaktiválása
// @route   POST /api/subscriptions/reactivate
// @access  Private
exports.reactivateSubscription = async (req, res, next) => {
  try {
    const subscription = await Subscription.findOne({ user: req.user.id });

    if (!subscription) {
      return res.status(404).json({
        success: false,
        message: 'Nincs előfizetésed'
      });
    }

    if (!subscription.cancelAtPeriodEnd) {
      return res.status(400).json({
        success: false,
        message: 'Az előfizetésed nem lett lemondva'
      });
    }

    // Előfizetés újraaktiválása a Stripe-ban
    await stripeUtils.reactivateSubscription(subscription.stripeSubscriptionId);

    // Előfizetés frissítése az adatbázisban
    subscription.cancelAtPeriodEnd = false;
    await subscription.save();

    res.status(200).json({
      success: true,
      data: {
        id: subscription._id,
        status: subscription.status,
        currentPeriodEnd: subscription.stripeCurrentPeriodEnd,
        cancelAtPeriodEnd: subscription.cancelAtPeriodEnd
      }
    });
  } catch (err) {
    next(err);
  }
};

// @desc    Webhook kezelése a Stripe-tól
// @route   POST /api/subscriptions/webhook
// @access  Public
exports.handleWebhook = async (req, res, next) => {
  const signature = req.headers['stripe-signature'];

  let event;

  try {
    event = stripeUtils.constructEvent(req.body, signature);
  } catch (err) {
    console.error(`Webhook signature verification failed: ${err.message}`);
    return res.status(400).send(`Webhook Error: ${err.message}`);
  }

  // Esemény kezelése
  switch (event.type) {
    case 'checkout.session.completed':
      await handleCheckoutSessionCompleted(event.data.object);
      break;
    case 'invoice.payment_succeeded':
      await handleInvoicePaymentSucceeded(event.data.object);
      break;
    case 'customer.subscription.updated':
      await handleSubscriptionUpdated(event.data.object);
      break;
    case 'customer.subscription.deleted':
      await handleSubscriptionDeleted(event.data.object);
      break;
    default:
      console.log(`Unhandled event type: ${event.type}`);
  }

  res.status(200).json({ received: true });
};

// Checkout session kezelése
const handleCheckoutSessionCompleted = async (session) => {
  try {
    if (session.mode !== 'subscription') return;

    // Felhasználó keresése a Stripe customer ID alapján
    const user = await User.findOne({ stripeCustomerId: session.customer });

    if (!user) {
      console.error(`No user found with Stripe customer ID: ${session.customer}`);
      return;
    }

    // Előfizetés lekérése a Stripe-tól
    const subscription = await stripe.subscriptions.retrieve(session.subscription);

    // Előfizetés mentése az adatbázisba
    await Subscription.create({
      user: user._id,
      stripeSubscriptionId: subscription.id,
      stripePriceId: subscription.items.data[0].price.id,
      stripeCurrentPeriodEnd: new Date(subscription.current_period_end * 1000),
      status: subscription.status,
      cancelAtPeriodEnd: subscription.cancel_at_period_end
    });

    // Fizetés mentése az adatbázisba
    await Payment.create({
      user: user._id,
      stripePaymentIntentId: session.payment_intent,
      stripeCustomerId: session.customer,
      amount: session.amount_total,
      currency: session.currency,
      status: 'succeeded',
      description: 'Előfizetés'
    });
  } catch (error) {
    console.error('Error handling checkout.session.completed:', error);
  }
};

// Invoice payment kezelése
const handleInvoicePaymentSucceeded = async (invoice) => {
  try {
    if (!invoice.subscription) return;

    // Előfizetés keresése
    const subscription = await Subscription.findOne({
      stripeSubscriptionId: invoice.subscription
    });

    if (!subscription) {
      console.error(`No subscription found with ID: ${invoice.subscription}`);
      return;
    }

    // Stripe előfizetés lekérése
    const stripeSubscription = await stripe.subscriptions.retrieve(invoice.subscription);

    // Előfizetés frissítése
    subscription.status = stripeSubscription.status;
    subscription.stripeCurrentPeriodEnd = new Date(stripeSubscription.current_period_end * 1000);
    await subscription.save();

    // Fizetés mentése
    if (invoice.payment_intent) {
      await Payment.create({
        user: subscription.user,
        stripePaymentIntentId: invoice.payment_intent,
        stripeCustomerId: invoice.customer,
        amount: invoice.amount_paid,
        currency: invoice.currency,
        status: 'succeeded',
        description: 'Előfizetés megújítás'
      });
    }
  } catch (error) {
    console.error('Error handling invoice.payment_succeeded:', error);
  }
};

// Előfizetés frissítés kezelése
const handleSubscriptionUpdated = async (stripeSubscription) => {
  try {
    // Előfizetés keresése
    const subscription = await Subscription.findOne({
      stripeSubscriptionId: stripeSubscription.id
    });

    if (!subscription) {
      console.error(`No subscription found with ID: ${stripeSubscription.id}`);
      return;
    }

    // Előfizetés frissítése
    subscription.status = stripeSubscription.status;
    subscription.stripeCurrentPeriodEnd = new Date(stripeSubscription.current_period_end * 1000);
    subscription.cancelAtPeriodEnd = stripeSubscription.cancel_at_period_end;
    await subscription.save();
  } catch (error) {
    console.error('Error handling customer.subscription.updated:', error);
  }
};

// Előfizetés törlés kezelése
const handleSubscriptionDeleted = async (stripeSubscription) => {
  try {
    // Előfizetés keresése
    const subscription = await Subscription.findOne({
      stripeSubscriptionId: stripeSubscription.id
    });

    if (!subscription) {
      console.error(`No subscription found with ID: ${stripeSubscription.id}`);
      return;
    }

    // Előfizetés frissítése
    subscription.status = 'canceled';
    await subscription.save();
  } catch (error) {
    console.error('Error handling customer.subscription.deleted:', error);
  }
};
