const express = require('express');
const cors = require('cors');

// Express alkalmazás
const app = express();

console.log('Szerver inicializálás...');

// Body parser
app.use(express.json());

// CORS
app.use(cors({ origin: "*" }));

// Tesztútvonalak
app.get('/', (req, res) => {
  console.log('GET / kérés érkezett');
  res.json({ message: 'Egyszerű API szerver működik' });
});

app.post('/register', (req, res) => {
  console.log('POST /register kérés érkezett:', req.body);
  res.json({ 
    success: true, 
    message: 'Regisztráció teszt sikeres', 
    data: req.body 
  });
});

// Port
const PORT = 3333;

// Szerver indítása
app.listen(PORT, () => {
  console.log(`Egyszerű szerver fut a ${PORT} porton`);
});