# Base image mindkét szolgáltatáshoz
FROM node:18-alpine as base

# OpenSSL, libc6-compat és nginx telepítése
RUN apk add --no-cache openssl libc6-compat nginx

# Környezeti változó beállítása a Prisma számára, hogy használja az OpenSSL 3.x-et
ENV PRISMA_CLI_QUERY_ENGINE_TYPE=binary
ENV PRISMA_CLIENT_ENGINE_TYPE=binary

WORKDIR /app

# Frontend build
FROM base as frontend-builder
COPY package*.json ./
RUN npm install
COPY . .
RUN npm run build

# Backend build
FROM base as backend-builder
WORKDIR /app/backend-sqlite
COPY backend-sqlite/package*.json ./
RUN npm install
COPY backend-sqlite .
RUN npx prisma generate

# Végső image
FROM base
WORKDIR /app

# G<PERSON><PERSON><PERSON><PERSON> csomagok telepítése
RUN npm install -g serve

# Backend másolása
COPY --from=backend-builder /app/backend-sqlite /app/backend-sqlite
# Frontend build másolása
COPY --from=frontend-builder /app/dist /app/dist

# Környezeti változók beállítása
ENV DATABASE_URL=****************************************************************
ENV JWT_SECRET=magyar_nemet_nyelvtanulo_jwt_secret_2024
ENV STRIPE_SECRET_KEY=sk_test_51RFbaGPDb2KbDM6GHsKuT21JVFmTGXhYcmKP1Xufcn6xUZzCQAZyIpDVQ7MhEbxR7Z2vuQjPlkr1c8mxlLzSwdi200JR9OqC7m
ENV STRIPE_WEBHOOK_SECRET=we_1RFqa0PDb2KbDM6GzgR5O969
ENV STRIPE_PRICE_ID=price_1RFbaGPDb2KbDM6G
ENV STRIPE_PRODUCT_ID=prod_S9vUbzJakxCBx9
ENV PORT=3000
ENV NODE_ENV=production
ENV FRONTEND_URL=https://digitalisnemet.hu
ENV VITE_API_URL=https://app.digitalisnemet.hu/api

# Portok
EXPOSE 3000 80

# Nginx konfiguráció másolása
COPY nginx.conf /etc/nginx/nginx.conf

# Start script másolása
COPY start-docker.sh /app/
RUN chmod +x /app/start-docker.sh

# Indítás
CMD ["/app/start-docker.sh"]