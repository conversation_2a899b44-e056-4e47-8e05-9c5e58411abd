# Apache konfiguráció React SPA alkalmazáshoz
# Ez a fájl biztosítja, hogy minden útvonal az index.html-re irányuljon

# Mod_rewrite engedélyezése
<IfModule mod_rewrite.c>
    RewriteEngine On
    
    # HTTPS átirányítás (opcionális)
    # RewriteCond %{HTTPS} off
    # RewriteRule ^(.*)$ https://%{HTTP_HOST}%{REQUEST_URI} [L,R=301]
    
    # Statikus fájlok kiszolgálása (CSS, JS, képek, stb.)
    RewriteCond %{REQUEST_FILENAME} -f [OR]
    RewriteCond %{REQUEST_FILENAME} -d
    RewriteRule ^ - [L]
    
    # API kérések továbbítása (ha szükséges)
    # RewriteRule ^api/(.*)$ https://app.digitalisnemet.hu/api/$1 [P,L]
    
    # Minden más útvonal az index.html-re irányítása (SPA routing)
    RewriteRule ^ index.html [L]
</IfModule>

# Fájl típusok és cache beállítások
<IfModule mod_expires.c>
    ExpiresActive on
    
    # HTML fájlok - ne cache-elj
    ExpiresByType text/html "access plus 0 seconds"
    
    # CSS és JavaScript fájlok - 1 év cache
    ExpiresByType text/css "access plus 1 year"
    ExpiresByType application/javascript "access plus 1 year"
    ExpiresByType text/javascript "access plus 1 year"
    
    # Képek - 1 hónap cache
    ExpiresByType image/png "access plus 1 month"
    ExpiresByType image/jpg "access plus 1 month"
    ExpiresByType image/jpeg "access plus 1 month"
    ExpiresByType image/gif "access plus 1 month"
    ExpiresByType image/svg+xml "access plus 1 month"
    ExpiresByType image/webp "access plus 1 month"
    
    # Fontok - 1 év cache
    ExpiresByType font/woff "access plus 1 year"
    ExpiresByType font/woff2 "access plus 1 year"
    ExpiresByType application/font-woff "access plus 1 year"
    ExpiresByType application/font-woff2 "access plus 1 year"
</IfModule>

# Gzip tömörítés
<IfModule mod_deflate.c>
    # HTML, CSS, JavaScript, Text, XML és fontok tömörítése
    AddOutputFilterByType DEFLATE text/plain
    AddOutputFilterByType DEFLATE text/html
    AddOutputFilterByType DEFLATE text/xml
    AddOutputFilterByType DEFLATE text/css
    AddOutputFilterByType DEFLATE application/xml
    AddOutputFilterByType DEFLATE application/xhtml+xml
    AddOutputFilterByType DEFLATE application/rss+xml
    AddOutputFilterByType DEFLATE application/javascript
    AddOutputFilterByType DEFLATE application/x-javascript
    AddOutputFilterByType DEFLATE application/json
</IfModule>

# Biztonsági fejlécek
<IfModule mod_headers.c>
    # X-Frame-Options
    Header always set X-Frame-Options "SAMEORIGIN"
    
    # X-Content-Type-Options
    Header always set X-Content-Type-Options "nosniff"
    
    # X-XSS-Protection
    Header always set X-XSS-Protection "1; mode=block"
    
    # Referrer Policy
    Header always set Referrer-Policy "strict-origin-when-cross-origin"
    
    # Content Security Policy (alapértelmezett - testreszabható)
    # Header always set Content-Security-Policy "default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline'; img-src 'self' data: https:; font-src 'self' data:; connect-src 'self' https://app.digitalisnemet.hu;"
</IfModule>

# Fájl hozzáférés korlátozása
<Files ".htaccess">
    Order allow,deny
    Deny from all
</Files>

# Környezeti fájlok védelem
<FilesMatch "^\.env">
    Order allow,deny
    Deny from all
</FilesMatch>

# Node.js fájlok védelem
<FilesMatch "\.(json|lock)$">
    Order allow,deny
    Deny from all
</FilesMatch>
