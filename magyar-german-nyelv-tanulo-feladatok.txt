# Magyar-German Nyelvtanuló Feladatlista

## Projekt áttekintés
A Magyar-German Nyelvtanuló egy interaktív webalkalmazás, amely seg<PERSON>t a németül tanulóknak a szókincs és nyelvi kifejezések elsajátításában. Az alkalmazás különböző tanulási módszereket kínál, be<PERSON><PERSON><PERSON><PERSON> flashcard<PERSON>t, mondatgyakor<PERSON><PERSON>t, kvízeket és interaktív játékokat.

## Technológiai stack
- React
- TypeScript
- Vite
- Tailwind CSS
- shadcn-ui komponensek
- React Router
- React Query

## Jelenlegi funkciók

### 1. <PERSON><PERSON>oldal (Home)
- Áttekintést nyújt a tanulási előrehaladásról
- Megjeleníti a napi tanulási ajánlott szót és mondatot
- Gyors hozzáférést biztosít a különböz<PERSON> tanulási modulokhoz

### 2. Szókincs tanulás (Vocabulary)
- Flashcard rendszer magyar-német szavak tanulásához
- Szókártyák megfordíthatók a fordítás megjelenítéséhez
- Szűrés kategória és nehézségi szint szerint
- Szavak felolvasása mind magyar, mind német nyelven

### 3. Mondatok tanulása (Phrases)
- Magyar-német mondatpárok gyakorlása
- Szűrés kategória és nehézségi szint szerint
- Mondatok felolvasása mind magyar, mind német nyelven

### 4. Kvíz (Quiz)
- Különböző típusú kérdések a nyelvtudás tesztelésére
  - Feleletválasztós
  - Szövegkitöltős
- Időkorlátos kérdések
- Azonnali visszajelzés a válaszokra
- Nehézségi szint szerinti szűrés

### 5. Nyelvtanulási játékok (Games)
- Különböző interaktív játékok a nyelvtanulás segítésére:
  - Szópárosítás
  - Szórend gyakorlás
  - Gyors válasz (időkorláttal)
  - Hallás utáni írás
  - Szövegkiegészítés

## Fejlesztési feladatok

### Általános fejlesztések
1. Mobilbarát felhasználói felület továbbfejlesztése
2. Teljesítményoptimalizálás (különösen a hanglejátszásnál)
3. Offline mód hozzáadása a lokális tanulás támogatásához
4. Felhasználói regisztráció és bejelentkezés implementálása
5. Felhasználói profilok és haladáskövetés létrehozása
6. Témák és színsémák testreszabásának lehetősége
7. Hiba jelentési rendszer hozzáadása

### Szókincs modul fejlesztése
1. Egyéni szólisták készítésének lehetősége
2. Nehézségi szintek finomítása és bővítése
3. Szótanulási statisztikák implementálása
4. Szóemlékezeti algoritmus bevezetése (spaced repetition)
5. Kiejtési gyakorlat és értékelés

### Mondatok modul fejlesztése
1. Nyelvtani magyarázatok hozzáadása a mondatokhoz
2. Mondatvariációk gyakorlása
3. Bővíteni a mondatok adatbázisát
4. Szituációs gyakorlatok (pl. étteremben, boltban)
5. Kulcsszavakra épülő mondatalkotási gyakorlatok

### Kvíz modul fejlesztése
1. Új kérdéstípusok hozzáadása (pl. párosítás, képfelismerés)
2. Egyedi kvízek összeállításának lehetősége
3. Nehézségi szint automatikus beállítása a teljesítmény alapján
4. Versenyszerű kvízmódok bevezetése (napi kihívások)
5. Kvízeredmények megosztási lehetősége

### Játékok modul fejlesztése
1. Memóriajáték hozzáadása (szó-kép párosítás)
2. Szójátékok fejlesztése (akasztófa, szókereső)
3. Társas tanulást elősegítő játékmódok bevezetése
4. Jutalomrendszer implementálása
5. Hangfelismerési játékok hozzáadása

### Új modulok fejlesztése
1. Nyelvtani magyarázatok és gyakorlatok modul
2. Kultúra és országismeret modul
3. Írás gyakorlása (diktálás, esszéírás)
4. Társalgási gyakorlatok (AI chatbot segítségével)
5. Hírek és olvasmányok szintezett nyelvi anyagokkal

### Adatbázis és tartalom fejlesztése
1. Több szó és kifejezés hozzáadása az adatbázishoz
2. Szavak kategorizálásának finomítása
3. Hanganyagok minőségének javítása
4. Kontextusban használt szavak és kifejezések példákkal
5. Szinonimák és ellentétek hozzáadása a szóadatbázishoz

### Backend fejlesztések
1. Adatbázis-kezelés modernizálása (Firebase vagy más felhőszolgáltatás)
2. API létrehozása a frontend és backend közötti kommunikációhoz
3. Felhasználói adatok biztonságos tárolása
4. Teljesítménystatisztikák generálása
5. Automatikus mentés és szinkronizálás különböző eszközök között

### Tesztelés és minőségbiztosítás
1. Egységtesztek írása a kritikus komponensekhez
2. Integrációs tesztek a modulok közötti interakciók tesztelésére
3. Felhasználói felület tesztelése különböző eszközökön
4. Teljesítménytesztek különböző felhasználási forgatókönyvekhez
5. A/B tesztelés a felhasználói felület optimalizálásához

### Dokumentáció és támogatás
1. Fejlesztői dokumentáció készítése
2. Felhasználói kézikönyv írása
3. Oktatóanyagok és bevezetőtúrák készítése
4. GYIK (Gyakran Ismételt Kérdések) összeállítása
5. Közösségi támogatási fórum létrehozása

## Prioritások

### Magas prioritású feladatok
1. Felhasználói regisztráció és profilkezelés
2. Haladáskövetés és statisztikák
3. Tartalombővítés (több szó, mondat, kvízkérdés)
4. Spaced repetition algoritmus implementálása
5. Offline mód támogatása

### Közepes prioritású feladatok
1. Új játékok fejlesztése
2. Nyelvtani magyarázatok modul
3. Teljesítményoptimalizálás
4. Mobilbarát felület továbbfejlesztése
5. Testreszabási lehetőségek

### Alacsony prioritású feladatok
1. Közösségi funkciók
2. Tartalom megosztási lehetőségek
3. Külső integrációk (pl. Google Translate)
4. Több nyelv támogatása
5. Gamifikációs elemek bővítése

## Hosszú távú tervek
1. iOS és Android natív alkalmazások fejlesztése
2. Tanári/osztálytermi verzió létrehozása
3. Előfizetéses prémium funkciók bevezetése
4. AI alapú személyre szabott tanulási útvonalak
5. VR/AR nyelvi környezet fejlesztése
