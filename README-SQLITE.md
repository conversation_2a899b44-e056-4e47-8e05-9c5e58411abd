# Magyar-Német Nyelvtanuló SQLite Backend

Ez a projekt a Magyar-Német Nyelvtanuló alkalmazás SQLite adatbázissal működő backend-je.

## Telepítés és beállítás

### 1. Telepítsd a függőségeket

```bash
cd backend-sqlite
npm install
```

### 2. Inicializáld az adatbázist

```bash
cd backend-sqlite
npx prisma generate
npx prisma migrate dev --name init
```

<PERSON><PERSON> létrehozza a SQLite adatbázist és a szükséges táblákat.

### 3. Indítsd el a szervert

```bash
cd backend-sqlite
node src/server.js
```

A szerver a 3000-es porton fog futni.

### 4. Indítsd el a frontend-et

Egy másik terminálban:

```bash
npm run dev
```

A frontend a 8081-es porton fog futni.

### 5. <PERSON><PERSON><PERSON> hasz<PERSON>ld az egyszerű indítóscriptet

```bash
./start-app.sh
```

Ez egyszerre indítja el a backend és frontend szervereket.

## Adatbázis kezelése

Az adatbázis kezeléséhez használhatod a Prisma Studio-t:

```bash
cd backend-sqlite
npx prisma studio
```

Ez egy webes felületet nyit meg a `http://localhost:5555` címen, ahol kezelheted az adatbázist.

## Környezeti változók

A backend-sqlite/.env fájlban találhatók a környezeti változók:

- `DATABASE_URL`: Az SQLite adatbázis elérési útja
- `JWT_SECRET`: A JWT token titkosításához használt kulcs
- `STRIPE_SECRET_KEY`: A Stripe API titkos kulcsa
- `STRIPE_WEBHOOK_SECRET`: A Stripe webhook titkos kulcsa
- `STRIPE_PRICE_ID`: A Stripe előfizetés ára azonosítója
- `STRIPE_PRODUCT_ID`: A Stripe termék azonosítója

## Miért SQLite?

Az SQLite egy könnyű, fájl-alapú adatbázis, amely nem igényel külön szervert. Ideális fejlesztéshez és kisebb alkalmazásokhoz. Előnyei:

1. Nincs szükség külön adatbázis szerverre
2. Egyszerű telepítés és karbantartás
3. Megbízható és gyors
4. Hordozható (az adatbázis egyetlen fájlban tárolódik)

## Különbségek a MongoDB-hez képest

- Az SQLite relációs adatbázis, míg a MongoDB dokumentum-orientált
- Az SQLite egyetlen fájlban tárolja az adatokat, míg a MongoDB külön szervert igényel
- Az SQLite-hoz Prisma ORM-et használunk, míg a MongoDB-hez Mongoose-t

## Hibaelhárítás

Ha problémád van a szerverrel:

1. Ellenőrizd, hogy a backend-sqlite/.env fájlban helyesen vannak-e beállítva a környezeti változók
2. Ellenőrizd, hogy a Prisma migrációk sikeresen lefutottak-e
3. Ellenőrizd a szerver logokat a hibák azonosításához
